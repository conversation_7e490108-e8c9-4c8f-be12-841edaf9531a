package com.sakidila.projectoic.service;

import com.sakidila.projectoic.entity.NivelAcademico;
import com.sakidila.projectoic.repository.NivelAcademicoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class NivelAcademicoService {

    @Autowired
    NivelAcademicoRepository repository;

    public NivelAcademico criarNivelAcademico(NivelAcademico nivelAcademico){
        return repository.save(nivelAcademico);
    }

    public List<NivelAcademico> listaNivelAcademico(){
        return repository.findAll();
    }

}
