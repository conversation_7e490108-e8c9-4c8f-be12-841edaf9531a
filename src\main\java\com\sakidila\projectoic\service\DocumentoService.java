package com.sakidila.projectoic.service;

import com.sakidila.projectoic.entity.Documento;
import com.sakidila.projectoic.entity.Projecto;
import com.sakidila.projectoic.repository.DocumentoRepository;
import org.apache.tomcat.util.http.fileupload.InvalidFileNameException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;

@Service
public class DocumentoService {

    @Autowired
    private DocumentoRepository repository;

    public Documento upload(MultipartFile file) {
            Documento documento = new Documento();
            try {
                documento.setDesignacao(file.getOriginalFilename());
                documento.setFicheiro(file.getBytes());
            } catch (IOException e) {
                e.printStackTrace();
            }
            documento.setDataCriacao(LocalDateTime.now());
            return repository.save(documento);
    }

    public ResponseEntity<ByteArrayResource> download(Long id) {
        Documento documento = findById(id);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + documento.getDesignacao());
        headers.add(HttpHeaders.CONTENT_TYPE, "application/pdf");
        headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(documento.getFicheiro().length));
        return ResponseEntity.ok()
                .headers(headers)
                .body(new ByteArrayResource(documento.getFicheiro()));
    }

    public Documento findById(Long id){
        return repository.findById(id).get();
    }
}
