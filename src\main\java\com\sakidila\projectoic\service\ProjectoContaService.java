package com.sakidila.projectoic.service;

import com.sakidila.projectoic.entity.*;
import com.sakidila.projectoic.repository.ProjectoContaRepository;
import com.sakidila.projectoic.repository.TipoProjectoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class ProjectoContaService {

    @Autowired
    private ProjectoContaRepository repository;


    public List<ProjectoConta> findAllInvestigadoresByIdProjecto(List<Projecto> projectos){
        List<ProjectoConta> projectoContas = new ArrayList<>();
        for (Projecto projecto:projectos){
            projectoContas.addAll(repository.findAllByChaveProjectoId(projecto.getId()));
        }
        return projectoContas;
    }

    public List<ProjectoConta> findAllInvestigadoresByIdProjecto(Long idProjecto){
        return repository.findAllByChaveProjectoId(idProjecto);
    }

    public List<ProjectoConta> findAllProjectosByIdInvestigador(Long idInvestigador){
        return repository.findAllByChaveContaId(idInvestigador);
    }

    public ProjectoConta findById(Long chave) {
        Optional<ProjectoConta> optional = repository.findById(chave);
        if (optional.isPresent()) {
            return optional.get();
        }
        return null;
    }

    public ProjectoConta desativar(Long idProjecto, Long idConta){
        ProjectoConta projectoConta = repository.findByIdProjectoAndIdConta(idProjecto,idConta).get();
        if(projectoConta!=null)
            repository.disabledByIdProjectoAndIdConta(idProjecto,idConta);
        return projectoConta;
    }

}
