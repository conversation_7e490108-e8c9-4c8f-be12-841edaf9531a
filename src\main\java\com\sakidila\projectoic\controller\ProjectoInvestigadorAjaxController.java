package com.sakidila.projectoic.controller;

import com.sakidila.projectoic.entity.EntidadeFaculdade;
import com.sakidila.projectoic.entity.ProjectoConta;
import com.sakidila.projectoic.service.EntidadeFaculdadeService;
import com.sakidila.projectoic.service.ProjectoContaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("Ajax/projecto/")
public class ProjectoInvestigadorAjaxController {

    @Autowired
    private ProjectoContaService service;

    @GetMapping("/{idProjecto}")
    public ResponseEntity<List<?>> encontrarParticipantePeloIdProjecto(@PathVariable Long idProjecto){
        List<ProjectoConta> lista = new ArrayList<>();
        lista = service.findAllInvestigadoresByIdProjecto(idProjecto);
        return new ResponseEntity<>(lista, HttpStatus.OK);

    }

}
