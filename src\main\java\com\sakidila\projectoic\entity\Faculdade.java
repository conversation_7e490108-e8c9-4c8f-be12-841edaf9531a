package com.sakidila.projectoic.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Entity
@Getter
@Setter
public class Faculdade{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String designacao;

    @OneToMany(mappedBy = "faculdade")
    private List<Curso> listaCursos;

    @JsonIgnore
    @OneToMany(mappedBy = "faculdade")
    private List<Centro> listaCentros;

    @Column(columnDefinition = "boolean default false")
    @Nullable
    private boolean disabled;

}
