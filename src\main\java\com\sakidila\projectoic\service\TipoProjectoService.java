package com.sakidila.projectoic.service;

import com.sakidila.projectoic.entity.Curso;
import com.sakidila.projectoic.entity.TipoProjecto;
import com.sakidila.projectoic.repository.TipoProjectoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class TipoProjectoService {

    @Autowired
    private TipoProjectoRepository repository;

        public TipoProjecto criarTipoProjecto(TipoProjecto tipoProjecto){
            return repository.save(tipoProjecto);
        }

        public List<TipoProjecto> listaTipoProjectos(){
            return repository.findAll();
        }

    public TipoProjecto findById(Long chave) {
        Optional<TipoProjecto> optional = repository.findById(chave);
        if (optional.isPresent()) {
            return optional.get();
        }
        return null;
    }
}
