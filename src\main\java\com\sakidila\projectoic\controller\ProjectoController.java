package com.sakidila.projectoic.controller;

import com.sakidila.projectoic.entity.*;
import com.sakidila.projectoic.repository.ProjectoContaRepository;
import com.sakidila.projectoic.repository.ProjectoRespository;
import com.sakidila.projectoic.repository.TipoContaRepository;
import com.sakidila.projectoic.service.*;
import com.sakidila.projectoic.utility.Nomenclatura;
import jakarta.servlet.http.HttpSession;
import org.apache.commons.io.FileUtils;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

@Controller
@RequestMapping("sakidila_ic/portal")
public class ProjectoController {

    Nomenclatura nomenclatura = new Nomenclatura();

    @Autowired
    private ContaService contaService;

    @Autowired
    private ProjectoServico servico;

    @Autowired
    private EntidadeFaculdadeService entidadeFaculdadeService;

    @Autowired
    private EntidadeServico entidadeServico;

    @Autowired
    private FaculdadeService faculdadeService;

    @Autowired
    private CentroServico centroServico;

    @Autowired
    private TipoProjectoService tipoProjectoService;

    @Autowired
    private MetodologiaService metodologiaService;

    @Autowired
    private TipoContaRepository tipoContaRepository;

    @Autowired
    private FicheiroServicoImpl ficheiroServico;

    @Autowired
    private ProjectoRespository respository;

    @Autowired
    private ProjectoContaRepository projectoContaRepository;

    @Autowired
    private ProjectoContaService projectoContaService;

    @Autowired
    private InvestidorProjectoService investidorProjectoService;

    @Autowired
    private DocumentoService documentoService;

    @Autowired
    private CursoServico cursoServico;

    @Autowired
    private LocalidadeServico localidadeServico;

    @Autowired
    private EstadoService estadoService;

    @Autowired
    private SexoService sexoService;

    @Autowired
    private NivelAcademicoService nivelAcademicoService;


    @GetMapping("/projectos")
    public String projecto(){
        return "projectos";
    }

    @GetMapping("/listagem/{pagina}")
    public String listagem(Model model, @RequestParam(value = "instituto",required = false,defaultValue = "0") Long idInstituto,
                           @RequestParam(value = "ordem",required = false,defaultValue = "1") Long ordem,
                           @RequestParam(value = "investimento",required = false,defaultValue = "0") Long investimento,
                           @PathVariable(required = false) int pagina){

        int tamanho =10;
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        System.out.println("dados :"+authentication.getName());
        if (authentication.isAuthenticated() && !authentication.getName().equals("anonymousUser")) {
            Conta conta = contaService.mostrarDadoConta(authentication.getName());
            List<EntidadeFaculdade> lista = entidadeFaculdadeService.ListaEntidadeFaculdadePelaInstituto(conta.getEntidade() != null ? conta.getEntidade().getId() : null);
            List<Faculdade> faculdades = faculdadeService.encontrarTodasFaculdadePeloInstituto(lista);
            List<Centro> listaCentros = centroServico.encontrarTodosCentrosPelaIdInstituicao(faculdades,conta);
            List<Projecto> userProjectosInvestidos = null;
            if(conta.getTipoConta().getId() == nomenclatura.getEMPRESA()) {
                userProjectosInvestidos = investidorProjectoService.encontrarPeloIdInvestidor(conta.getId());
            }
            model.addAttribute("investimentos",userProjectosInvestidos);
            model.addAttribute("conta", authentication.getName());
            model.addAttribute("userDefault", "anonymousUser");
            model.addAttribute("dadoConta", conta);
            model.addAttribute("institutos",entidadeServico.listaInstitutos());
            model.addAttribute("investigadorPerfil", "ROLE_INVESTIGATOR");
            model.addAttribute("areas",cursoServico.listarTodos());
            model.addAttribute("localidades",localidadeServico.listaLocalidadesPrimitiva());
            model.addAttribute("niveis",nivelAcademicoService.listaNivelAcademico());
            model.addAttribute("sexos",sexoService.listaSexos());
            model.addAttribute("estados",estadoService.listaEstadoCivil());
            model.addAttribute("faculdades",faculdades);
            model.addAttribute("adminPerfil","ROLE_ADMIN");
            model.addAttribute("gestorProjectoPerfil", "ROLE_GESTOR_PROJECTO");
            model.addAttribute("gestorCentroPerfil", "ROLE_RESPONSAVEL_CENTRO");
            model.addAttribute("gestorInstituicaoPerfil","ROLE_GESTOR_INSTITUICAO");
            model.addAttribute("estudantePerfil", "ROLE_ESTUDANTE");
            model.addAttribute("investigadorIndependentePerfil", "ROLE_INVESTIGADOR_INDEPENDENTE");
            model.addAttribute("gestorPerfil", "ROLE_GESTOR");
            model.addAttribute("empresaPerfil", "ROLE_EMPRESA");
            model.addAttribute("centros",listaCentros);
            model.addAttribute("dadoConta", conta);
            Page<Projecto> paginaNova = null;
            if(idInstituto == null && ordem == null && investimento == null){
                Long chave = Long.valueOf(0);
                paginaNova = servico.encontrarPaginaPeloIdInstituto(Long.valueOf(idInstituto),Long.valueOf(ordem),Long.valueOf(investimento),pagina,tamanho);
            }
            else
                paginaNova = servico.encontrarPaginaPeloIdInstituto(idInstituto,ordem,investimento,pagina,tamanho);

            List<Projecto> projectosLista = paginaNova.getContent();
            model.addAttribute("paginaActual",pagina);
            model.addAttribute("totalPaginas",paginaNova.getTotalPages());
            model.addAttribute("totalItems",paginaNova.getTotalElements());
            model.addAttribute("projectos",projectosLista);
            return "listagem_projectos";
        }else{
            SecurityContextHolder.clearContext();
            model.addAttribute("conta",null);
            model.addAttribute("dadoConta",null);
            return "login";
        }

    }

    @GetMapping("/projecto/registrar/{pagina}")
    public String Registrar(Model model, @PathVariable(required = false) int pagina){
        int tamanho=8;
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        System.out.println("dados :"+authentication.getName());
        if (authentication.isAuthenticated() && !authentication.getName().equals("anonymousUser")) {
            Conta conta = contaService.mostrarDadoConta(authentication.getName());
            List<InvestidorProjecto> listaInvestProjectos = new ArrayList<>();
                List<EntidadeFaculdade> lista = entidadeFaculdadeService.ListaEntidadeFaculdadePelaInstituto(conta.getEntidade() != null ? conta.getEntidade().getId() : null);
                List<Faculdade> faculdades = faculdadeService.encontrarTodasFaculdadePeloInstituto(lista);
                List<Centro> listaCentros = centroServico.encontrarTodosCentrosPelaIdInstituicao(faculdades,conta);
                List<Conta> investigadores = contaService.encontrarTodasContas(listaCentros,conta);
                List<Projecto> projectos = new ArrayList<>();
            Page<Projecto> paginaNova = null;
            if(conta.getTipoConta().getId() == nomenclatura.getGESTOR_INSTITUTO()) {
                projectos = servico.encontrarTodosProjectosPeloIdInstituicao(conta.getEntidade().getId(), conta);
                paginaNova = (Page<Projecto>) servico.convertListToPage(pagina,tamanho,projectos);
            }
            else if(conta.getTipoConta().getId() == nomenclatura.getGESTOR_CENTRO()) {
                    Centro centro = centroServico.findCentroBIdyResponsavel(conta.getId());
                    conta.setCentro(centro);
                System.out.println("Teste conta do responsavel: "+ centro.getConta().getPessoa().getNome());
                projectos = servico.encontrarProjectosPeloIdCentro(centro.getId());
                paginaNova = (Page<Projecto>) servico.convertListToPage(pagina,tamanho,projectos);
            }
            else if(conta.getTipoConta().getId() == nomenclatura.getESTUDANTE()
                    || conta.getTipoConta().getId() == nomenclatura.getGESTOR_PROJECTO()
                    || conta.getTipoConta().getId() == nomenclatura.getINVESTIGADOR()
                    || conta.getTipoConta().getId() == nomenclatura.getINVESTIGADOR_INDEPENDENTE()) {
                projectos = servico.encontrarProjectosPeloGestor(conta.getId());
                List<ProjectoConta> projectoContas = projectoContaService.findAllProjectosByIdInvestigador(conta.getId());
                projectos.addAll(servico.EncontrarProjectoPeloIdParticipante(conta.getId(), projectoContas));
                paginaNova = (Page<Projecto>) servico.convertListToPage(pagina,tamanho,projectos);
            }
            else if(conta.getTipoConta().getId() == nomenclatura.getEMPRESA()){
                listaInvestProjectos = investidorProjectoService.encontrarProjectosPeloIdInvestidor(conta.getId());
                paginaNova = (Page<Projecto>) servico.convertListToPage(pagina,tamanho,listaInvestProjectos);
            }

            List<Projecto> projectosLista = paginaNova.getContent();
            System.out.println("Teste teste:"+paginaNova);
            model.addAttribute("paginaActual",pagina);
            model.addAttribute("totalPaginas",paginaNova.getTotalPages());
            model.addAttribute("totalItems",paginaNova.getTotalElements());
            model.addAttribute("conta", authentication.getName());
            model.addAttribute("centros",listaCentros);
            model.addAttribute("tipoProjectos",tipoProjectoService.listaTipoProjectos());
            model.addAttribute("investigadores", investigadores);
            model.addAttribute("metodologias",metodologiaService.listaMetodologias());
            model.addAttribute("dadoConta", conta);
            model.addAttribute("gestorPerfil", "ROLE_GESTOR");
            model.addAttribute("dadoConta", conta);
            model.addAttribute("projectos",paginaNova);
            model.addAttribute("projectosInvestidos",paginaNova);
            model.addAttribute("gestorInstituicaoPerfil", "ROLE_GESTOR_INSTITUICAO");
            model.addAttribute("empresaPerfil", "ROLE_EMPRESA");
            model.addAttribute("gestorCentroPerfil", "ROLE_RESPONSAVEL_CENTRO");
            model.addAttribute("gestorProjectoPerfil", "ROLE_GESTOR_PROJECTO");
            model.addAttribute("investigadorPerfil", "ROLE_INVESTIGATOR");
            model.addAttribute("investigadorIndependentePerfil","ROLE_INVESTIGADOR_INDEPENDENTE");
            model.addAttribute("estudantePerfil", "ROLE_ESTUDANTE");
            model.addAttribute("adminPerfil", "ROLE_ADMIN");
            System.out.println("dados :"+authentication.getName() + " tipo:"+conta.getTipoConta().getDesignacao());
            System.out.println("dados :"+authentication.getName());
            return "projecto_registrar";
        }else{
            SecurityContextHolder.clearContext();
            model.addAttribute("conta",null);
            model.addAttribute("dadoConta",null);
            return "projecto_registrar";
        }


    }

    @GetMapping("/projecto/aprovar/{id}")
    public String aprovar(@PathVariable Long id)
    {
        Projecto projecto = respository.findById(id).get();
        projecto.setEhAprovado(true);
        servico.salvarProjecto(projecto);
        return "redirect:/sakidila_ic/portal/projecto/pendentes/1";
    }

    @GetMapping("/projecto/pendentes/{pagina}")
    public String pendentes(Model model, @PathVariable(required = false) int pagina){
        int tamanho = 10;
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        System.out.println("dados :"+authentication.getName());
        if (authentication.isAuthenticated() && !authentication.getName().equals("anonymousUser")) {
            Conta conta = contaService.mostrarDadoConta(authentication.getName());
            Page<Projecto> paginaNova = null;
            List<Projecto> projectos = servico.encontrarProjectosEstudantesPelaInstituicao(conta.getEntidade().getId());
            paginaNova = (Page<Projecto>) servico.convertListToPage(pagina,tamanho,projectos);
            List<Projecto> projectosLista = paginaNova.getContent();
            model.addAttribute("paginaActual",pagina);
            model.addAttribute("totalPaginas",paginaNova.getTotalPages());
            model.addAttribute("totalItems",paginaNova.getTotalElements());
            model.addAttribute("projectos",projectosLista);
            model.addAttribute("dadoConta", conta);
            model.addAttribute("gestorPerfil", "ROLE_GESTOR");
            model.addAttribute("dadoConta", conta);
            model.addAttribute("gestorInstituicaoPerfil", "ROLE_GESTOR_INSTITUICAO");
            model.addAttribute("gestorCentroPerfil", "ROLE_RESPONSAVEL_CENTRO");
            model.addAttribute("gestorProjectoPerfil", "ROLE_GESTOR_PROJECTO");
            model.addAttribute("investigadorPerfil", "ROLE_INVESTIGATOR");
            model.addAttribute("estudantePerfil", "ROLE_ESTUDANTE");
            model.addAttribute("adminPerfil", "ROLE_ADMIN");

            return "projecto_aprovacao";
        }else{
            SecurityContextHolder.clearContext();
            model.addAttribute("conta",null);
            model.addAttribute("dadoConta",null);
            return "gestor_pagina_principal";
        }

    }

    @GetMapping("/projecto/investigador/associar/{pagina}")
    public String associar(Model model,  @PathVariable(required = false) int pagina){
        int tamanho = 10;
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        System.out.println("dados :"+authentication.getName());
        if (authentication.isAuthenticated() && !authentication.getName().equals("anonymousUser")) {
            Conta conta = contaService.mostrarDadoConta(authentication.getName());
            List<Conta> investigadores = conta.getCentro().getMembros();
            List<Projecto> projectos = conta.getCentro().getProjectos();
            List<ProjectoConta> projectoContas = projectoContaService.findAllInvestigadoresByIdProjecto(conta.getCentro().getProjectos());
            Page<ProjectoConta> paginaNova = null;
            paginaNova = (Page<ProjectoConta>) servico.convertListToPage(pagina,tamanho,projectoContas);
            List<ProjectoConta> projectosContasLista = paginaNova.getContent();
            model.addAttribute("paginaActual",pagina);
            model.addAttribute("totalPaginas",paginaNova.getTotalPages());
            model.addAttribute("totalItems",paginaNova.getTotalElements());
            model.addAttribute("conta", authentication.getName());
            model.addAttribute("userDefault", "anonymousUser");
            model.addAttribute("dadoConta", conta);
            model.addAttribute("admin_role","ROLE_ADMIN");
            model.addAttribute("gestorInstituicaoPerfil", "ROLE_GESTOR_INSTITUICAO");
            model.addAttribute("gestorCentroPerfil", "ROLE_RESPONSAVEL_CENTRO");
            model.addAttribute("gestorProjectoPerfil", "ROLE_GESTOR_PROJECTO");
            model.addAttribute("investigadorPerfil", "ROLE_INVESTIGATOR");
            model.addAttribute("adminPerfil", "ROLE_ADMIN");
            model.addAttribute("investigadores", investigadores);
            model.addAttribute("projectos", projectos);
            model.addAttribute("membros", projectosContasLista);

            return "projecto_investigador_registrar";
        }else{
            SecurityContextHolder.clearContext();
            model.addAttribute("conta",null);
            model.addAttribute("dadoConta",null);
            return "login";
        }

    }

    @PostMapping("/investigador/associar")
    public String salvarInvestigadorCentro(@ModelAttribute Conta conta,@RequestParam Long idProjecto, HttpSession session){
        conta = contaService.findById(conta.getId());
        System.out.println("Centro ID: "+idProjecto);
        Projecto projecto = respository.findById(idProjecto).get();
        ProjectoConta projectoConta = new ProjectoConta();
        projectoConta.setChave(new ProjectoContaId());
        projectoConta.getChave().setProjecto(projecto);
        projectoConta.getChave().setConta(conta);
        projectoContaRepository.save(projectoConta);
        if(conta != null)
            session.setAttribute("msg", "Usuario " + conta.getUsername() + " criado com sucesso!");
        else
            session.setAttribute("msg", "O E-mail " + conta.getUsername() + " já existe no sistema");
        return "redirect:/sakidila_ic/portal/projecto/investigador/associar/1";
    }

    @PostMapping("/projecto/salvar")
    public String salvarProjecto(@ModelAttribute Projecto projecto,
                                 HttpSession session,
                                 @RequestParam(name = "data") String dataCriacao,
                                 @RequestParam(name = "imagem") MultipartFile multipartFile,
                                 @RequestParam(name = "ficheiro") MultipartFile file){
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Conta conta = contaService.mostrarDadoConta(authentication.getName());
        String imagem = ficheiroServico.uploadImage(multipartFile);
        if(imagem != null) {
            projecto.setLogotipo(imagem);
        }
        else
        {

            if(projecto.getId() != null) {
                Projecto aux = servico.findById(projecto.getId());
                if(aux.getLogotipo() != null || aux.getLogotipo() != "")
                    projecto.setLogotipo(aux.getLogotipo());
            }
        }
        projecto.setTipoProjecto(tipoProjectoService.findById(projecto.getTipoProjecto().getId()));
        projecto.setMetodologia(metodologiaService.findById(projecto.getMetodologia().getId()));
        if(dataCriacao != null || dataCriacao == "") {

            try {
                System.out.println("Data string: "+dataCriacao);
                DateFormat formatter = new SimpleDateFormat("YYYY-MM-DD");
                projecto.setDataCriacao(formatter.parse(dataCriacao));
                System.out.println("Teste:"+projecto.getOrcamento());
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        if(projecto.getOrcamento() != 0 || projecto.getOrcamento() != 0.0){
            Locale locale = new Locale("pt", "AO");
            NumberFormat numberFormat = NumberFormat.getCurrencyInstance(locale);
            String preco = numberFormat.format(projecto.getOrcamento());
            projecto.setOrcamentoTexto(preco);
        }
        else
            projecto.setOrcamentoTexto("Não definido");
        if(conta.getTipoConta().getId() == nomenclatura.getGESTOR_PROJECTO() || conta.getTipoConta().getId() == nomenclatura.getGESTOR_CENTRO()) {
            projecto.setGestor(contaService.findById(projecto.getGestor().getId()));
            projecto.getGestor().setTipoConta(tipoContaRepository.findById(nomenclatura.getGESTOR_PROJECTO()).get());
            projecto.setCentro(centroServico.findById(projecto.getCentro().getId()));
            projecto.setEhAprovado(true);
        }
        else
        {
            projecto.setGestor(conta);
            if(conta.getTipoConta().getId() == nomenclatura.getINVESTIGADOR()
            || conta.getTipoConta().getId() == nomenclatura.getINVESTIGADOR_INDEPENDENTE())
                projecto.setEhAprovado(true);
            else
                projecto.setEhAprovado(false);
        }
        projecto.setDocumento(documentoService.upload(file));
        projecto.setCreatedAt(LocalDateTime.now());
        projecto = servico.salvarProjecto(projecto);

        if(projecto != null)
            session.setAttribute("msg", "Usuario " + projecto.getDesignacao() + " criado com sucesso!");
        else
            session.setAttribute("msg", "O E-mail " + projecto.getDesignacao() + " já existe no sistema");
        return "redirect:/sakidila_ic/portal/listagem/1";
    }

    @GetMapping("/desativar/{idProjecto}/{idConta}")
    public String disabledByIdCentro(@PathVariable Long idProjecto,@PathVariable Long idConta){
        projectoContaService.desativar(idProjecto,idConta);
        return "redirect:/sakidila_ic/portal/projecto/registrar/1";

    }

    @GetMapping("/desativar/{idProjecto}")
    public String disabledByIdProjecto(@PathVariable Long idProjecto){
        servico.desativar(idProjecto);
        return "redirect:/sakidila_ic/portal/projecto/registrar/1";

    }
}
