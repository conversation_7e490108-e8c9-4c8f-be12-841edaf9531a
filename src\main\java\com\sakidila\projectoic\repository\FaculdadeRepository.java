package com.sakidila.projectoic.repository;

import com.sakidila.projectoic.entity.Faculdade;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Transactional
public interface FaculdadeRepository extends JpaRepository<Faculdade,Long> {


    @Modifying
    @Query("update Faculdade f set f.disabled = true where f.id =:idCentro")
    void disabledByIdFaculdade(Long id);
}
