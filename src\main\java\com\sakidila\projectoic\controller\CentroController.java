package com.sakidila.projectoic.controller;

import com.sakidila.projectoic.entity.*;
import com.sakidila.projectoic.repository.ContaRepository;
import com.sakidila.projectoic.repository.TipoContaRepository;
import com.sakidila.projectoic.service.*;
import com.sakidila.projectoic.utility.Nomenclatura;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

@Controller
@RequestMapping("sakidila_ic/centro")
public class CentroController {

    Nomenclatura nomenclatura = new Nomenclatura();

    @Autowired
    private ContaService contaService;
    
    @Autowired
    private CentroServico centroServico;

    @Autowired
    private FicheiroServicoImpl ficheiroServico;

    @Autowired
    private EntidadeFaculdadeService entidadeFaculdadeService;

    @Autowired
    private FaculdadeService faculdadeService;

    @Autowired
    private CursoServico cursoServico;

    @Autowired
    private TipoContaRepository tipoContaRepository;

    @Autowired
    ContaRepository repository;

    @GetMapping("/registrar/{pagina}")
    public String Registrar(Model model, @PathVariable(required = false) int pagina) {
        int tamanho = 10;
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        System.out.println("dados :"+authentication.getName());
        if (authentication.isAuthenticated() && !authentication.getName().equals("anonymousUser")) {
            Conta conta = contaService.mostrarDadoConta(authentication.getName());
            List<EntidadeFaculdade> lista = entidadeFaculdadeService.ListaEntidadeFaculdadePelaInstituto(conta.getEntidade().getId());
            List<Faculdade> faculdades = faculdadeService.encontrarTodasFaculdadePeloInstituto(lista);
            List<Centro> listaCentros = centroServico.encontrarTodosCentrosPelaIdInstituicao(faculdades,conta);
            Page<Centro> paginaNova = (Page<Centro>) centroServico.convertListToPage(pagina,tamanho,listaCentros);
            model.addAttribute("conta", authentication.getName());
            model.addAttribute("userDefault", "anonymousUser");
            model.addAttribute("dadoConta", conta);
            model.addAttribute("gestorInstituicaoPerfil", "ROLE_GESTOR_INSTITUICAO");
            model.addAttribute("gestorCentroPerfil", "ROLE_RESPONSAVEL_CENTRO");
            model.addAttribute("gestorProjectoPerfil", "ROLE_GESTOR_PROJECTO");
            model.addAttribute("investigadorPerfil", "ROLE_INVESTIGATOR");
            model.addAttribute("adminPerfil", "ROLE_ADMIN");
            model.addAttribute("faculdades",lista);
            model.addAttribute("cursos",cursoServico.listarTodos());
            model.addAttribute("investigadores",contaService.listarInvestigadorPeloIdInstituicaoIdTipoConta(conta.getEntidade().getId(), nomenclatura.getINVESTIGADOR()));
            List<Centro> centrosLista = paginaNova.getContent();
            model.addAttribute("paginaActual",pagina);
            model.addAttribute("totalPaginas",paginaNova.getTotalPages());
            model.addAttribute("totalItems",paginaNova.getTotalElements());
            model.addAttribute("centros",centrosLista);
            return "centro_registrar";
        }else{
            SecurityContextHolder.clearContext();
            model.addAttribute("conta",null);
            model.addAttribute("dadoConta",null);
            return "login";
        }
    }

    @PostMapping("/salvar")
    public String salvarCentro(@ModelAttribute Centro centro, HttpSession session, @RequestParam(name = "imagem") MultipartFile multipartFile){
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Conta conta = contaService.mostrarDadoConta(authentication.getName());
        String imagem = ficheiroServico.uploadImage(multipartFile);
        if(imagem != null) {
            centro.setLogotipo(imagem);
        }
        else
        {

            if(centro.getId() != null) {
                Centro aux = centroServico.findById(centro.getId());
                if(aux.getLogotipo() != null || aux.getLogotipo() != "")
                    centro.setLogotipo(aux.getLogotipo());
            }
        }
        centro.setConta(contaService.findById(centro.getConta().getId()));
        centro.getConta().setTipoConta(tipoContaRepository.findById(nomenclatura.getGESTOR_CENTRO()).get());
        centro = centroServico.salvar(centro);
        centro.getConta().setCentro(centro);
        centro.getConta().setFaculdade(centro.getFaculdade());
        contaService.criarConta(centro.getConta(), nomenclatura.getGESTOR_CENTRO());
        if(centro != null)
            session.setAttribute("msg", "Usuario " + centro.getDesignacao() + " criado com sucesso!");
        else
            session.setAttribute("msg", "O E-mail " + centro.getDesignacao() + " já existe no sistema");
        return "redirect:/sakidila_ic/centro/registrar/1";
    }


    @GetMapping("/investigador/associar/{pagina}")
    public String associar(Model model, @PathVariable(required = false) int pagina){
        int tamanho = 10;
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        System.out.println("dados :"+authentication.getName());
        if (authentication.isAuthenticated() && !authentication.getName().equals("anonymousUser")) {
            Conta conta = contaService.mostrarDadoConta(authentication.getName());
            List<EntidadeFaculdade> lista = entidadeFaculdadeService.ListaEntidadeFaculdadePelaInstituto(conta.getEntidade().getId());
            List<Faculdade> faculdades = faculdadeService.encontrarTodasFaculdadePeloInstituto(lista);
            List<Centro> listaCentros = centroServico.encontrarTodosCentrosPelaIdInstituicao(faculdades,conta);
            List<Conta> investigadores = contaService.encontrarTodasContas(listaCentros,conta);
            List<Conta> investigadoresEntidade = new ArrayList<>();
            if(conta.getEntidade() != null)
                investigadoresEntidade = contaService.findAllContasByIdEntidade(conta.getEntidade().getId());
            if (conta.getTipoConta().getId() == nomenclatura.getGESTOR_CENTRO()) {
                Centro centro = centroServico.findCentroBIdyResponsavel(conta.getId());
                investigadores.removeAll(investigadores);
                listaCentros.removeAll(listaCentros);
                listaCentros.add(centro);
                investigadores.addAll(contaService.encontrarTodasContas(listaCentros,conta));
                conta.setCentro(centro);
            }
            Page<Conta> paginaNova = (Page<Conta>) centroServico.convertListToPage(pagina,tamanho,investigadores);
            model.addAttribute("conta", authentication.getName());
            model.addAttribute("userDefault", "anonymousUser");
            model.addAttribute("dadoConta", conta);
            model.addAttribute("admin_role","ROLE_ADMIN");
            model.addAttribute("gestorInstituicaoPerfil", "ROLE_GESTOR_INSTITUICAO");
            model.addAttribute("gestorCentroPerfil", "ROLE_RESPONSAVEL_CENTRO");
            model.addAttribute("gestorProjectoPerfil", "ROLE_GESTOR_PROJECTO");
            model.addAttribute("investigadorPerfil", "ROLE_INVESTIGATOR");
            model.addAttribute("estudantePerfil", "ROLE_ESTUDANTE");
            model.addAttribute("adminPerfil", "ROLE_ADMIN");
            model.addAttribute("faculdades",lista);
            model.addAttribute("cursos",cursoServico.listarTodos());
            model.addAttribute("investigadores",/*contaService.listarInvestigadorPeloIdInstituicaoIdTipoConta(conta.getEntidade().getId(),nomenclatura.getINVESTIGADOR())*/investigadoresEntidade);
            model.addAttribute("centros",listaCentros);
            model.addAttribute("contas",paginaNova.getContent());
            model.addAttribute("paginaActual",pagina);
            model.addAttribute("totalPaginas",paginaNova.getTotalPages());
            model.addAttribute("totalItems",paginaNova.getTotalElements());
            return "centro_investigador_registrar";
        }else{
            SecurityContextHolder.clearContext();
            model.addAttribute("conta",null);
            model.addAttribute("dadoConta",null);
            return "login";
        }

    }

    @PostMapping("/investigador/registrar")
    public String salvarInvestigadorCentro(@ModelAttribute Conta conta,@RequestParam Long idCentro, HttpSession session){
        conta = contaService.findById(conta.getId());
        System.out.println("Centro ID: "+idCentro);
        conta.setCentro(centroServico.findById(idCentro));
        conta = repository.save(conta);
       if(conta != null)
            session.setAttribute("msg", "Usuario " + conta.getUsername() + " criado com sucesso!");
        else
            session.setAttribute("msg", "O E-mail " + conta.getUsername() + " já existe no sistema");
        return "redirect:/sakidila_ic/centro/investigador/associar/1";
    }

    @GetMapping("/desativar/{id}")
    public String disabledByIdCentro(@PathVariable Long id){
        centroServico.desativar(id);
        return "redirect:/sakidila_ic/centro/registrar/1";

    }

    @GetMapping("/delete/{idCentro}/{idConta}")
    public String deletar(@PathVariable Long idCentro, @PathVariable Long idConta){
        contaService.deletarCentro(idCentro,idConta);
        return "redirect:/sakidila_ic/centro/investigador/associar/1";

    }
}
