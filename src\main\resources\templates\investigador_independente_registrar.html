<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">

    <!-- Importação do Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
            crossorigin="anonymous"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style th:inline="text">
        body{
            font-family: Poppins;
        }
    </style>
    <title>Registra-se na Sakidila!</title>
</head>
<body style="background-color: ghostwhite">

<nav class="navbar navbar-expand-lg p-0 sticky-top" style="margin: 0%;">
    <button class="navbar-toggler ms-2" type="button" data-bs-toggle="collapse" data-bs-target="#navbarTogglerDemo01" aria-controls="navbarTogglerDemo01" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarTogglerDemo01">
        <div class=" d-flex flex-column bg-white shadow-sm" style="width: 100%">
            <div class="container-fluid">
                <div class="container d-flex flex-row align-items-center">
                    <a class="navbar-brand" href="/sakidila_ic/portal/">
                        <img src="/img/logotipo.jpg" alt="Bootstrap" width="120">
                    </a>

                    <div class="container d-flex flex-row justify-content-end align-items-center">
                        <a href="/sakidila_ic/usuario/principal" class="nav-link">Registrar</a>
                        <div class="dropdown btn">
                            <a href="#" class="d-block link-body-emphasis text-decoration-none dropdown dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <img src="/img/user.png" alt="mdo" width="32" height="32" class="rounded-circle">
                            </a>
                            <ul class="dropdown-menu text-small">
                                <li><a class="dropdown-item" href="/sakidila_ic/usuario/login">Entrar</a></li>
                                <!--<li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#">Sair</a></li>-->
                            </ul>
                        </div>
                        <i class="fs-5 bi bi-facebook me-2" style="color: #7c6aa2"></i>
                        <i class="fs-5 bi bi-instagram me-2" style="color: #7c6aa2"></i>
                        <i class="fs-5 bi bi-whatsapp" style="color: #7c6aa2"></i>
                    </div>
                </div>

            </div>
            <div class="container-fluid py-1" style="background-color: #AB81A3">
                <ul class="container navbar-nav d-flex justify-content-end ">
                    <li class="nav-item">
                        <a class="nav-link text-white active px-3" aria-current="page" href="/sakidila_ic/portal/">Portal</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link text-white  px-3" data-bs-toggle="dropdown" aria-expanded="false" href="#">Instituições</a>
                        <ul class="dropdown-menu rounded-top-0 rounded-bottom-4 border-top-0 text-white" style="background-color: #AB81A3">
                            <li><a class="dropdown-item text-white" href="#"><i class="fst-normal fw-bold">UCAN </i>Universidade Católica de Angola</a></li>
                            <li><a class="dropdown-item text-white" href="#"><i class="fst-normal fw-bold">UAN </i>Universidade Agostinho Neto</a></li>
                            <li><a class="dropdown-item text-white" href="#"><i class="fst-normal fw-bold">UMA </i>Universidade Metódista de Angola</a></li>
                            <li><a class="dropdown-item text-white" href="#"><i class="fst-normal fw-bold">UTANGA </i>Universidade Técnica de Angola</a></li>
                            <li><a class="dropdown-item text-white" href="#"><i class="fst-normal fw-bold">ISPITEC </i>Instituto Politecnico de Inovação Tecnologica</a></li>
                            <li><a class="dropdown-item text-white" href="#">Ver mais...</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white px-3 active" href="/sakidila_ic/portal/listagem/1">Projectos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white px-3" href="#">Sobre</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white ps-3 me-3" href="#">Contacte-nos</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</nav>                            <!--<li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#">Sair</a></li>-->

<div class="container mt-5">
    <h5 class="display-6 mt-5 mb-1 fw-bold" style="color: #AB81A3">Criar Conta <i class="fw-bold text-dark">Investigador!</i></h5>
    <hr>
    <form method="post" name="myForm" th:action="@{/sakidila_ic/usuario/investigador_independente/registrar/salvar}" enctype="multipart/form-data" onsubmit="return validateForm()" required>
        <fieldset class="bg-white px-5 py-4 shadow-sm rounded-4 mb-3">
            <legend>Dados Pessoais</legend>
            <div class="row mb-3">
                <div class="col">
                    <div class="">
                        <label class="form-label" id="nome">Nome</label>
                        <input type="text"  class="form-control" placeholder="" id="nomeValidate" onfocusout="validNome()" name="pessoa.nome" aria-label="Nome" aria-describedby="Nome">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher o campo com o seu nome.
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" id="apelido">Apelido</label>
                        <input type="text" class="form-control" id="apelidoValidate" onfocusout="validApelido()" name="pessoa.apelido" placeholder="" aria-label="Apelido" aria-describedby="apelido">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher o campo com o seu ultimo nome.
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label">Adicione sua foto de perfil</label>
                        <input class="form-control" type="file" id="foto" name="imagem">
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col">
                    <div class="">
                        <label class="form-label" id="dataNascimento">Data de Nascimento</label>
                        <input type="date" class="form-control" id="Nascimentovalidate" onfocusout="validDataNascimento()" name="pessoa.dataNascimento" placeholder="" aria-label="dataNascimento" aria-describedby="dataNascimento">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Especifique a sua data de nascimento
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label">Sexo</label>
                        <select class="form-select"  aria-label="Sexo" id="sexo" name="pessoa.sexo.id" aria-describedby="sexo">
                            <option th:each="sexo : ${sexos}" th:value="${sexo.id}"
                                    th:text="${sexo.designacao}"></option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" id="estadoCivil.id">Estado Civil</label>
                        <select class="form-select" placeholder="" name="pessoa.estadoCivil.id" aria-label="estadoCivil" id="estado" aria-describedby="estadoCivil">
                            <option th:each="estado : ${estados}" th:value="${estado.id}"
                                    th:text="${estado.designacao}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col">
                    <div class="">
                        <label class="form-label" id="identidade">Nº Identidade</label>
                        <input type="text" class="form-control" placeholder="" id="biValidate" onfocusout="validBi()" name="pessoa.bi" aria-label="identidade" aria-describedby="identidade">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher o campo com o número de identidade.
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" id="dataEmissao">Data de Emissão</label>
                        <input type="date" class="form-control"  aria-label="dataEmissao" id="emissaoValidate" onfocusout="validDataEmissao()" name="pessoa.dataEmissao" aria-describedby="dataEmissao">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Especifique a data de emissão.
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" id="dataValidade">Data de Validade</label>
                        <input type="date" class="form-control" placeholder="" id="validadeValidate" onfocusout="validDataValidade()" name="pessoa.dataValidade" aria-label="dataValidade" aria-describedby="dataValidade">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Especifique a data de validade.
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col">
                    <div class="">
                        <label class="form-label">Provincia</label>
                        <select class="form-select" id="provincia" placeholder="" aria-label="provincia" aria-describedby="provincia">
                            <option th:each="localidade : ${localidades}" th:value="${localidade.id}"
                                    th:text="${localidade.designacao}"></option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label">Municipio</label>
                        <select class="form-select"  aria-label="municipio"  id="municipio" aria-describedby="municipio"></select>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" >Bairro</label>
                        <select class="form-select" id="bairro" name="pessoa.localidade.id" placeholder="" aria-label="bairro" aria-describedby="bairro"></select>
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col">
                    <div class="">
                        <label class="form-label" id="tel">Contacto telefónico</label>
                        <input type="text" class="form-control" placeholder="" id="contactoValidate" onfocusout="validContacto()" name="pessoa.contacto" aria-label="tel" aria-describedby="tel">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher campo com o contacto telefônico.
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" id="email">Correio Electrônico</label>
                        <input type="email" class="form-control" id="emailValidate" onfocusout="validEmail()" name="pessoa.email"  aria-label="email" aria-describedby="email">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher campo com o email.
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" id="rua">Endereço</label>
                        <input type="text" class="form-control" placeholder="" id="enderecoValidate" onfocusout="validEndereco()" name="pessoa.endereco" aria-label="rua" aria-describedby="rua">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Especifique a rua, nº da casa.
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="bg-white px-5 py-4 shadow-sm rounded-4 mb-3">
            <legend>Dados Acadêmicos</legend>
            <div class="row mb-3">
                <div class="col">
                    <div class="">
                        <label class="form-label" id="nivelAcademico">Nível Acadêmico</label>
                        <select class="form-select" placeholder="" aria-label="nivelAcademico" name="nivelAcademico.id" aria-describedby="nivelAcademico">
                            <option th:each="nivel : ${niveis}" th:value="${nivel.id}"
                                    th:text="${nivel.designacao}"></option>
                        </select>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="bg-white px-5 py-4 shadow-sm rounded-4 mb-3">
            <legend>Dados da Conta</legend>
            <div class="row mb-3">
                <div class="col-4">
                    <div class="">
                        <label class="form-label" id="usuario">Username</label>
                        <input type="text" class="form-control" placeholder="" name="username" onfocusout="validUsuario()" id="usuarioValidate" aria-label="usuario" aria-describedby="usuario">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher campo com o nome de usuario.
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="">
                        <label class="form-label" id="emailConta">E-mail</label>
                        <input type="email" class="form-control" placeholder="" id="emailLoginValidate" onfocusout="validEmailLogin()" name="email" aria-label="emailConta" aria-describedby="emailConta">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher campo com o email de login.
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="">
                        <label class="form-label" id="senha">Senha</label>
                        <input type="password" class="form-control" placeholder="" id="senhaValidate" onfocusout="validSenha()" name="senha" aria-label="senha" aria-describedby="senha">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher campo com a nova senha da conta.
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <!--<div class="col-4">
                    <div class="input-group ">
                        <span class="input-group-text" id="tipoConta">Tipo de Conta</span>
                        <select class="form-select" placeholder="" aria-label="tipoConta" aria-describedby="tipoConta"></select>
                    </div>
                </div>-->

                <div class="col-4">
                    <div>
                        <label class="form-label">Confirmar Senha</label>
                        <input type="password" class="form-control" placeholder="" id="senhaconfirmaValidate" onfocusout="validSenhaConfirma()" name="senha1" aria-label="senha1" aria-describedby="senha1">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher campo com mesma senha para confirmar.
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <div class="col-12 d-flex flex-row justify-content-end">
            <button class="btn btn-warning mb-3 me-2 px-3 shadow-sm rounded-4" type="reset">Limpar</button>
            <button class="btn mb-3 shadow-sm rounded-4 text-white" type="button" style="background-color: #AB81A3" data-bs-toggle="modal" data-bs-target="#pagamentoModal">Registrar</button>
        </div>

        <!-- Modal -->
        <div class="modal fade" id="pagamentoModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="exampleModalLabel">Plano de subscrição</h1>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <h6>Seja um investigador Premium na MosalaLinkUp!</h6>
                        <div class="mb-3 mt-3">
                            <!--<div class="mb-2">
                                <input type="checkbox" class="btn-check" name="packLite" id="btn-check-lite" autocomplete="off">
                                <label class="btn btn-secondary" for="btn-check-1">Pacote Lite</label>
                            </div>-->

                            <div class="mb-2">
                                <input type="checkbox" class="btn-check" name="packPremium" id="btn-check-premium" autocomplete="off">
                                <label class="btn btn-primary" for="btn-check-2">Pacote Premium</label>
                            </div>
                            <small>Partilhe seus projectos, encontre investidores e dê soluções aos problemas!</small>
                            <p class="mt-2"> Por apenas <i class="fw-bold fs-5"> 2.000,00 <i class="text-muted fw-normal">AOA/Mensal</i></i></p>
                        </div>
                        <h6 class="mb-3">Selecione o metodo de pagamento</h6>
                        <div class="d-flex flex-row">
                            <div class="mb-4 me-2">
                                <input type="checkbox" class="btn-check" name="checkpagamento" id="btn-check-1" onfocusout="validCheck()" autocomplete="off">
                                <label class="btn btn-outline-secondary" for="btn-check-1">Visa Card</label>

                                <div class="valid-feedback">

                                </div>
                            </div>
                            <div class="mb-4 me-2">
                                <input type="checkbox" class="btn-check" name="checkpagamento" id="btn-check-2" onfocusout="validCheck()" autocomplete="off">
                                <label class="btn btn-outline-secondary" for="btn-check-2">Paypal</label>
                            </div>
                            <div class="mb-4 me-2">
                                <input type="checkbox" class="btn-check" name="checkpagamento" id="btn-check-3" onfocusout="validCheck()" autocomplete="off">
                                <label class="btn btn-outline-secondary" for="btn-check-3">Multicaixa Express</label>
                                <div class="valid-feedback">

                                </div>
                                <div class="invalid-feedback">
                                    Preencher campo com o número do cartão.
                                </div>
                            </div>
                        </div>
                        <h6 class="mb-2">Dados do cartão</h6>
                        <div class="row">
                            <div class="col">
                                <div class="mb-3">
                                    <label for="numCartao" class="form-label">Nº do Cartão</label>
                                    <input type="number" class="form-control" id="numCartao" name="numCartao" onfocusout="validNumcartao()" placeholder="1000 2345 6000 7890">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher campo com o número do cartão.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <div class="mb-3">
                                    <label for="dataCartaoExp" class="form-label">Data de Expiração</label>
                                    <input type="date" class="form-control" id="dataCartaoExp" name="dataCartaoExp" onfocusout="validDataCartaoExp()" placeholder="<EMAIL>">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher campo com a data de expiração do cartão.
                                    </div>
                                </div>
                            </div>

                            <div class="col">
                                <div class="mb-3">
                                    <label for="cvvValidate" class="form-label">CVV</label>
                                    <input type="number" class="form-control" name="cvvValidate" id="cvvValidate" onfocusout="validCVV()" placeholder="890">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher campo com o CVV do cartão.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-secondary" data-bs-dismiss="modal">Apenas registrar</button>
                        <button type="submit" id="subscrever" class="btn" style="background-color: #AB81A3; color: white">Subscrever agora!</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="toast-container position-fixed bottom-0 end-0 p-3 "  data-bs-autohide="false">
    <div id="liveToast" class="toast  border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto" id="titlo-alerta">MusalaLinkUp</strong>
            <small class="text-body-secondary">Agora</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="mensagem">
            Crie sua conta investigador independente, poderás ver projectos divulgados e procurar receber propostas de investimentos!
        </div>
    </div>
</div>
<script>
    window.onload = (event) => {
        getMunicipios();

        getFaculdades();
        getBairros();
        const toastLiveExample = document.getElementById('liveToast')

        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        setTimeout(() => {
            toastBootstrap.show()
        },1000)
    };

    $(document).ready(function () {
        $("#provincia").change(function () {
            $("#municipio").empty();
            getMunicipios();
        })

        $("#subscrever").click(function (){
            novo = $("<input type='hidden' name='ehSubscrito' value='true'>")
            novo.appendTo($("#pagamentoModal"))
        })

        $("#municipio").change(function () {
            $("#bairro").empty();
            getBairros();
        })

        $("#instituto").change(function () {
            $("#faculdade").empty();
            getFaculdades();
        })
    })

    function getFaculdades() {
        institutoId = $("#instituto").val();
        url = "http://localhost:8080/entidadeFaculdadeAjax/" + institutoId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                faculdades = $("#faculdade")
                $.each(responseJson, function (index, entidadeFaculdade) {
                    $("<option>")
                        .val(entidadeFaculdade.id.faculdade.id)
                        .text(entidadeFaculdade.id.faculdade.designacao)
                        .appendTo(faculdades);

                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }

    function getMunicipios() {
        provinciaId = $("#provincia").val();
        url = "http://localhost:8080/localidadesAjax/" + provinciaId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                municipios = $("#municipio")
                $.each(responseJson, function (index, localidade) {
                    $("<option>")
                        .val(localidade.id)
                        .text(localidade.designacao)
                        .appendTo(municipios);

                })
                getBairros()
            })
            .fail(function () {
            })
            .always(function () {
            });
    }


    function getBairros() {
        municipioId = $("#municipio").val();
        url = "http://localhost:8080/localidadesAjax/" + municipioId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                bairros = $("#bairro")
                $.each(responseJson, function (index, localidade) {
                    $("<option>")
                        .val(localidade.id)
                        .text(localidade.designacao)
                        .appendTo(bairros);

                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }

    function validNome(){
        info = document.forms["myForm"]["pessoa.nome"].value;
        component = $("#nomeValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validApelido(){
        info = document.forms["myForm"]["pessoa.apelido"].value;
        component = $("#apelidoValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataNascimento(){
        info = document.forms["myForm"]["pessoa.dataNascimento"].value;
        component = $("#dataNascimento")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validBi(){
        info = document.forms["myForm"]["pessoa.bi"].value;
        component = $("#identidade")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataEmissao(){
        info = document.forms["myForm"]["pessoa.dataEmissao"].value;
        component = $("#dataEmissao")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataValidade(){
        info = document.forms["myForm"]["pessoa.dataValidade"].value;
        component = $("#dataValidade")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validContacto(){
        info = document.forms["myForm"]["pessoa.contacto"].value;
        component = $("#contactoValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validEmail(){
        info = document.forms["myForm"]["pessoa.email"].value;
        component = $("#emailValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validEndereco(){
        info = document.forms["myForm"]["pessoa.endereco"].value;
        component = $("#enderecoValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validUsuario(){
        info = document.forms["myForm"]["username"].value;
        component = $("#usuarioValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validEmailLogin(){
        info = document.forms["myForm"]["email"].value;
        component = $("#emailLoginValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenha(){
        info = document.forms["myForm"]["senha"].value;
        component = $("#senhaValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenhaConfirma(){
        info = document.forms["myForm"]["senha1"].value;
        component = $("#senhaconfirmaValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    //Validação da subscrição

    function validCVV(){
        info = document.forms["myForm"]["cvvValidate"].value;
        component = $("#cvvValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataCartaoExp(){
        info = document.forms["myForm"]["dataCartaoExp"].value;
        component = $("#dataCartaoExp")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validNumcartao(){
        info = document.forms["myForm"]["numCartao"].value;
        component = $("#numCartao")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validCheck(){
        info = document.forms["myForm"]["checkpagamento"].value;
        if(info != "" ) {
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
        }
    }

    var dadosGlobais;

    function fazerRequisicao() {
        email = document.forms["myForm"]["username"].value;

        url = "http://localhost:8080/usuarioAjax/"+email;
        // Retorna uma Promise que resolve quando a requisição é bem-sucedida
        return new Promise((resolve, reject) => {
            // Faz a requisição usando a função fetch
            fetch(url)
                .then(response => {

                    // Verifica se a resposta da requisição é bem-sucedida (status 2xx)
                    if (!response.ok) {
                        throw new Error('Erro na requisição');
                    }
                    // Parseia a resposta como JSON
                    return response.json();
                })
                .then(data => {
                    // Armazena os dados na variável global
                    dadosGlobais = data;
                    // Resolve a Promise
                    submitValidation = true
                    resolve(submitValidation);
                })
                .catch(error => {
                    // Rejeita a Promise em caso de erro
                    reject(error);
                });
        });
    }

    function validateForm() {
        const toastLiveExample = document.getElementById('liveToast')
        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        let submit = false;
        fazerRequisicao().then((submitValidation) =>{
            nome = document.forms["myForm"]["pessoa.nome"].value;
            apelido = document.forms["myForm"]["pessoa.apelido"].value;
            dtNascimento = document.forms["myForm"]["pessoa.dataNascimento"].value;
            bi = document.forms["myForm"]["pessoa.bi"].value;
            dtEmissao = document.forms["myForm"]["pessoa.dataEmissao"].value;
            dtValidade = document.forms["myForm"]["pessoa.dataValidade"].value;
            contacto = document.forms["myForm"]["pessoa.contacto"].value;
            email = document.forms["myForm"]["pessoa.email"].value;
            endereco = document.forms["myForm"]["pessoa.endereco"].value;
            username = document.forms["myForm"]["username"].value;
            email2 = document.forms["myForm"]["email"].value;
            senha = document.forms["myForm"]["senha"].value;
            senhaConfirmar = document.forms["myForm"]["senha1"].value;
            cvv = document.forms["myForm"]["cvvValidate"].value;
            dtExpiracao = document.forms["myForm"]["dataCartaoExp"].value;
            numCartao = document.forms["myForm"]["numCartao"].value;

            if (nome == ""
                || apelido == ""
                || dtNascimento == ""
                || bi ==""
                || dtEmissao == ""
                || dtValidade == ""
                || contacto == ""
                || email == ""
                || endereco == ""
                || username == ""
                || email2 == ""
                || senha == ""
                || cvv == ""
                || dtExpiracao == ""
                || numCartao == ""
                || senhaConfirmar == ""
                || senha != senhaConfirmar) {
                validNome()
                validApelido()
                validBi()
                validContacto()
                validDataEmissao()
                validDataNascimento()
                validDataValidade()
                validEmail()
                validEmailLogin()
                validEndereco()
                validSenha()
                validSenhaConfirma()
                validUsuario()
                validCVV()
                validNumcartao()
                validDataCartaoExp()
                //confirmar senha
                if(senha != senhaConfirmar){

                    $("#liveToast").addClass("text-bg-danger")
                    $("#titlo-alerta").empty()
                    $("#titlo-alerta").text("Erro ao criar conta estudante")
                    $("#mensagem").empty()
                    $("#mensagem").text("A senha deve ser a mesma no campo confirmar senha")
                    toastBootstrap.show()
                    submit = false;
                }
                setTimeout(() => {
                    const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
                    $("#liveToast").addClass("text-bg-danger")
                    $("#liveToast").removeClass("text-bg-warning")
                    $("#titlo-alerta").empty()
                    $("#titlo-alerta").text("Erro ao criar conta estudante")
                    $("#mensagem").empty()
                    $("#mensagem").text("Verifique os campos se estão vazios ou devidamente preenchidos.")
                    toastBootstrap.show()
                    submit = false;
                },4000)
            }
            else if(dadosGlobais)
            {
                submit = false;
                const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
                $("#liveToast").addClass("text-bg-warning")
                $("#liveToast").removeClass("text-bg-danger")
                $("#titlo-alerta").empty()
                $("#titlo-alerta").text("Erro ao criar conta estudante")
                $("#mensagem").empty()
                $("#mensagem").text("Já existe uma conta com este username")
                toastBootstrap.show()
            }
            else {
                submit = submitValidation;
                $("#liveToast").removeClass("text-bg-danger")
                $("#liveToast").removeClass("text-bg-warning")
                $("#titlo-alerta").empty()
                $("#titlo-alerta").text("MusalaLinkUp")
                $("#mensagem").empty()
                $("#mensagem").text("A tua conta foi criada com sucesso. Agora podes iniciar sua sessão.")
                toastBootstrap.show()
                setTimeout(() => {
                    document.myForm.submit()

                },5000)
            }
        })
        return submit;
    }
</script>
</body>
</html>