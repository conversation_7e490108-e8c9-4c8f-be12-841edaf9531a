package com.sakidila.projectoic.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.Date;

@Entity
@Data
public class Pessoa {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = true)
    private String nome;

    @Column(nullable = true)
    private String apelido;

    @Column(nullable = true)
    private String email;



    @Column(nullable = true)
    private String endereco;

    private LocalDate dataEmissao;

    private LocalDate dataValidade;

    private LocalDate dataNascimento;

    @Column(nullable = true)
    private String bi;

    @Column(nullable = true)
    private String passaporte;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "fk_sexo")
    private Sexo sexo;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "fk_estado_civil")
    private EstadoCivil estadoCivil;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "fk_localidade")
    private Localidade localidade;

    @JsonIgnore
    @OneToOne
    @JoinColumn(name = "fk_conta")
    private Conta conta;

}
