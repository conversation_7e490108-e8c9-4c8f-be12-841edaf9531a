package com.sakidila.projectoic.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Entity
@Getter
@Setter
public class Metodologia {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String designacao;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "fk_fase_pai")
    private Metodologia fasePai;

    @OneToMany(mappedBy = "fasePai")
    private List<Metodologia> subFases;

    @JsonIgnore
    @OneToMany(mappedBy = "fase")
    private List<Tarefa> tarefas;
}
