package com.sakidila.projectoic.entity;


import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.Date;

@Entity
@Getter
@Setter
public class Tarefa {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idTarefa;

    private String designacao;

    private LocalDate dataInicio;
    private LocalDate dataFim;

    @ManyToOne
    @JoinColumn(name = "fk_estado")
    private Estado estado;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "fk_fase")
    private Metodologia fase;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "fk_investigador")
    private Conta investigador;

    @ManyToOne
    @JoinColumn(name = "fk_projecto")
    private Projecto projecto;
}
