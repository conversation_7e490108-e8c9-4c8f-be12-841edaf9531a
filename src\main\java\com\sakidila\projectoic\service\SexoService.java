package com.sakidila.projectoic.service;

import com.sakidila.projectoic.entity.Sexo;
import com.sakidila.projectoic.repository.SexoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SexoService {

    @Autowired
    private SexoRepository repository;

        public Sexo criarSexo(Sexo sexo){
            return repository.save(sexo);
        }

        public List<Sexo> listaSexos(){
            return repository.findAll();
        }
}
