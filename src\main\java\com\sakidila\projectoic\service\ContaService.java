package com.sakidila.projectoic.service;

import com.sakidila.projectoic.entity.Centro;
import com.sakidila.projectoic.entity.Conta;
import com.sakidila.projectoic.entity.TipoConta;
import com.sakidila.projectoic.entity.TipoEntidade;
import com.sakidila.projectoic.repository.*;
import com.sakidila.projectoic.utility.Nomenclatura;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class ContaService {

    Nomenclatura nomenclatura = new Nomenclatura();

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private ContaRepository repository;

    @Autowired
    private PessoaRepository pessoaRepository;

    @Autowired
    private TipoContaRepository tipoContaRepository;

    @Autowired
    private FaculdadeRepository faculdadeRepository;

    @Autowired
    private NivelAcademicoRepository nivelAcademicoRepository;

    @Autowired
    private TipoEntidadeRepository tipoEntidadeRepository;

    @Autowired
    private EntidadeRepository entidadeRepository;

    public void criarContaPadrao(){
        Conta conta = new Conta();
        conta.setEmail("<EMAIL>");
        conta.setSenha("admin");
        conta.setUsername("admin");
        TipoConta tipoConta = tipoContaRepository.findById(Long.parseLong("1")).get();
        if(!repository.existsByEmail(conta.getEmail())) {
            conta.setSenha(passwordEncoder.encode(conta.getPassword()));
            conta.setTipoConta(tipoConta);
            repository.save(conta);
        }
    }

    public Conta criarConta(Conta conta,Long tipo){
            if(conta.getPessoa() != null) {
                pessoaRepository.save(conta.getPessoa());
                // Estudantes | pesquisadores das instituições | gestores das instituições
                if(tipo == nomenclatura.getESTUDANTE() || tipo == nomenclatura.getINVESTIGADOR() || tipo == nomenclatura.getGESTOR_CENTRO() || tipo == nomenclatura.getGESTOR_PROJECTO()) {
                    conta.setEhSubscrito(true);
                    conta.setFaculdade(faculdadeRepository.findById(conta.getFaculdade().getId()).get());
                    conta.setEntidade(entidadeRepository.findById(conta.getEntidade().getId()).get());
                    conta.setNivelAcademico(nivelAcademicoRepository.findById(conta.getNivelAcademico().getId()).get());
                }
                else if(tipo == nomenclatura.getGESTOR_INSTITUTO()) {
                    conta.setEhSubscrito(true);
                    conta.setEntidade(entidadeRepository.findById(conta.getEntidade().getId()).get());
                }
                else if(tipo == nomenclatura.getINVESTIGADOR_INDEPENDENTE())
                    conta.setNivelAcademico(nivelAcademicoRepository.findById(conta.getNivelAcademico().getId()).get());
                // para todas pessoas que fazem parte da app

            }
            else if(tipo == 5){
                TipoEntidade tipoEntidade = tipoEntidadeRepository.findById(Long.parseLong("1")).get();
                conta.getEntidade().setTipo(tipoEntidade);
                System.out.println("entidade id: "+conta.getEntidade().getId());
                entidadeRepository.save(conta.getEntidade());
            }


            if(conta.getId() == null) {
                conta.setSenha(passwordEncoder.encode(conta.getPassword()));
            }
            else{
                String senha = repository.findById(conta.getId()).get().getSenha();
                System.out.println("senha bd: "+senha+" senha user nova:"+conta.getSenha());
                if(conta.getSenha().compareToIgnoreCase(senha) != 0 && !passwordEncoder.matches(conta.getSenha(),senha)) {
                    conta.setSenha(passwordEncoder.encode(conta.getSenha()));

                }
                else if(!passwordEncoder.matches(conta.getSenha(),senha))
                    conta.setSenha(senha);
            }
            return repository.save(conta);
    }

    public List<Conta> listarInvestigadorPeloIdInstituicaoIdTipoConta(Long idInstituicao, Long idTipoConta){
        return repository.findAllByEntidadeIdAndTipoContaId(idInstituicao,idTipoConta);
    }

    public Conta mostrarDadoConta(String email){
        return repository.findByNomeUsuario(email).get();
    }

    public Conta findById(Long chave) {
        Optional<Conta> optional = repository.findById(chave);
        if (optional.isPresent()) {
            return optional.get();
        }
        return null;
    }

    public List<Conta> encontrarTodasContas(List<Centro> centros, Conta conta){
        List<Conta> contas = new ArrayList<>();
        for (Centro centro:centros) {
            if(centro.getConta().getEntidade().getId() == conta.getEntidade().getId())
                for (Conta item:centro.getMembros()) {
                    contas.add(item);
                }
        }
        return contas;
    }

    public List<Conta> findAllContasByIdEntidade(Long idEntidade){
        return repository.findAllByEntidadeId(idEntidade);
    }

    public boolean verificarContaExistencia(String username){
        if(repository.findByUsername(username) != null){
            return true;
        }
        return false;
    }

    public Conta verificarContaExistenciaObjecto(String username){
        Conta conta = repository.findByUsername(username);
        if(conta != null){
            return conta;
        }
        return null;
    }

    public void subscrever(Long idConta) {
        repository.subscribe(idConta);
    }

    public Conta desativar(Long id){
        Conta conta = repository.findById(id).get();
        if(conta!=null)
            repository.disabledByIdConta(id);
        return conta;
    }

    public Conta deletarCentro(Long idCentro, Long idConta){
        Conta conta = repository.findContaByCentroIdAndId(idCentro,idConta).get();
        if(conta != null){
            conta.setCentro(null);
            conta.setTipoConta(tipoContaRepository.findById(nomenclatura.getINVESTIGADOR()).get());
            return repository.save(conta);
        }
        return null;
    }
}
