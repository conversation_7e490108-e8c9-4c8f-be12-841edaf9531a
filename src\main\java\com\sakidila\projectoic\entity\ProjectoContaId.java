package com.sakidila.projectoic.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Embeddable;
import jakarta.persistence.ManyToOne;
import lombok.*;

import java.io.Serializable;

@Embeddable
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
public class ProjectoContaId implements Serializable {

    @ManyToOne(cascade = CascadeType.REMOVE)
    private Projecto projecto;

    @ManyToOne(cascade = CascadeType.REMOVE)
    private Conta conta;
}
