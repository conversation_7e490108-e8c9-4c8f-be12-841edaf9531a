package com.sakidila.projectoic.service;

import com.sakidila.projectoic.entity.EntidadeFaculdade;
import com.sakidila.projectoic.entity.Faculdade;
import com.sakidila.projectoic.repository.EntidadeFaculdadeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EntidadeFaculdadeService {

    @Autowired
    private EntidadeFaculdadeRepository repository;

    public EntidadeFaculdade criarEntidadeFaculdade(EntidadeFaculdade entidadeFaculdade){

        return repository.save(entidadeFaculdade);
    }

    public List<EntidadeFaculdade> criarListaEntidadeFaculdade(List<EntidadeFaculdade> lista){
        return repository.saveAll(lista);
    }

    public Page<EntidadeFaculdade> encontrarPagina(int pagina, int tamanho,Long idInstituto) {
        Pageable pageable = PageRequest.of(pagina - 1, tamanho, Sort.by("id.instituto.designacao"));
        return  repository.findAllByIdInstitutoId(idInstituto,pageable);
    }

    public List<EntidadeFaculdade> ListaEntidadeFaculdadePelaInstituto(Long idInstituto){
        return repository.encontrarFaculdadesPeloIdInstituto(idInstituto);
    }

    public EntidadeFaculdade desativar(Long idFaculdade,Long idInstituto){
        EntidadeFaculdade entidadeFaculdade = repository.findByIdFaculdade(idFaculdade,idInstituto).get();
        if(entidadeFaculdade!=null)
            repository.disabledByIdFaculdade(idFaculdade,idInstituto);
        return entidadeFaculdade;
    }
}
