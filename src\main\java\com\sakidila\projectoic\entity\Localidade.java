package com.sakidila.projectoic.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Entity
@Getter
@Setter
public class Localidade {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String designacao;

    @ManyToOne
    @JoinColumn(name = "fk_localidade_pai")
    private Localidade localidadePai;

    @JsonIgnore
    @Nullable
    @OneToMany(mappedBy = "localidade")
    private List<Entidade> listaLocalidades;

    @JsonIgnore
    @Nullable
    @OneToMany(mappedBy = "localidade")
    private List<Pessoa> listaPessoas;

}
