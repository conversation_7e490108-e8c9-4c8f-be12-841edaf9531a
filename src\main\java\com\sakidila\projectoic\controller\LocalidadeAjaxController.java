package com.sakidila.projectoic.controller;

import com.sakidila.projectoic.entity.Localidade;
import com.sakidila.projectoic.service.LocalidadeServico;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/localidadesAjax")
public class LocalidadeAjaxController {

    @Autowired
    private LocalidadeServico localidadeServico;

    @GetMapping("/{id}")
    public List<Localidade> encontrarLocalidadesPelaLocalidadePai(@PathVariable Long id){
        List<Localidade> lista = new ArrayList<>();
        if(localidadeServico.findById(id)!=null)
            lista = localidadeServico.listaLocalidadesPelaChaveLocalidadePrimitiva(id);
        return lista;
    }

}
