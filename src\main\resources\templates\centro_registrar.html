<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8" >

    <!-- Importação do Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
            crossorigin="anonymous"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
    <style th:inline="text">
        body{
            font-family: Poppins;
        }
    </style>
    <title>Registra-se na Sakidila!</title>
</head>
<body class="bg-light row" style="overflow-x: hidden">

<!--Menu da App-->
<div class="col-xl-2 col-lg-3 col-md-4">
<div class="d-flex flex-column flex-shrink-0 p-3 bg-white shadow-sm" style="width: 280px; height: 100vh">
    <a href="/sakidila_ic/portal/" class="d-flex align-items-center mb-3 mb-md-0 me-md-auto link-body-emphasis text-decoration-none">
        <img src="/img/logotipo.jpg" alt="Bootstrap" width="100"> <span class="fw-bold text-dark text-center">Gestão Acadêmica</span></a>
    </a>
    <hr>
    <ul class="nav nav-pills flex-column mb-auto">
        <li class="nav-item">
            <a href="/sakidila_ic/portal/" class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Inicio
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/entidade/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == adminPerfil}" class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Entidade
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/entidade_faculdade/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Faculdade
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/usuario/gestor_instituicao_registrar"
               th:if="${dadoConta.tipoConta.designacao == adminPerfil}"
               class="nav-link link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#table"></use></svg>
                Gestor de Instituição
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/centro/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link  active">
                <svg class="bi pe-none me-2" width="16" height="16"><use h:href="/sakidila_ic/centro/registrar"></use></svg>
                Centro de Investigação
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/centro/investigador/associar"
               th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil}"
               class="nav-link  active">
                <svg class="bi pe-none me-2" width="16" height="16"><use h:href="/sakidila_ic/centro/registrar"></use></svg>
                Centro de Investigação
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/portal/projecto/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil
               or dadoConta.tipoConta.designacao == gestorProjectoPerfil
               or dadoConta.tipoConta.designacao == investigadorPerfil}"
               class="nav-link">
                <svg class="bi pe-none me-2" width="16" height="16"><use href="/sakidila_ic/centro/registrar"></use></svg>
                Projectos
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/portal/projecto/pendentes/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link">
                <svg class="bi pe-none me-2" width="16" height="16"><use href="/sakidila_ic/centro/registrar"></use></svg>
                Projectos pendentes
            </a>
        </li>
        <li>
            <a href="#" class="nav-link link-body-emphasis"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}">
                <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#table"></use></svg>
                Estudante
            </a>
        </li>
    </ul>
    <hr>
    <div class="dropdown">
        <a href="#" class="d-flex align-items-center link-body-emphasis text-decoration-none dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
            <img th:if="${conta == userDefault}" th:src="@{/img/user.png}" alt="mdo" width="32" height="32" class="rounded-circle">
            <img th:if="${conta != null}" th:src="${dadoConta.foto}" alt="mdo" width="32" height="32" class="rounded-circle">
            <strong>[[${dadoConta.username}]]</strong>
        </a>
        <ul class="dropdown-menu text-small shadow">
            <li><a th:if="${conta == userDefault}" class="dropdown-item" href="/sakidila_ic/usuario/login">Entrar</a></li>
            <li><a th:if="${conta != null}" class="dropdown-item" href="">Meu Perfil</a></li>
            <li><a th:if="${conta != null}" class="dropdown-item" href="/logout">Sair</a></li>
        </ul>
    </div>
</div>
</div>

<div class="col-xl-10 col-lg-9 col-md-8">
<div class="container">
    <div class="d-flex flex-row mt-4 justify-content-end">
        <button href="/sakidila_ic/usuario/investigador_instituicao_registrar"
                class="me-3" style="border:none; background-color:transparent;text-decoration: none; color: #7c6aa2"
                data-bs-toggle="modal"
        data-bs-target="#criarCentro">Registrar centro</button>
        <a href="/sakidila_ic/usuario/investigador_instituicao_registrar" class=" me-3" style="text-decoration: none; color: #7c6aa2">Adicionar investigador</a>
        <a href="/sakidila_ic/centro/investigador/associar/1" class="" style="text-decoration: none; color: #7c6aa2">Associar investigador</a>
    </div>

    <!-- Modal -->
    <form method="post" name="myForm" th:action="@{/sakidila_ic/centro/salvar}" enctype="multipart/form-data"  onsubmit="return validateForm()" required>
    <div class="modal fade" id="criarCentro" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="" id="staticBackdropLabel"><h5 class="modal-title fs-4 fw-bold" style="color: rgba(158,158,151)">Criar <i class="fw-bold text-dark">Centro de Investigação!</i></h5></h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">

                        <fieldset class=" px-5 py-4 mb-3">
                            <legend class="text-secondary"></legend>
                            <div class="row mb-3">
                                <div class="col">
                                    <div class="col">
                                        <div class="">
                                            <h4 style="color: #AB81A3">Instituto</h4>
                                            <h4 th:text="${dadoConta.entidade.designacao}" style=""></h4>
                                            <input type="hidden" style="display: none" name="entidade.id" th:value="${dadoConta.entidade.id}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col">
                                    <div class="">
                                        <label for="formFile" class="form-label">Adicione um logotipo ao centro</label>
                                        <input class="form-control" type="file" name="imagem" id="formFile">
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="">
                                        <label class="form-label" >Designação</label>
                                        <input type="text" class="form-control" id="designacaoValidate" onfocusout="validDesignacao()" name="designacao" placeholder="" aria-label="Apelido" aria-describedby="apelido">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com a Designação do centro.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col">
                                    <div class="">
                                        <label class="form-label">Descrição</label>
                                        <textarea class="form-control" placeholder="" id="descricaoValidate" onfocusout="validDescricao()" name="descricao" aria-label="Nome" aria-describedby="Nome" style="height: 200px"></textarea>
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com a descrição do centro.
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="row mb-3">
                                        <div class="">
                                            <label class="form-label" id="nome">Sigla</label>
                                            <input type="text" class="form-control" id="siglaValidate" onfocusout="validSigla()" name="sigla" placeholder="" aria-label="Nome" aria-describedby="Nome">
                                            <div class="valid-feedback">

                                            </div>
                                            <div class="invalid-feedback">
                                                Preencher o campo com a Sigla do centro.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="">
                                            <label class="form-label" id="">Faculdade</label>
                                            <select class="form-select" placeholder="" name="faculdade.id" aria-label="faculdade" aria-describedby="faculdade">
                                                <option th:each="item : ${faculdades}" th:value="${item.id.faculdade.id}" th:if="${!item.disabled}"
                                                        th:text="${item.id.faculdade.designacao}"></option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="">
                                            <label class="form-label" >Area de Aplicação</label>
                                            <select class="form-select" placeholder="" name="curso.id" aria-label="estadoCivil" aria-describedby="estadoCivil">
                                                <option th:each="item : ${cursos}" th:value="${item.id}"
                                                        th:text="${item.designacao}"></option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class=" ">
                                            <span class="form-label" >Responsavel</span>
                                            <select class="form-select" placeholder="" name="conta.id" aria-label="estadoCivil" aria-describedby="estadoCivil">
                                                <option th:each="item : ${investigadores}" th:value="${item.id}"
                                                        th:text="${item.pessoa.nome+' '+item.pessoa.apelido}"></option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 d-flex flex-row justify-content-end align-items-end">

                            </div>
                        </fieldset>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Voltar</button>
                    <button class="btn btn-warning  shadow-sm" type="reset">Limpar</button>
                    <button class="btn btn-primary  shadow-sm" type="submit">Salvar</button>
                </div>
            </div>
        </div>
    </div>
    </form>
        <h5 class="display-6 mt-1 mb-1 fw-bold" style="color: rgba(158,158,151)">Visualização <i class="fw-bold text-dark"></i></h5>
        <hr>
        <fieldset class="bg-white px-5 py-4 shadow-sm rounded mb-3" style="height: 80vh">
            <table class="table">
                <tr>
                    <th scope="col">Logotipo</th>
                    <th scope="col">Designacão</th>
                    <th scope="col">Descrição</th>
                    <th scope="col">Responsavel</th>
                    <th scope="col"></th>
                    <th scope="col"></th>
                </tr>
                <tr th:each="item : ${centros}" id="centros" th:if="${!item.disabled}">
                    <td>
                        <img th:src="${item.logotipo}" th:if="${item.logotipo != null}" height="50">
                        <img th:src="@{'/img/empty.jpg'}" th:if="${item.logotipo == null}" height="50">
                    </td>
                    <td th:text="${item.designacao}"></td>
                    <td th:text="${item.descricao}"></td>
                    <td th:if="${item.conta != null}" th:text="${item.conta.pessoa.nome}+' '+${item.conta.pessoa.apelido}"></td>
                    <td th:if="${item.conta == null}" th:text="Nenhum"></td>
                    <td>

                        <input type="hidden" name="idCentro" th:value="${item.id}">
                        <input type="hidden" name="imagem" th:value="${item.logotipo}">
                        <input type="hidden" name="sigla" th:value="${item.sigla}">
                        <input type="hidden" name="designacao" th:value="${item.designacao}">
                        <input type="hidden" name="gestor" th:value="${item.conta.pessoa.nome+' '+item.conta.pessoa.apelido}">
                        <input type="hidden" name="descricao" th:value="${item.descricao}">
                        <input type="hidden" name="curso" th:value="${item.curso.designacao}">
                        <input type="hidden" name="faculdade" th:value="${item.faculdade.designacao}">


                        <button type="button" class="btn btn-primary btn-sm shadow-sm detalhe" data-bs-target="#modalVer" data-bs-toggle="modal">Ver</button>
                    </td>
                    <td><a class="btn btn-danger btn-sm shadow-sm" th:href="@{/sakidila_ic/centro/desativar/{id}(id=${item.id})}">Remover</a></td>
                </tr>
            </table>
            <div class="d-flex justify-content-center">
                <nav class="Page navigation example" th:if="${totalPaginas > 1}">
                    <ul class="pagination">
                        <li class="page-item"><a class="page-link">Total de projectos: [[${totalItems}]]</a></li>
                        <div th:each="i : ${#numbers.sequence(1,totalPaginas)}">
                            <li class="page-item">
                                <a class="page-link" th:if="${paginaActual != i}"
                                   th:href="@{'/sakidila_ic/centro/registrar/'+ ${i}}">[[${i}]]</a>
                                <a class="page-link" th:unless="${paginaActual != i}">[[${i}]]</a> &nbsp; &nbsp;
                            </li>
                        </div>
                        <li class="page-item">
                            <a class="page-link" th:if="${paginaActual < totalPaginas}"
                               th:href="@{'/sakidila_ic/centro/registrar/'+${paginaActual + 1}}">Próximo</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" th:if="${paginaActual < totalPaginas}"
                               th:href="@{'/sakidila_ic/centro/registrar/'+${paginaActual + 1}}">Ultimo</a>
                            <a class="page-link" th:unless="${paginaActual < totalPaginas}">Ultimo</a>
                        </li>
                    </ul>
                </nav>
            </div>

        </fieldset>
</div>
</div>


<!--Modal de ver ou editar um projecto-->
<form method="post" name="myFormEditar" th:action="@{/sakidila_ic/centro/salvar}" enctype="multipart/form-data"  onsubmit="return validateFormEditar()" required>
<div class="modal fade" id="modalVer" tabindex="-1" aria-labelledby="ModalLabelVer" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="ModalLabelVer">Detalhes do Centro</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!--<input type="hidden" id="idProjectoAux" name="id">-->
                <div class="row">
                    <input type="hidden" name="id" id="idCentro">
                    <div class="ver">
                        <h6>Logotipo do centro</h6>
                        <img id="imagemAux" width="100" height="100">
                    </div>
                    <div class="p-3 editar  d-none">
                        <label class="form-label fw-bolder">Adicione o logotipo do Centro</label>
                        <input class="form-control" type="file" id="imagem" name="imagem">
                    </div>
                </div>
                <div class="row">
                    <div class="ver">
                        <h6 class="mb-1">Sigla</h6>
                        <p class="" id="siglaAux"></p>
                    </div>
                    <div class="p-3 editar d-none">
                        <label class="form-label fw-bolder">Sigla</label>
                        <input type="text" class="form-control" onfocusout="validSigla()" id="siglaAltera" name="sigla">
                        <div class="invalid-feedback">
                            Preencher o campo com a designação do centro.
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="ver">
                        <h6 class="mb-1">Designação</h6>
                        <p class="" id="designacaoAux"></p>
                    </div>
                    <div class="p-3 editar d-none">
                        <label class="form-label fw-bolder">Designação</label>
                        <input type="text" class="form-control" onfocusout="validDesignacao()" id="designacaoAltera" name="designacao">
                        <div class="invalid-feedback">
                            Preencher o campo com a designação do centro.
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="ver">
                        <h6 class="mb-1">Descrição</h6>
                        <p class="" id="descricaoAux"></p>
                    </div>
                    <div class="p-3 editar  d-none">

                        <div class="form-floating">
                            <textarea type="text" class="form-control" id="descricaoAltera" onfocusout="validDescricao()" name="descricao" style="height: 100px" placeholder=""></textarea>
                            <label class="label">Descrição</label>
                            <div class="invalid-feedback">
                                Preencher o campo com a descrição do centro.
                            </div>
                        </div>

                    </div>
                </div>
                <div class="row">
                    <div class="ver">
                        <h6 class="mb-1">Faculdade</h6>
                        <p class="" id="faculdadeAux"></p>
                    </div>
                    <div class="p-3 editar  d-none">
                        <label class="form-label fw-bolder">Faculdade</label>
                        <select class="form-select" id="faculdadeAltera" name="faculdade.id">
                            <option th:each="item : ${faculdades}"
                                    th:text="${item.id.faculdade.designacao}"
                                    th:value="${item.id.faculdade.id}"
                                    th:if="${!item.disabled}">
                            </option>
                        </select>

                    </div>
                </div>
                <div class="row">
                    <div class="ver">
                        <h6 class="mb-1">Area de aplicação</h6>
                        <p class="" id="cursoAux"></p>
                    </div>
                    <div class="p-3 editar  d-none">
                        <label class="form-label fw-bolder">Area de aplicação</label>
                        <select class="form-select" id="cursoAltera" name="curso.id">
                            <option th:each="item : ${cursos}"
                                    th:text="${item.designacao}"
                                    th:value="${item.id}">
                            </option>
                        </select>

                    </div>
                </div>
                <div class="row">
                    <div class="ver">
                        <h6 class="mb-1">Responsavel</h6>
                        <p class="" id="responsavelAux"></p>
                    </div>
                    <div class="p-3 editar d-none">
                        <label class="form-label fw-bolder">Responsavel</label>
                        <select class="form-select" id="responsavelAltera" onfocusout="validResponsavel()" name="conta.id">
                            <!--<option th:each="item : ${investigadores}"
                                    th:text="${item.pessoa.nome+' '+item.pessoa.apelido}"
                                    th:value="${item.id}"
                                    th:if="${item.tipoConta.designacao != gestorCentroPerfil
                                    || item.tipoConta.designacao != gestorInstituicaoPerfil}">
                            </option>-->
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="ver">
                        <h6 class="mb-1">Membros</h6>
                        <div id="investigadores"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                <button type="button" class="btn btn-warning" id="modificarButton" value="0">Modificar</button>
                <button type="submit" class="btn btn-primary d-none" id="salvarButton">Guardar</button>
            </div>
        </div>
    </div>
</div>
</form>
</div>
<div class="toast-container position-fixed bottom-0 end-0 p-3 "  data-bs-autohide="false">
    <div id="liveToast" class="toast  border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto" id="titlo-alerta">MusalaLinkUp</strong>
            <small class="text-body-secondary">Agora</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="mensagem">
            Nesta secção podes criar centros, definir o responsável e seus membros!
        </div>
    </div>
</div>
<script>

    var idCentroGlobal;
    var designacaoGlobal;

    window.onload = (event) => {
        const toastLiveExample = document.getElementById('liveToast')

        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        setTimeout(() => {
            toastBootstrap.show()
        },1000)


    };

    $(document).ready(function () {

        $("#modificarButton").click(function (){
            modificarView();
        })
        getInvestigadoresByCentro()
    })

    function getInvestigadoresByCentro() {
        idCentro = $("#idCentro").text();

        url = "http://localhost:8080/CentroAjax/" + idCentro+"/investigadores";
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                investigadores = $("#investigadores")
                opcoes = $("#responsavelAltera")
                investigadores.empty()
                opcoes.empty()
                $.each(responseJson, function (index, item) {

                        if (!["ROLE_GESTOR_INSTITUICAO"].includes(item.tipoConta.designacao) || [responsavelGlobal].includes(item.pessoa.nome+' '+item.pessoa.apelido)) {
                        $("<p class='p-0 m-0'>")
                            .text(item.pessoa.nome + " " + item.pessoa.apelido)
                            .val(item.id)
                            .appendTo(investigadores)

                        $("<option>")
                            .text(item.pessoa.nome + ' ' + item.pessoa.apelido)
                            .val(item.id)
                            .appendTo(opcoes)
                    }
                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }


    function modificarView(){
        if($("#modificarButton").val() == "0") {
            $(".editar").removeClass("d-none")
            $(".ver").addClass("d-none")
            $("#modificarButton").removeClass("btn-warning")
            $("#modificarButton").addClass("btn-outline-warning")
            $("#modificarButton").text("Apenas ver")
            $("#modificarButton").val("1")
            $("#salvarButton").removeClass("d-none")
        }
        else if ($("#modificarButton").val() == "1"){
            $(".ver").removeClass("d-none")
            $(".editar").addClass("d-none")
            $("#modificarButton").removeClass("btn-outline-warning")
            $("#modificarButton").addClass("btn-warning")
            $("#modificarButton").text("Modificar")
            $("#modificarButton").val("0")
            $("#salvarButton").addClass("d-none")
        }
    }

    $(document).on("click", "#centros button.detalhe", function sms() {

        let tr = $(this).closest('tr');
        idCentroGlobal = tr.find($("[name=idCentro]")).val();
        $("#idCentro").text(idCentroGlobal);
        $("#idCentro").val(idCentroGlobal);
        imagemGlobal = tr.find($("[name=imagem]")).val() != ""  ? tr.find($("[name=imagem]")).val() : '/img/empty.jpg';
        $("#imagemAux").attr("src", imagemGlobal);
        designacaoGlobal = tr.find($("[name=designacao]")).val()
        $("#designacaoAux").text(designacaoGlobal);
        $("#designacaoAltera").val(designacaoGlobal);
        descricaoGlobal = tr.find($("[name=descricao]")).val()
        $("#descricaoAux").text(descricaoGlobal);
        $("#descricaoAltera").text(descricaoGlobal);
        siglaGlobal = tr.find($("[name=sigla]")).val();
        $("#siglaAux").text(siglaGlobal);
        $("#siglaAltera").val(siglaGlobal);
        responsavelGlobal = tr.find($("[name=gestor]")).val()
        $("#responsavelAux").text(responsavelGlobal);
        //$("#gestorAltera").text(tr.find($("[name=gestorProjecto]")).val());
        faculdadeGlobal = tr.find($("[name=faculdade]")).val()
        $("#faculdadeAux").text(faculdadeGlobal);
        //$("#tipoProjectoAltera").text(tr.find($("[name=tipoProjecto]")).val());
        cursoGlobal = tr.find($("[name=curso]")).val()
        $("#cursoAux").text(cursoGlobal);
        getInvestigadoresByCentro()
        //$("#dataCriacaoAltera").text(tr.find($("[name=dataCriacao]")).val());
    })

    function validDesignacao(){
        info = document.forms["myForm"]["designacao"].value;
        component = $("#designacaoValidate")
        if(info != "") {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDesignacaoEditar(){
        infoEditar = document.forms["myFormEditar"]["designacao"].value;
        componentEditar = $("#designacaoAltera")
        if( infoEditar != "") {
            componentEditar.addClass("is-valid")
            componentEditar.removeClass("is-invalid")
        }
        else {
            componentEditar.addClass("is-invalid")
            componentEditar.removeClass("is-valid")
        }
    }

    function validDescricao(){
        info = document.forms["myForm"]["descricao"].value;
        component = $("#descricaoValidate")
        if(info != "") {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDescricaoEditar(){
        infoEditar = document.forms["myFormEditar"]["descricao"].value;
        componentEditar = $("#descricaoAltera")
        if(infoEditar != "") {
            componentEditar.addClass("is-valid")
            componentEditar.removeClass("is-invalid")
        }
        else {
            componentEditar.addClass("is-invalid")
            componentEditar.removeClass("is-valid")
        }
    }

    function validSigla(){
        info = document.forms["myForm"]["sigla"].value;
        component = $("#siglaValidate")
        if(info != "") {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSiglaEditar(){
        infoEditar = document.forms["myFormEditar"]["sigla"].value;
        componentEditar = $("#siglaAltera")
        if(infoEditar != "") {
            componentEditar.addClass("is-valid")
            componentEditar.removeClass("is-invalid")
        }
        else {
            componentEditar.addClass("is-invalid")
            componentEditar.removeClass("is-valid")
        }
    }

    function validateForm() {
        const toastLiveExample = document.getElementById('liveToast')
        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        nome = document.forms["myForm"]["designacao"].value;
        descricao = document.forms["myForm"]["descricao"].value;
        sigla = document.forms["myForm"]["sigla"].value;
        if (nome == ""
            || descricao == ""
            || sigla == "") {
            validSigla()
            validDesignacao()
            validDescricao()
            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
            $("#liveToast").addClass("text-bg-danger")
            $("#liveToast").removeClass("text-bg-warning")
            $("#titlo-alerta").empty()
            $("#titlo-alerta").text("Erro ao registrar o centro")
            $("#mensagem").empty()
            $("#mensagem").text("Verifique os campos se estão vazios ou devidamente preenchidos.")
            toastBootstrap.show()
            return false;
        }
        else {
            $("#liveToast").removeClass("text-bg-danger")
            $("#liveToast").removeClass("text-bg-warning")
            $("#titlo-alerta").empty()
            $("#titlo-alerta").text("MusalaLinkUp")
            $("#mensagem").empty()
            $("#mensagem").text("Centro de Investigação registrado com sucesso!")
            toastBootstrap.show()
            setTimeout(() => {
                document.myForm.submit()
            }, 5000)
        }
        return false;
    }

    function validateFormEditar() {
        const toastLiveExample = document.getElementById('liveToast')
        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        nomeEditar = document.forms["myFormEditar"]["designacao"].value;
        descricaoEditar = document.forms["myFormEditar"]["descricao"].value;
        siglaEditar = document.forms["myFormEditar"]["sigla"].value;
        if (nomeEditar == ""
            || descricaoEditar == ""
            || siglaEditar == "") {
            validSiglaEditar()
            validDesignacaoEditar()
            validDescricaoEditar()
            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
            $("#liveToast").addClass("text-bg-danger")
            $("#liveToast").removeClass("text-bg-warning")
            $("#titlo-alerta").empty()
            $("#titlo-alerta").text("Erro ao registrar o centro")
            $("#mensagem").empty()
            $("#mensagem").text("Verifique os campos se estão vazios ou devidamente preenchidos.")
            toastBootstrap.show()
            return false;
        }
        else {
            $("#liveToast").removeClass("text-bg-danger")
            $("#liveToast").removeClass("text-bg-warning")
            $("#titlo-alerta").empty()
            $("#titlo-alerta").text("MusalaLinkUp")
            $("#mensagem").empty()
            $("#mensagem").text("Centro de Investigação registrado com sucesso!")
            toastBootstrap.show()
            setTimeout(() => {
                document.myFormEditar.submit()
            }, 5000)
        }
        return false;
    }
</script>
</body>
</html>