package com.sakidila.projectoic.service;

import com.sakidila.projectoic.entity.Pessoa;
import com.sakidila.projectoic.repository.PessoaRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PessoaService {

    @Autowired
    private PessoaRepository repository;

        public Pessoa criarPessoa(Pessoa pessoa){
            return repository.save(pessoa);
        }

        public List<Pessoa> listaEstadoCivil(){
            return repository.findAll();
        }
}
