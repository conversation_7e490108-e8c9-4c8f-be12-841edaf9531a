<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">

    <!-- Importação do Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
            crossorigin="anonymous"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style th:inline="text">
        body{

            font-family: Poppins;
        }


    </style>
    <title>Registra-se na Sakidila!</title>
</head>
<body style="background-color: ghostwhite">

<nav class="navbar navbar-expand-lg p-0 sticky-top" style="margin: 0%;">
    <button class="navbar-toggler ms-2" type="button" data-bs-toggle="collapse" data-bs-target="#navbarTogglerDemo01" aria-controls="navbarTogglerDemo01" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarTogglerDemo01">
        <div class=" d-flex flex-column bg-white shadow-sm" style="width: 100%">
            <div class="container-fluid">
                <div class="container d-flex flex-row align-items-center">
                    <a class="navbar-brand" href="/sakidila_ic/portal/">
                        <img src="/img/logotipo.jpg" alt="Bootstrap" width="120">
                    </a>

                    <div class="container d-flex flex-row justify-content-end align-items-center">
                        <a href="/sakidila_ic/usuario/principal" class="nav-link">Registrar</a>
                        <div class="dropdown btn">
                            <a href="#" class="d-block link-body-emphasis text-decoration-none dropdown dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <img src="/img/user.png" alt="mdo" width="32" height="32" class="rounded-circle">
                            </a>
                            <ul class="dropdown-menu text-small">
                                <li><a class="dropdown-item" href="/sakidila_ic/usuario/login">Entrar</a></li>
                                <!--<li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#">Sair</a></li>-->
                            </ul>
                        </div>
                        <i class="fs-5 bi bi-facebook me-2" style="color: #7c6aa2"></i>
                        <i class="fs-5 bi bi-instagram me-2" style="color: #7c6aa2"></i>
                        <i class="fs-5 bi bi-whatsapp" style="color: #7c6aa2"></i>
                    </div>
                </div>

            </div>
            <div class="container-fluid py-1" style="background-color: #AB81A3">
                <ul class="container navbar-nav d-flex justify-content-end ">
                    <li class="nav-item">
                        <a class="nav-link text-white active px-3" aria-current="page" href="/sakidila_ic/portal/">Portal</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link text-white  px-3" data-bs-toggle="dropdown" aria-expanded="false" href="#">Instituições</a>
                        <ul class="dropdown-menu rounded-top-0 rounded-bottom-4 border-top-0 text-white" style="background-color: #AB81A3">
                            <li><a class="dropdown-item text-white" href="#"><i class="fst-normal fw-bold">UCAN </i>Universidade Católica de Angola</a></li>
                            <li><a class="dropdown-item text-white" href="#"><i class="fst-normal fw-bold">UAN </i>Universidade Agostinho Neto</a></li>
                            <li><a class="dropdown-item text-white" href="#"><i class="fst-normal fw-bold">UMA </i>Universidade Metódista de Angola</a></li>
                            <li><a class="dropdown-item text-white" href="#"><i class="fst-normal fw-bold">UTANGA </i>Universidade Técnica de Angola</a></li>
                            <li><a class="dropdown-item text-white" href="#"><i class="fst-normal fw-bold">ISPITEC </i>Instituto Politecnico de Inovação Tecnologica</a></li>
                            <li><a class="dropdown-item text-white" href="#">Ver mais...</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white px-3 active" href="/sakidila_ic/portal/listagem/1">Projectos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white px-3" href="#">Sobre</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white ps-3 me-3" href="#">Contacte-nos</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</nav>                            <!--<li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#">Sair</a></li>-->

<div class="container mt-5">
    <h5 class="display-6 mt-5 mb-1 fw-bold" style="color: #AB81A3">Criar Conta <i class="fw-bold text-dark">Empresa!</i></h5>
    <hr>
    <form method="post" name="myForm" th:action="@{/sakidila_ic/usuario/empresa/registrar/salvar}" onsubmit="return validateForm()" enctype="multipart/form-data" required>
        <fieldset class="bg-white px-5 py-4 shadow-sm rounded-4 mb-3">
            <legend></legend>
                <div class="row mb-3">
                    <div class="col-5">
                        <div class="border border-1 rounded-4 mb-3" style="">
                            <img th:src="@{/img/teach.png}" class="rounded-4" width="100%">
                        </div>
                        <div class="border border-1 p-3 rounded-4">
                            <label class="form-label">Adicione o logotipo da Empresa</label>
                            <input class="form-control" type="file" id="imagem" name="imagem">
                        </div>
                    </div>
                    <div class="col">
                        <div class="px-3">
                            <div class="row mb-4 mt-1">
                                <div class="col">
                                    <div class="">
                                        <label class="form-label">Designação</label>
                                        <input type="text"  class="form-control" placeholder="" id="nomeValidate" onfocusout="validNome()" name="entidade.designacao" aria-label="Nome" aria-describedby="Nome">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com o nome da empresa.
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="">
                                        <label class="form-label" id="sigla">Sigla</label>
                                        <input type="text"  class="form-control" placeholder="" id="siglaValidate" onfocusout="validSigla()" name="entidade.sigla" aria-label="Nome" aria-describedby="Nome">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com a Sigla da empresa.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-4">
                                <div class="col">
                                    <div class="">
                                        <label class="form-label" id="nif">Nif</label>
                                        <input type="text" class="form-control" placeholder="" id="nifValidate" onfocusout="validNif()" name="entidade.nif" aria-label="Nome" aria-describedby="Nome">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com o NIF da empresa.
                                        </div>
                                    </div>
                                </div>
                            </div><div class="row mb-4">
                                <div class="col">
                                    <div class="">
                                        <label class="form-label" id="contacto">Contacto</label>
                                        <input type="text" class="form-control" placeholder="" id="contactoValidate" onfocusout="validContacto()" name="contacto" aria-label="" aria-describedby="">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com o contacto da empresa.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-4">
                                <div class="col">
                                    <div class="">
                                        <label class="form-label">Provincia</label>
                                        <select class="form-select" placeholder="" aria-label="provincia" id="provincia" aria-describedby="provincia">
                                            <option th:each="localidade : ${localidades}" th:value="${localidade.id}"
                                                    th:text="${localidade.designacao}"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="">
                                        <label class="form-label">Municipio</label>
                                        <select class="form-select"  aria-label="municipio"  id="municipio" aria-describedby="municipio"></select>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-4">
                                <div class="col">
                                    <div class="">
                                        <label class="form-label">Bairro</label>
                                        <select class="form-select" id="bairro" name="entidade.localidade.id" placeholder="" aria-label="bairro" aria-describedby="bairro"></select>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="">
                                        <label class="form-label" id="rua">Endereço</label>
                                        <input type="text" class="form-control" placeholder="" id="enderecoValidate" onfocusout="validEndereco()" name="entidade.endereco" aria-label="rua" aria-describedby="rua">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Especifique a rua, nº da casa.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class=" px-3">
                            <div class="row mb-3">
                                <div class="col">
                                    <div class="">
                                        <label class="form-label" id="usuario">Username</label>
                                        <input type="text" class="form-control" placeholder="" id="usuarioValidate" name="username" onfocusout="validUsuario()"  aria-label="usuario" aria-describedby="usuario">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher campo com o nome de usuario.
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class=" ">
                                        <label class="form-label" id="emailConta">E-mail</label>
                                        <input type="email" class="form-control" placeholder="" id="emailLoginValidate" onfocusout="validEmailLogin()" name="email" aria-label="emailConta" aria-describedby="emailConta">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher campo com o email de login.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col">
                                    <div class=" ">
                                        <label class="form-label" id="senha">Senha</label>
                                        <input type="password" class="form-control" placeholder="" id="senhaValidate" onfocusout="validSenha()" name="senha" aria-label="senha" aria-describedby="senha">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher campo com a nova senha da conta.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col">
                                    <div class="">
                                        <label class="form-label">Confirmar Senha</label>
                                        <input type="password" class="form-control" placeholder="" id="senhaconfirmaValidate" onfocusout="validSenhaConfirma()" name="senha1" aria-label="senha1" aria-describedby="senha1">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher campo com mesma senha para confirmar.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

        </fieldset>
        <div class="col-12 d-flex flex-row justify-content-end">
            <button class="btn btn-warning mb-3 me-2 px-3 shadow-sm rounded-4" type="reset">Limpar</button>
            <!--<button class="btn btn-success mb-3 me-2 px-3 shadow-sm rounded-4" type="submit" >Subscrever</button>-->
            <button class="btn mb-3 shadow-sm rounded-4 text-white" type="button" style="background-color: #AB81A3" data-bs-toggle="modal" data-bs-target="#pagamentoModal">Registrar</button>
        </div>

        <!-- Modal -->
        <div class="modal fade" id="pagamentoModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="exampleModalLabel">Plano de subscrição</h1>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <h6>Torna-te uma empresa Premium na MosalaLinkUp!</h6>
                        <div class="mb-3 mt-3">
                            <!--<div class="mb-2">
                                <input type="checkbox" class="btn-check" name="packLite" id="btn-check-lite" autocomplete="off">
                                <label class="btn btn-secondary" for="btn-check-1">Pacote Lite</label>
                            </div>-->

                            <div class="mb-2">
                                <input type="checkbox" class="btn-check" name="packPremium" id="btn-check-premium" autocomplete="off">
                                <label class="btn btn-primary">Pacote Premium</label>
                            </div>
                            <small>Para poder procurar e dar soluções de seus problemas com os investigadores disponiveis subscreva-se na nossa plataforma.</small>
                            <p class="mt-2"> Por apenas <i class="fw-bold fs-5"> 2.000,00 <i class="text-muted fw-normal">AOA/Mensal</i></i></p>
                        </div>
                        <h6 class="mb-3">Selecione o metodo de pagamento</h6>
                        <div class="d-flex flex-row">
                            <div class="mb-4 me-2">
                                <input type="checkbox" class="btn-check" name="checkpagamento" id="btn-check1" autocomplete="off">
                                <label class="btn btn-outline-secondary" for="btn-check1">Visa Card</label>
                            </div>
                            <div class="mb-4 me-2">
                                <input type="checkbox" class="btn-check" name="checkpagamento" id="btn-check2" autocomplete="off">
                                <label class="btn btn-outline-secondary" for="btn-check2">Paypal</label>
                            </div>
                            <div class="mb-4 me-2">
                                <input type="checkbox" class="btn-check" name="checkpagamento" id="btn-check3" autocomplete="off">
                                <label class="btn btn-outline-secondary" for="btn-check3">Multicaixa Express</label>
                                <div class="valid-feedback">

                                </div>
                                <div class="invalid-feedback">
                                    Preencher campo com o número do cartão.
                                </div>
                            </div>
                        </div>
                        <h6 class="mb-2">Dados do cartão</h6>
                        <div class="row">
                            <div class="col">
                                <div class="mb-3">
                                    <label for="numCartao" class="form-label">Nº do Cartão</label>
                                    <input type="number" class="form-control" id="numCartao" name="numCartao" onfocusout="validNumcartao()" placeholder="1000 2345 6000 7890">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher campo com o número do cartão.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <div class="mb-3">
                                    <label for="dataCartaoExp" class="form-label">Data de Expiração</label>
                                    <input type="date" class="form-control" id="dataCartaoExp" name="dataCartaoExp" onfocusout="validDataCartaoExp()" placeholder="<EMAIL>">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher campo com a data de expiração do cartão.
                                    </div>
                                </div>
                            </div>

                            <div class="col">
                                <div class="mb-3">
                                    <label for="cvvValidate" class="form-label">CVC</label>
                                    <input type="number" class="form-control" name="cvvValidate" id="cvvValidate" onfocusout="validCVV()" placeholder="890">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher campo com o CVV do cartão.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-secondary" data-bs-dismiss="modal">Apenas registrar</button>
                        <button type="submit" class="btn" id="subscrever" style="background-color: #AB81A3; color: white">Subscrever agora!</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="toast-container position-fixed bottom-0 end-0 p-3 "  data-bs-autohide="false">
    <div id="liveToast" class="toast  border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto" id="titlo-alerta">MusalaLinkUp</strong>
            <small class="text-body-secondary">Agora</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="mensagem">
           Crie sua conta empresa, poderás ver projectos divulgados e investi-los!
        </div>
    </div>
</div>
<script>
    var verificar = false;
    window.onload = (event) => {
        getMunicipios();

        getFaculdades();
        getBairros();
        const toastLiveExample = document.getElementById('liveToast')

        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        setTimeout(() => {
            toastBootstrap.show()
        },1000)
    };

    function modificarView(){
        if($("#modificarButton").val() == "0") {
            $(".editar").removeClass("d-none")
            $(".ver").addClass("d-none")
            $("#modificarButton").removeClass("btn-warning")
            $("#modificarButton").addClass("btn-outline-warning")
            $("#modificarButton").text("Apenas ver")
            $("#modificarButton").val("1")
            $("#salvarButton").removeClass("d-none")
        }
        else if ($("#modificarButton").val() == "1"){
            $(".ver").removeClass("d-none")
            $(".editar").addClass("d-none")
            $("#modificarButton").removeClass("btn-outline-warning")
            $("#modificarButton").addClass("btn-warning")
            $("#modificarButton").text("Modificar")
            $("#modificarButton").val("0")
            $("#salvarButton").addClass("d-none")
        }
    }

    $(document).ready(function () {
        $("#provincia").change(function () {
            $("#municipio").empty();
            getMunicipios();
        })

        $("#municipio").change(function () {
            $("#bairro").empty();
            getBairros();
        })

        $("#subscrever").click(function (){
            novo = $("<input type='hidden' name='ehSubscrito' value='true'>")
            novo.appendTo($("#pagamentoModal"))
        })

        $("#instituto").change(function () {
            $("#faculdade").empty();
            getFaculdades();
        })

        $("#modificarButton").click(function (){
            modificarView();
        })
    })

    function getFaculdades() {
        institutoId = $("#instituto").val();
        url = "http://localhost:8080/entidadeFaculdadeAjax/" + institutoId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                faculdades = $("#faculdade")
                $.each(responseJson, function (index, entidadeFaculdade) {
                    $("<option>")
                        .val(entidadeFaculdade.id.faculdade.id)
                        .text(entidadeFaculdade.id.faculdade.designacao)
                        .appendTo(faculdades);

                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }

    function getMunicipios() {
        provinciaId = $("#provincia").val();
        url = "http://localhost:8080/localidadesAjax/" + provinciaId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                municipios = $("#municipio")
                $.each(responseJson, function (index, localidade) {
                    $("<option>")
                        .val(localidade.id)
                        .text(localidade.designacao)
                        .appendTo(municipios);

                })
                getBairros()
            })
            .fail(function () {
            })
            .always(function () {
            });
    }


    function getBairros() {
        municipioId = $("#municipio").val();
        url = "http://localhost:8080/localidadesAjax/" + municipioId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                bairros = $("#bairro")
                $.each(responseJson, function (index, localidade) {
                    $("<option>")
                        .val(localidade.id)
                        .text(localidade.designacao)
                        .appendTo(bairros);

                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }


    function validNome(){
        info = document.forms["myForm"]["entidade.designacao"].value;
        component = $("#nomeValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSigla(){
        info = document.forms["myForm"]["entidade.sigla"].value;
        component = $("#siglaValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validNif(){
        info = document.forms["myForm"]["entidade.nif"].value;
        component = $("#nifValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }
    function validContacto(){
            info = document.forms["myForm"]["contacto"].value;
            component = $("#contactoValidate")
            if(info != "" ) {
                component.addClass("is-valid")
                component.removeClass("is-invalid")
            }
            else {
                component.addClass("is-invalid")
                component.removeClass("is-valid")
            }
        }

    function validEndereco(){
        info = document.forms["myForm"]["entidade.endereco"].value;
        component = $("#enderecoValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validUsuario(){
        info = document.forms["myForm"]["username"].value;
        component = $("#usuarioValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validEmailLogin(){
        info = document.forms["myForm"]["email"].value;
        component = $("#emailLoginValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenha(){
        info = document.forms["myForm"]["senha"].value;
        component = $("#senhaValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenhaConfirma(){
        info = document.forms["myForm"]["senha1"].value;
        component = $("#senhaconfirmaValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    //Validação da subscrição

    function validCVV(){
        info = document.forms["myForm"]["cvvValidate"].value;
        component = $("#cvvValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataCartaoExp(){
        info = document.forms["myForm"]["dataCartaoExp"].value;
        component = $("#dataCartaoExp")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validNumcartao(){
        info = document.forms["myForm"]["numCartao"].value;
        component = $("#numCartao")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validCheck(){
        info = document.forms["myForm"]["checkpagamento"].value;
        if(info != "" ) {
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
        }
    }

    var dadosGlobais;

    function fazerRequisicao() {
        email = document.forms["myForm"]["username"].value;

        url = "http://localhost:8080/usuarioAjax/"+email;
        // Retorna uma Promise que resolve quando a requisição é bem-sucedida
        return new Promise((resolve, reject) => {
            // Faz a requisição usando a função fetch
            fetch(url)
                .then(response => {

                    // Verifica se a resposta da requisição é bem-sucedida (status 2xx)
                    if (!response.ok) {
                        throw new Error('Erro na requisição');
                    }
                    // Parseia a resposta como JSON
                    return response.json();
                })
                .then(data => {
                    // Armazena os dados na variável global
                    dadosGlobais = data;
                    // Resolve a Promise
                    submitValidation = true
                    resolve(submitValidation);
                })
                .catch(error => {
                    // Rejeita a Promise em caso de erro
                    reject(error);
                });
        });
    }

    function validateForm() {

        const toastLiveExample = document.getElementById('liveToast')
        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        let submit = false;
        fazerRequisicao().then((submitValidation) => {
            nome = document.forms["myForm"]["entidade.designacao"].value;
            sigla = document.forms["myForm"]["entidade.sigla"].value;
            nif = document.forms["myForm"]["entidade.nif"].value;
            endereco = document.forms["myForm"]["entidade.endereco"].value;
            username = document.forms["myForm"]["username"].value;
            email = document.forms["myForm"]["email"].value;
            senha = document.forms["myForm"]["senha"].value;
            senhaConfirmar = document.forms["myForm"]["senha1"].value;
            cvv = document.forms["myForm"]["cvvValidate"].value;
            dtExpiracao = document.forms["myForm"]["dataCartaoExp"].value;
            numCartao = document.forms["myForm"]["numCartao"].value;
            contacto = document.forms["myForm"]["contacto"].value;
            if (nome == ""
            || sigla == ""
            || nif == ""
            || endereco == ""
            || username == ""
            || email == ""
            || senha == ""
            || senhaConfirmar == ""
            || cvv == ""
            || dtExpiracao == ""
            || numCartao == ""
            || contacto == ""
            || senha != senhaConfirmar) {

                validNome()
                validSigla()
                validNif()
                validEndereco()
                validEmailLogin()
                //ordem
                validCVV()
                validDataCartaoExp()
                validNumcartao()
                validSenha()
                validSenhaConfirma()
                validUsuario()
                validContacto()

                //confirmar senha

                if (senha != senhaConfirmar) {

                    $("#liveToast").addClass("text-bg-danger")
                    $("#titlo-alerta").empty()
                    $("#titlo-alerta").text("Erro ao criar conta estudante")
                    $("#mensagem").empty()
                    $("#mensagem").text("A senha deve ser a mesma no campo confirmar senha")
                    toastBootstrap.show()
                    submit = false;
                }
                setTimeout(() => {
                    const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
                    $("#liveToast").addClass("text-bg-danger")
                    $("#liveToast").removeClass("text-bg-warning")
                    $("#titlo-alerta").empty()
                    $("#titlo-alerta").text("Erro ao criar conta estudante")
                    $("#mensagem").empty()
                    $("#mensagem").text("Verifique os campos se estão vazios ou devidamente preenchidos.")
                    toastBootstrap.show()
                    submit = false;
                }, 4000)
            } else if (dadosGlobais) {
                submit = false
                const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
                $("#liveToast").addClass("text-bg-warning")
                $("#liveToast").removeClass("text-bg-danger")
                $("#titlo-alerta").empty()
                $("#titlo-alerta").text("Erro ao criar conta estudante")
                $("#mensagem").empty()
                $("#mensagem").text("Já existe uma conta com este username")
                toastBootstrap.show()
            } else {
                submit = submitValidation;
                $("#liveToast").removeClass("text-bg-danger")
                $("#liveToast").removeClass("text-bg-warning")
                $("#titlo-alerta").empty()
                $("#titlo-alerta").text("MusalaLinkUp")
                $("#mensagem").empty()
                $("#mensagem").text("A tua conta foi criada com sucesso. Agora podes iniciar sua sessão.")
                toastBootstrap.show()
                setTimeout(() => {
                    document.myForm.submit()
                }, 5000)
            }
        })
        return submit;
    }
</script>
</body>
</html>