package com.sakidila.projectoic.utility;

import lombok.Builder;
import lombok.Getter;

@Getter
public class Nomenclatura {
    final Long ADMIN = Long.parseLong("1");
    final Long GESTOR_INSTITUTO = Long.parseLong("4");
    final Long GESTOR_CENTRO = Long.parseLong("7");
    final Long GESTOR_PROJECTO = Long.parseLong("8");
    final Long EMPRESA = Long.parseLong("5");
    final Long INVESTIGADOR_INDEPENDENTE = Long.parseLong("6");
    final Long INVESTIGADOR = Long.parseLong("3");
    final Long ESTUDANTE = Long.parseLong("2");
    final Long INSTITUTO = Long.parseLong("2");

}
