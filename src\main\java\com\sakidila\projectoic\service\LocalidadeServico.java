/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/springframework/Service.java to edit this template
 */
package com.sakidila.projectoic.service;

import com.sakidila.projectoic.entity.Localidade;
import com.sakidila.projectoic.repository.LocalidadeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 *
 * <AUTHOR> Mona
 */
@Service
public class LocalidadeServico {
    
    @Autowired
    private LocalidadeRepository repositorio;
    
    public Localidade salvar(Localidade localidade) {
        return repositorio.save(localidade);
    }
    
    public Localidade ApagarPelaChave(Long chave) {
        Optional<Localidade> optional = repositorio.findById(chave);
        if (optional.isPresent()) {
            repositorio.deleteById(chave);
            return optional.get();
        }
        return null;
    }
    
    public List<Localidade> listaLocalidadesPrimitiva() {
        return repositorio.findAllLocalidadesPrimitivas();
    }
    
    public List<Localidade> listaLocalidadesPelaChaveLocalidadePrimitiva(Long chave) {
        return repositorio.findAllLocalidadesByChaveLocalidadePrimitiva(chave);
    }
    
    public Localidade editar(Localidade localidade) {
        Optional<Localidade> optional = repositorio.findById(localidade.getId());
        if (optional.isPresent()) {
            repositorio.save(localidade);
            return optional.get();
        }
        return null;
        
    }
    
    public Localidade editarElementos(Localidade localidade) {
        Optional<Localidade> optional = repositorio.findById(localidade.getId());
        if (optional.isPresent()) {
            Localidade l = optional.get();
            
            l.setDesignacao(localidade.getDesignacao() != null ? localidade.getDesignacao() : l.getDesignacao());
            l.setLocalidadePai(localidade.getLocalidadePai() != null ? localidade.getLocalidadePai() : l.getLocalidadePai());
            repositorio.save(l);
            return l;
        }
        return null;
        
    }
    
    public Localidade findById(Long chave) {
        Optional<Localidade> optional = repositorio.findById(chave);
        if (optional.isPresent()) {
            return optional.get();
        }
        return null;
    }
}
