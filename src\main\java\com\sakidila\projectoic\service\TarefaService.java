package com.sakidila.projectoic.service;

import com.sakidila.projectoic.entity.Metodologia;
import com.sakidila.projectoic.entity.Projecto;
import com.sakidila.projectoic.entity.Tarefa;
import com.sakidila.projectoic.repository.EstadoRepository;
import com.sakidila.projectoic.repository.TarefaRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class TarefaService {

    @Autowired
    private TarefaRepository repository;

    @Autowired
    private MetodologiaService metodologiaService;

    @Autowired
    private EstadoRepository estadoRepository;

    public Tarefa salvar(Tarefa tarefa){
        return repository.save(tarefa);
    }

    public List<Tarefa> encontrarTarefasPeloIdInvestigador(Long id){
        return repository.findAllByInvestigadorId(id);
    }

    public List<Tarefa> encontrarTarefasPeloIdFase(Long id){
        return repository.findAllByFaseId(id);
    }

    public List<Tarefa> encontrarTarefasPelosProjectos(List<Projecto> projectos){
        List<Tarefa> tarefas = new ArrayList<>();

        for (Projecto projecto:projectos) {
            tarefas.addAll(projecto.getTarefas());
        }
        return tarefas;
    }

    public List<Tarefa> encontrarTarefasPeloIdInvestigador(Long id,List<Tarefa> tarefas){
        List<Tarefa> lista = new ArrayList<>();
        for (Tarefa tarefa: tarefas) {
            if(tarefa.getInvestigador().getId() == id)
                lista.add(tarefa);
        }
        return lista;
    }

    public List<Tarefa> encontrarTarefasPelaMetodologia(Long id){
            List<Metodologia> subfases = metodologiaService.listarFasesPeloIdMetodologia(id);
            List<Tarefa> tarefas = new ArrayList<>();
            if(subfases != null){
                for (Metodologia fase: subfases) {
                    tarefas.addAll(fase.getTarefas());
                }
                return tarefas;
            }
            return null;
    }

    public List<Tarefa> encontrarTarefasPeloIdProjecto(Long id){
        return repository.findAllByProjectoId(id);
    }

    public Tarefa deletar(Long id){
        Tarefa tarefa = repository.findById(id).get();
        if(tarefa != null){
            repository.deleteById(id);
            return tarefa;
        }
        return null;
    }

    public Tarefa validar(Long id, Long idEstado){
        Optional<Tarefa> optional = repository.findById(id);
        if(optional.isPresent()){
            optional.get().setEstado(estadoRepository.findById(idEstado).get());

            return salvar(optional.get());
        }
        return null;
    }

    public Page<?> convertListToPage(int page, int size, List<?> lista) {

        Pageable pageRequest = PageRequest.of(page-1, size);

        int start = (int) pageRequest.getOffset();
        int end = Math.min((start + pageRequest.getPageSize()), lista.size());

        return new PageImpl<>(lista.subList(start, end), pageRequest, lista.size());
    }

}
