package com.sakidila.projectoic.controller;

import com.sakidila.projectoic.entity.Conta;
import com.sakidila.projectoic.entity.EntidadeFaculdade;
import com.sakidila.projectoic.service.ContaService;
import com.sakidila.projectoic.service.EntidadeFaculdadeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/usuarioAjax")
public class UsuarioAjaxController {

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private ContaService service;

    @GetMapping("/{username}")
    public boolean verificarContaExistencia(@PathVariable String username){
            return service.verificarContaExistencia(username);
    }

    @GetMapping("/conta/{username}")
    public Conta verificarContaExistenciaObjecto(@PathVariable String username){
        return service.verificarContaExistenciaObjecto(username);
    }

    @GetMapping("/conta/{id}/{password}")
    public boolean decodificar(@PathVariable Long id, @PathVariable String password){
        Conta conta = service.findById(id);
        return passwordEncoder.matches(password, conta.getPassword());
    }

}
