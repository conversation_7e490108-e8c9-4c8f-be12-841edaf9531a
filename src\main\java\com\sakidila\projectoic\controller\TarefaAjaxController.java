package com.sakidila.projectoic.controller;

import com.sakidila.projectoic.entity.*;
import com.sakidila.projectoic.service.ProjectoContaService;
import com.sakidila.projectoic.service.ProjectoServico;
import com.sakidila.projectoic.service.TarefaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/tarefaAjax")
public class TarefaAjaxController {

    @Autowired
    private ProjectoServico projectoServico;

    @Autowired
    private TarefaService service;

    @GetMapping("/{idProjecto}")
    public ResponseEntity<List<?>> encontrarTarefasPeloIdProjecto(@PathVariable Long idProjecto) {

        List<Tarefa> tarefas = service.encontrarTarefasPeloIdProjecto(idProjecto);
        System.out.println(tarefas.get(0).getDesignacao());
        return new ResponseEntity<>(tarefas,HttpStatus.OK);
    }

}
