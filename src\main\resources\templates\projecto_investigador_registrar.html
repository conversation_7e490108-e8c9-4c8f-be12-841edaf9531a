<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">

    <!-- Importação do Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
            crossorigin="anonymous"></script>
    <style th:inline="text">
        body{
            font-family: Poppins;
        }
    </style>
    <title>Registra-se na Sakidila!</title>
</head>
<body class="bg-light row" style="overflow-x: hidden">


<!--Menu da App-->
<div class="col-xl-2 col-lg-3 col-md-4">
<div class="d-flex flex-column flex-shrink-0 p-3 bg-white shadow-sm position-fixed" style="width: 280px; height: 100vh">
    <a href="/sakidila_ic/portal/" class="d-flex align-items-center mb-3 mb-md-0 me-md-auto link-body-emphasis text-decoration-none">
        <img src="/img/logotipo.jpg" alt="Bootstrap" width="100"> <span class="fw-bold text-dark text-center">Gestão Acadêmica</span></a>
    </a>
    <hr>
    <ul class="nav nav-pills flex-column mb-auto">
        <li class="nav-item">
            <a href="/sakidila_ic/portal/" class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Inicio
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/portal/modulo_gestao" class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Dashboard
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/entidade/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == adminPerfil}" class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Entidade
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/entidade_faculdade/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Faculdade
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/usuario/gestor_instituicao_registrar"
               th:if="${dadoConta.tipoConta.designacao == adminPerfil}"
               class="nav-link link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#table"></use></svg>
                Gestor de Instituição
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/centro/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link  link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use h:href="/sakidila_ic/centro/registrar"></use></svg>
                Centro de Investigação
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/centro/investigador/associar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil}"
               class="nav-link  link-body-emphasis active">
                <svg class="bi pe-none me-2" width="16" height="16"><use h:href="/sakidila_ic/centro/registrar"></use></svg>
                Centro de Investigação
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/portal/projecto/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil
               or dadoConta.tipoConta.designacao == gestorProjectoPerfil
               or dadoConta.tipoConta.designacao == investigadorPerfil}"
               class="nav-link">
                <svg class="bi pe-none me-2" width="16" height="16"><use href="/sakidila_ic/centro/registrar"></use></svg>
                Projectos
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/portal/projecto/pendentes/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link">
                <svg class="bi pe-none me-2" width="16" height="16"><use href="/sakidila_ic/centro/registrar"></use></svg>
                Projectos pendentes
            </a>
        </li>
        <li>
            <a href="#" class="nav-link link-body-emphasis"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}">
                <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#table"></use></svg>
                Estudante
            </a>
        </li>
        <li>
            <a href="#" class="nav-link link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#people-circle"></use></svg>
                Relatórios
            </a>
        </li>
    </ul>
    <hr>
    <div class="dropdown">
        <a href="#" class="d-flex align-items-center link-body-emphasis text-decoration-none dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
            <img th:if="${conta == userDefault}" th:src="@{/img/user.png}" alt="mdo" width="32" height="32" class="rounded-circle">
            <img th:if="${conta != null}" th:src="${dadoConta.foto}" alt="mdo" width="32" height="32" class="rounded-circle">
            <strong>[[${dadoConta.username}]]</strong>
        </a>
        <ul class="dropdown-menu text-small shadow">
            <li><a th:if="${conta == userDefault}" class="dropdown-item" href="/sakidila_ic/usuario/login">Entrar</a></li>
            <li><a th:if="${conta != null}" class="dropdown-item" href="">Meu Perfil</a></li>
            <li><a th:if="${conta != null}" class="dropdown-item" href="/logout">Sair</a></li>
        </ul>
    </div>
</div>
</div>

<div class="col-xl-10 col-lg-9 col-md-8">
<div class="container">
    <div class="d-flex flex-row mt-4 mb-5 justify-content-end">
        <a href="/sakidila_ic/portal/projecto/registrar/1" class="me-3" style="text-decoration: none; color: #7c6aa2">Voltar</a>
        <a class="" style="text-decoration: none; color: #7c6aa2" data-bs-target="#associarMembros" data-bs-toggle="modal" href="#">Adicionar participante</a>
    </div>

    <!-- Modal -->
    <form method="post" th:action="@{/sakidila_ic/portal/investigador/associar}" >
    <div class="modal fade" id="associarMembros" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="" id="staticBackdropLabel"><h5 class="modal-title fs-4 fw-bold" style="color: rgba(158,158,151)">Adicionar <i class="fw-bold text-dark">Membros do Centro de Investigação ao projecto!</i></h5></h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <fieldset class=" px-5 py-4  rounded mb-3">
                        <legend class="text-secondary">Meus projectos</legend>
                        <div class="row mb-3">
                            <div class="col">
                                <div class="form-floating ">
                                    <select class="form-select" placeholder="" name="idProjecto" aria-label="Apelido" aria-describedby="apelido">
                                        <option th:each="item : ${projectos}" th:if="${dadoConta.tipoConta.designacao != gestorCentroPerfil and item.gestor.id == dadoConta.id and !item.disabled}" th:value="${item.id}" th:text="${item.designacao}"/>
                                        <option th:each="item : ${projectos}" th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil and !item.disabled}" th:value="${item.id}" th:text="${item.designacao}"/>
                                    </select>
                                    <label class="label">Selecione o projecto...</label>
                                </div>
                            </div>
                        </div>
                        <legend class="text-secondary">Membros</legend>
                        <div class="row mb-3">
                            <div class="col">
                                <div class="form-floating ">
                                    <select class="form-select" placeholder="" name="id" aria-label="Apelido" aria-describedby="apelido" >
                                        <option th:each="item : ${investigadores}" th:if="${item.id != dadoConta.id and item.tipoConta.designacao != gestorCentroPerfil}" th:value="${item.id}" th:text="${item.pessoa.nome+' '+item.pessoa.apelido}"/>
                                    </select>
                                    <label class="label">Adicione membros ao projecto...</label>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end">

                        </div>
                    </fieldset>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                    <button class="btn btn-primary " type="submit">Adicionar</button>
                </div>
            </div>
        </div>
    </div>
    </form>


    <h5 class="display-6 mt-1 mb-1 fw-bold" style="color: rgba(158,158,151)">Meus projectos e <i class="fw-bold text-dark">Investigadores do centro envolvidos</i></h5>
    <hr>
        <fieldset class="bg-white px-5 py-4 shadow-sm rounded mb-3">
            <legend class="text-secondary"> </legend>
            <table class="table">
                <tr>
                    <th scope="col">Projecto</th>
                    <th scope="col">Investigador</th>
                    <th scope="col">Remover</th>
                </tr>
                <tr th:each="item : ${membros}" th:if="${!item.chave.conta.disabled and  !item.disabled}">
                    <td th:if="${dadoConta.id == item.chave.projecto.gestor.id}" th:text="${item.chave.projecto.designacao}"></td>
                    <td th:if="${dadoConta.id == item.chave.projecto.gestor.id}" th:text="${item.chave.conta.pessoa.nome+' '+item.chave.conta.pessoa.apelido}"></td>
                    <td th:if="${dadoConta.id == item.chave.projecto.gestor.id}">
                        <a class="btn btn-danger btn-sm" th:href="@{/sakidila_ic/portal/desativar/{idProjecto}/{idConta}(idProjecto=${item.chave.projecto.id},idConta=${item.chave.conta.id})}">Remover</a>
                    </td>
                </tr>
            </table>
            <div class="d-flex justify-content-center">
                <nav class="Page navigation example" th:if="${totalPaginas > 1}">
                    <ul class="pagination">
                        <li class="page-item"><a class="page-link">Total de projectos: [[${totalItems}]]</a></li>
                        <div th:each="i : ${#numbers.sequence(1,totalPaginas)}">
                            <li class="page-item">
                                <a class="page-link" th:if="${paginaActual != i}"
                                   th:href="@{'/sakidila_ic/portal/projecto/investigador/associar/'+ ${i}}">[[${i}]]</a>
                                <a class="page-link" th:unless="${paginaActual != i}">[[${i}]]</a> &nbsp; &nbsp;
                            </li>
                        </div>
                        <li class="page-item">
                            <a class="page-link" th:if="${paginaActual < totalPaginas}"
                               th:href="@{'/sakidila_ic/portal/projecto/investigador/associar/'+${paginaActual + 1}}">Próximo</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" th:if="${paginaActual < totalPaginas}"
                               th:href="@{'/sakidila_ic/portal/projecto/investigador/associar/'+${paginaActual + 1}}">Ultimo</a>
                            <a class="page-link" th:unless="${paginaActual < totalPaginas}">Ultimo</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </fieldset>

</div>
</div>
</body>
</html>