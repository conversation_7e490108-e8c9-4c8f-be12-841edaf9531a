<!DOCTYPE html>
<html lang="en">
<head>

    <!-- Importação do Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
            crossorigin="anonymous"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
    <style th:inline="text">
        body{

            font-family: Poppins;
            overflow-x: hidden;
        }

        .menu{
            position: -webkit-sticky;
            position: sticky;
            top: 0;
            padding: 10px;
        }


    </style>
    <title>Registra-se na Sakidila!</title>
</head>
<body class="bg-light  row">

<!--Menu da App-->
<div class="col-xl-2 col-lg-3 col-md-4">
    <div class="d-flex flex-column flex-shrink-0 p-3 bg-white shadow-sm position-fixed" style=" width: 280px; height: 100vh">
        <a href="/sakidila_ic/portal/" class="d-flex align-items-center mb-3 mb-md-0 me-md-auto link-body-emphasis text-decoration-none">
            <img src="/img/logotipo.jpg" alt="Bootstrap" width="100"> <span class="fw-bold text-dark text-center">Gestão Acadêmica</span></a>
        </a>
        <hr>
        <ul class="nav nav-pills flex-column mb-auto">
            <li class="nav-item">
                <a href="/sakidila_ic/portal/" class="nav-link link-body-emphasis" aria-current="page">
                    <svg class="bi pe-none me-2" width="16" height="16"></svg>
                    Inicio
                </a>
            </li>
            <li class="nav-item">
                <a href="/sakidila_ic/portal/modulo_gestao" th:if="${ dadoConta.tipoConta.designacao == gestorProjectoPerfil}" class="nav-link link-body-emphasis" aria-current="page">
                    <svg class="bi pe-none me-2" width="16" height="16"></svg>
                    Dashboard
                </a>
            </li>
            <li class="nav-item" th:if="${dadoConta.tipoConta != null}">
                <a href="/sakidila_ic/entidade/registrar/1"
                   th:if="${dadoConta.tipoConta.designacao == adminPerfil}" class="nav-link link-body-emphasis" aria-current="page">
                    <svg class="bi pe-none me-2" width="16" height="16"></svg>
                    Entidade
                </a>
            </li>
            <li class="nav-item">
                <a href="/sakidila_ic/entidade_faculdade/registrar/1"
                   th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
                   class="nav-link link-body-emphasis" aria-current="page">
                    <svg class="bi pe-none me-2" width="16" height="16"></svg>
                    Faculdade
                </a>
            </li>
            <li>
                <a href="/sakidila_ic/usuario/gestor_instituicao_registrar"
                   th:if="${dadoConta.tipoConta.designacao == adminPerfil}"
                   class="nav-link link-body-emphasis">
                    <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#table"></use></svg>
                    Gestor de Instituição
                </a>
            </li>
            <li>
                <a href="/sakidila_ic/centro/registrar/1"
                   th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
                   class="nav-link  link-body-emphasis">
                    <svg class="bi pe-none me-2" width="16" height="16"><use h:href="/sakidila_ic/centro/registrar"></use></svg>
                    Centro de Investigação
                </a>
            </li>
            <li>
                <a href="/sakidila_ic/centro/investigador/associar/1"
                   th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil}"
                   class="nav-link  link-body-emphasis ">
                    <svg class="bi pe-none me-2" width="16" height="16"><use h:href="/sakidila_ic/centro/registrar"></use></svg>
                    Centro de Investigação
                </a>
            </li>
            <li>
                <a href="/sakidila_ic/portal/projecto/registrar/1"
                   th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil
               or dadoConta.tipoConta.designacao == gestorProjectoPerfil
               or dadoConta.tipoConta.designacao == investigadorPerfil
               or dadoConta.tipoConta.designacao == estudantePerfil
               or dadoConta.tipoConta.designacao == investigadorIndependentePerfil}"
                   class="nav-link active">
                    <svg class="bi pe-none me-2" width="16" height="16"><use href="/sakidila_ic/centro/registrar"></use></svg>
                    Projectos
                </a>
            </li>
            <li>
                <a href="/sakidila_ic/portal/projecto/pendentes/1"
                   th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
                   class="nav-link">
                    <svg class="bi pe-none me-2" width="16" height="16"><use href="/sakidila_ic/centro/registrar"></use></svg>
                    Projectos pendentes
                </a>
            </li>
            <li class="nav-item">
                <a href="/sakidila_ic/portal/projecto/registrar/1"
                   th:if="${dadoConta.tipoConta.designacao == empresaPerfil}"
                   class="nav-link active" aria-current="page">
                    <svg class="bi pe-none me-2" width="16" height="16"></svg>
                    Meus investimentos
                </a>
            </li>
            <li>
                <a href="#" class="nav-link link-body-emphasis"
                   th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}">
                    <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#table"></use></svg>
                    Estudante
                </a>
            </li>
        </ul>
        <hr>
        <div class="dropdown">
            <a href="#" class="d-flex align-items-center link-body-emphasis text-decoration-none dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                <img th:if="${conta == userDefault}" th:src="@{/img/user.png}" alt="mdo" width="32" height="32" class="rounded-circle">
                <img th:if="${conta != null}" th:src="${dadoConta.foto}" alt="mdo" width="32" height="32" class="rounded-circle">
                <strong>[[${dadoConta.username}]]</strong>
            </a>
            <ul class="dropdown-menu text-small shadow">
                <li><a th:if="${conta == userDefault}" class="dropdown-item" href="/sakidila_ic/usuario/login">Entrar</a></li>
                <li><a th:if="${conta != null}" class="dropdown-item" href="">Meu Perfil</a></li>
                <li><a th:if="${conta != null}" class="dropdown-item" href="/logout">Sair</a></li>
            </ul>
        </div>
    </div>

</div>


<div class="col-xl-10 col-lg-9 col-md-8">
    <div class="container">
        <div class="d-flex flex-row mt-4 justify-content-end">
            <a href="#novo" class="me-3" style="text-decoration: none; color: #7c6aa2"  th:if="${dadoConta.tipoConta.designacao != empresaPerfil }" data-bs-toggle="modal" data-bs-target="#criarProjecto">Adicionar projecto</a>
            <a href="/sakidila_ic/portal/projecto/investigador/associar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorProjectoPerfil}" class=" me-3" style="text-decoration: none; color: #7c6aa2">Adicionar membro ao projecto</a>
            <!--<button th:if="${dadoConta.tipoConta.designacao == gestorProjectoPerfil}" class="" style="border:none; background-color:transparent;text-decoration: none; color: #7c6aa2" data-bs-toggle="modal" data-bs-target="#exampleModal">Nova tarefa</button>-->

        </div>


        <h5 class="display-6 mt-1 mb-1 fw-bold" th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil}" style="color: rgba(158,158,151)">Projectos do <i class="fw-bold text-dark">[[${dadoConta.centro.designacao}]]</i></h5>

        <h5 class="display-6 mt-1 mb-1 fw-bold" th:if="${dadoConta.tipoConta.designacao == gestorProjectoPerfil
               or dadoConta.tipoConta.designacao == investigadorPerfil
               or dadoConta.tipoConta.designacao == estudantePerfil or dadoConta.tipoConta.designacao == investigadorIndependentePerfil}" style="color: rgba(158,158,151)">Meus projectos <i class="fw-bold text-dark"></i></h5>
        <h5 class="display-6 mt-1 mb-1 fw-bold" th:if="${dadoConta.tipoConta.designacao == empresaPerfil}" style="color: rgba(158,158,151)">Projectos Investidos <i class="fw-bold text-dark"></i></h5>
        <hr>
        <fieldset class="bg-white px-5 py-4 shadow-sm rounded mb-3" style="height: 80vh">
            <table class="table table-hover" th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil
               or dadoConta.tipoConta.designacao == gestorProjectoPerfil
               or dadoConta.tipoConta.designacao == investigadorPerfil
               or dadoConta.tipoConta.designacao == investigadorIndependentePerfil
               or dadoConta.tipoConta.designacao == estudantePerfil}">
                <tr>
                    <th scope="col">Projecto</th>
                    <th scope="col" th:if="${dadoConta.tipoConta.designacao != estudantePerfil and dadoConta.tipoConta.designacao != gestorCentroPerfil and dadoConta.tipoConta.designacao != investigadorIndependentePerfil}">Função</th>
                    <th scope="col" th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil}">Gestor de projecto</th>
                    <th scope="col" th:if="${dadoConta.tipoConta.designacao != estudantePerfil and dadoConta.tipoConta.designacao != investigadorIndependentePerfil}">Centro</th>
                    <th scope="col" th:if="${dadoConta.tipoConta.designacao == gestorProjectoPerfil
                    or dadoConta.tipoConta.designacao == investigadorPerfil
                    or dadoConta.tipoConta.designacao == investigadorIndependentePerfil}">Tarefas</th>
                    <th scope="col" class="col-1">Detalhes</th>
                    <th scope="col" th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil
                    or dadoConta.tipoConta.designacao == investigadorIndependentePerfil}" class="col-1">solicitações</th>
                    <th scope="col" th:if="${dadoConta.tipoConta.designacao == gestorProjectoPerfil
                    or dadoConta.tipoConta.designacao == gestorCentroPerfil
                    or dadoConta.tipoConta.designacao == estudantePerfil
                    or dadoConta.tipoConta.designacao == investigadorIndependentePerfil }" class="col-1">Remover</th>
                </tr>
                <tr th:each="item : ${projectos}" th:if="${!item.disabled}" id="projectos">
                    <td th:text="${item.designacao}"></td>
                    <td th:if="${dadoConta.tipoConta.designacao != estudantePerfil and dadoConta.tipoConta.designacao != investigadorIndependentePerfil and item.gestor.pessoa.nome == dadoConta.pessoa.nome}">Gestor de projecto</td>
                    <td th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil}" th:text="${item.gestor.pessoa.nome+' '+item.gestor.pessoa.apelido}"></td>

                    <td th:if="${dadoConta.tipoConta.designacao != estudantePerfil
                    and item.gestor.pessoa.nome != dadoConta.pessoa.nome
                    and dadoConta.tipoConta.designacao != gestorCentroPerfil}">Colaborador</td>

                    <td th:if="${dadoConta.tipoConta.designacao != estudantePerfil and dadoConta.tipoConta.designacao != investigadorIndependentePerfil}" >[[${item.centro.designacao}]]</td>

                    <td th:if="${dadoConta.tipoConta.designacao == investigadorPerfil
                    or dadoConta.tipoConta.designacao == gestorProjectoPerfil
                    or dadoConta.tipoConta.designacao == investigadorIndependentePerfil}" class="col-1"><a type="button" class="btn btn-primary btn-sm shadow-sm"
                                                                                                                                                                    th:if="${item.ehDesenvolvimento}"
                                                                                                                                                                    th:href="@{/sakidila_ic/portal/projecto/tarefa/{id}/1(id=${item.id})}">Abrir</a></td>
                    <td>

                        <!--Modal dados-->
                        <input type="hidden" name="idProjecto" th:value="${item.id}">
                        <input type="hidden" name="imagem" th:value="${item.logotipo}">
                        <input type="hidden" name="designacao" th:value="${item.designacao}">
                        <input type="hidden" name="descricao" th:value="${item.descricao}">
                        <input type="hidden" name="tipoProjecto" th:value="${item.tipoProjecto.designacao}">
                        <input type="hidden" name="gestorProjecto" th:value="${item.gestor.pessoa.nome+' '+item.gestor.pessoa.apelido}">
                        <input type="hidden" name="orcamento" th:value="${item.orcamentoTexto}">
                        <input type="hidden" name="orcamentoNumerico" th:value="${item.orcamento}">
                        <input type="hidden" name="ehDesenvolvimento" th:value="${item.ehDesenvolvimento}">
                        <input type="hidden" name="ehDeInvestimento" th:value="${item.ehDeInvestimento}">
                        <input type="hidden" name="Metodologia" th:value="${item.metodologia.designacao}">
                        <input type="hidden" name="aprovacao" th:value="${item.ehAprovado}">
                        <input type="hidden" name="centro" th:if="${dadoConta.tipoConta.designacao == gestorProjectoPerfil or dadoConta.tipoConta.designacao == gestorCentroPerfil }" th:value="${item.centro.designacao}">
                        <input type="hidden" name="dataCriacao" th:value="${item.dataCriacao}">
                        <input type="hidden" name="documento" th:if="${item.documento != null}"
                               th:value="${item.documento.idDocumento}">
                        <input type="hidden" name="docNome" th:if="${item.documento != null}"
                               th:value="${item.documento.designacao}">

                        <button type="button" class="btn btn-primary btn-sm detalhe" data-bs-toggle="modal" data-bs-target="#modalVer">Ver</button>
                    </td>
                    <td th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil or dadoConta.tipoConta.designacao == investigadorIndependentePerfil}">

                        <!--Informações para carregar as solicitações dado o projecto-->

                        <input type="hidden" name="idProjecto" th:value="${item.id}">
                        <input type="hidden" name="designacao" th:value="${item.designacao}">

                        <button type="button" class="btn btn-warning btn-sm load-solicitacoes" data-bs-toggle="modal" data-bs-target="#solicitacoes">Abrir</button>
                    </td>
                    <td th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil
                    or dadoConta.tipoConta.designacao == gestorProjectoPerfil
                    or dadoConta.tipoConta.designacao == estudantePerfil
                    or dadoConta.tipoConta.designacao == investigadorIndependentePerfil}"><a type="button" class="btn btn-danger btn-sm" th:href="@{/sakidila_ic/portal/desativar/{idProjecto}(idProjecto=${item.id})}">Remover</a></td>
                </tr>
            </table>
            <table class="table table-hover" th:if="${dadoConta.tipoConta.designacao == empresaPerfil}">
                <tr>
                    <th scope="col">Projecto</th>
                    <th scope="col" th:if="${dadoConta.tipoConta.designacao != estudantePerfil and dadoConta.tipoConta.designacao != empresaPerfil}">Centro</th>
                    <th scope="col">Detalhes</th>
                    <th scope="col">Tarefas</th>
                    <th scope="col" class="col-3">Acção</th>
                </tr>
                <tr th:each=" item : ${projectosInvestidos}" id="projectosInvestidos">
                    <td>[[${item.id.projecto.designacao}]]
                        <i th:if="${item.id.projecto.disabled}" class="text-danger ms-1">
                            • Projecto descontinuado!</i></td>
                    <td>

                        <!--Modal dados-->
                        <input type="hidden" name="idProjecto" th:value="${item.id.projecto.id}">
                        <input type="hidden" name="imagem" th:value="${item.id.projecto.logotipo}">
                        <input type="hidden" name="designacao" th:value="${item.id.projecto.designacao}">
                        <input type="hidden" name="descricao" th:value="${item.id.projecto.descricao}">
                        <input type="hidden" name="tipoProjecto" th:value="${item.id.projecto.tipoProjecto.designacao}">
                        <input type="hidden" name="gestorProjecto" th:value="${item.id.projecto.gestor.pessoa.nome+' '+item.id.projecto.gestor.pessoa.apelido}">
                        <input type="hidden" name="orcamento" th:value="${item.id.projecto.orcamentoTexto}">
                        <input type="hidden" name="orcamentoNumerico" th:value="${item.id.projecto.orcamento}">
                        <input type="hidden" name="ehDesenvolvimento" th:value="${item.id.projecto.ehDesenvolvimento}">
                        <input type="hidden" name="ehDeInvestimento" th:value="${item.id.projecto.ehDeInvestimento}">
                        <input type="hidden" name="Metodologia" th:value="${item.id.projecto.metodologia.designacao}">
                        <input type="hidden" name="aprovacao" th:value="${item.id.projecto.ehAprovado}">
                        <input type="hidden" name="centro" th:if="${dadoConta.tipoConta.designacao == gestorProjectoPerfil or dadoConta.tipoConta.designacao == gestorCentroPerfil }" th:value="${item.id.projecto.centro.designacao}">
                        <input type="hidden" name="dataCriacao" th:value="${item.id.projecto.dataCriacao}">
                        <input type="hidden" name="documento" th:if="${item.id.projecto.documento != null}"
                               th:value="${item.id.projecto.documento.idDocumento}">
                        <input type="hidden" name="docNome" th:if="${item.id.projecto.documento != null}"
                               th:value="${item.id.projecto.documento.designacao}">

                        <button type="button" class="btn btn-primary btn-sm detalhe" data-bs-toggle="modal" data-bs-target="#modalVer">Ver</button>
                    </td>
                    <td><a type="button" class="btn btn-primary btn-sm shadow-sm"
                           th:if="${item.id.projecto.ehDesenvolvimento != null and item.id.projecto.ehDesenvolvimento and item.ehAprovado}"
                           th:href="@{/sakidila_ic/portal/projecto/tarefa/{id}/1(id=${item.id.projecto.id})}">Abrir</a>

                        <p th:if="${!item.ehAprovado}" class="m-0 text-warning fw-normal">
                            Aguarde a aprovação do investimento
                        </p>
                    </td>

                    <td th:if="${!item.ehAprovado}">
                        <a type="button" class="btn btn-danger btn-sm shadow-sm"
                           th:href="@{'/sakidila_ic/investidor/delete/'+${item.id.projecto.id}+'/'+${item.id.investidor.id}}">Cancelar solicitação</a></td>
                    <td th:if="${item.ehAprovado}" class="text-success">
                        Investido
                        <a type="button" th:if="${item.id.projecto.disabled}" class="btn btn-danger btn-sm shadow-sm ms-2"
                           th:href="@{'/sakidila_ic/investidor/delete/'+${item.id.projecto.id}+'/'+${item.id.investidor.id}}">Cancelar solicitação</a></td></td>
                </tr>
            </table>
            <div class="d-flex justify-content-center">
                <nav class="Page navigation example" th:if="${totalPaginas > 1}">
                    <ul class="pagination">
                        <li class="page-item"><a class="page-link">Total de projectos: [[${totalItems}]]</a></li>
                        <div th:each="i : ${#numbers.sequence(1,totalPaginas)}">
                            <li class="page-item">
                                <a class="page-link" th:if="${paginaActual != i}"
                                   th:href="@{'/sakidila_ic/portal/projecto/registrar/'+ ${i}}">[[${i}]]</a>
                                <a class="page-link" th:unless="${paginaActual != i}">[[${i}]]</a> &nbsp; &nbsp;
                            </li>
                        </div>
                        <li class="page-item">
                            <a class="page-link" th:if="${paginaActual < totalPaginas}"
                               th:href="@{'/sakidila_ic/portal/projecto/registrar/'+${paginaActual + 1}}">Próximo</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" th:if="${paginaActual < totalPaginas}"
                               th:href="@{'/sakidila_ic/portal/projecto/registrar/'+${paginaActual + 1}}">Ultimo</a>
                            <a class="page-link" th:unless="${paginaActual < totalPaginas}">Ultimo</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </fieldset>
        <form method="post" th:action="@{/sakidila_ic/portal/projecto/salvar}" name="novo" id="novo" enctype="multipart/form-data" onsubmit="return validateForm()" required>
        <div class="modal fade" th:if="${dadoConta.tipoConta.designacao != empresaPerfil}" id="criarProjecto" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-scrollable modal-fullscreen">

                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="" id="staticBackdropLabel"><h5 class="modal-title fs-4 fw-bold" style="color: rgba(158,158,151)">Criar <i class="fw-bold text-dark">Projecto!</i></h5></h1>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">

                            <fieldset class="bg-white px-5 py-4 mb-3 mx-5">
                                <div class="mb-3" th:if="${dadoConta.centro != null}">
                                    <div th:if="${dadoConta.tipoConta.designacao == gestorProjectoPerfil
                                                or dadoConta.tipoConta.designacao == gestorCentroPerfil
                                                or dadoConta.tipoConta.designacao == investigadorPerfil}">
                                        <h4 class="fw-bolder" style="color: #AB81A3">Centro de investigação</h4>
                                        <h5 class="form-label ">[[${dadoConta.centro.designacao}]]</h5>
                                        <input type="hidden" name="centro.id" th:value="${dadoConta.centro.id}">
                                    </div>
                                </div>
                                <legend class="text-secondary">Dados </legend>

                                <div class="row mb-3 ">
                                    <div class="col">
                                        <div class="row mb-4">
                                            <div class="w-100">
                                                <div class="border border-1 p-3 rounded-4">
                                                    <label class="form-label fw-bolder">Adicione o logotipo da Empresa</label>
                                                    <input class="form-control" type="file" id="imagem" name="imagem">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mb-4">
                                            <div class="">
                                                <label class="form-label  fw-bolder">Designação</label>
                                                <input type="text" id="designacaoValidate" onfocusout="validDesignacaoP()" class="form-control" name="designacao">
                                                <div class="valid-feedback">

                                                </div>
                                                <div class="invalid-feedback">
                                                    Preencher o campo com a designação da empresa.
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mb-4">
                                            <div class="">
                                                <label class="form-label fw-bolder">Tipo de Projecto</label>
                                                <select class="form-select" name="tipoProjecto.id">
                                                    <option th:each="item : ${ tipoProjectos}" th:value="${item.id}" th:text="${item.designacao}"></option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row mb-4" th:if="${dadoConta.tipoConta.designacao != estudantePerfil}">
                                            <!--<div class="" th:if="${dadoConta.tipoConta.designacao != gestorProjectoPerfil}">
                                                <label class="form-label fw-bolder">Centro de Investigação</label>
                                                <select class="form-select" name="centro.id">
                                                    <option th:each="item : ${centros}" th:value="${item.id}" th:text="${item.designacao}"></option>
                                                </select>
                                            </div>-->

                                        </div>
                                        <div class="row mb-4" th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil}">
                                            <div class="">
                                                <label class="form-label fw-bolder">Gestor do projecto</label>
                                                <select class="form-select" name="gestor.id">
                                                    <option th:each="item : ${investigadores}"
                                                            th:if="${item.tipoConta.designacao != gestorCentroPerfil and item.centro.id == dadoConta.centro.id}" th:value="${item.id}" th:text="${item.pessoa.nome+' '+item.pessoa.apelido}"></option>
                                                </select>
                                            </div>
                                        </div>
                                        <div th:if="${dadoConta.tipoConta.designacao == gestorProjectoPerfil
                                        or dadoConta.tipoConta.designacao == estudantePerfil
                                        or dadoConta.tipoConta.designacao == investigadorIndependentePerfil}" class="">
                                            <input type="hidden" name="gestor.id" th:value="${dadoConta.id}">
                                        </div>
                                        <div class="row mb-2" th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil
                    or dadoConta.tipoConta.designacao == gestorProjectoPerfil or dadoConta.tipoConta.designacao == investigadorIndependentePerfil}">
                                            <div class="form-check form-switch ms-3 ">
                                                <input class="form-check-input" type="checkbox" name="ehDesenvolvimento" role="switch" id="flexSwitchCheckDefault">
                                                <label class="form-check-label" for="flexSwitchCheckDefault">Projecto em desenvolvimento?</label>
                                            </div>
                                        </div>
                                        <div class="row mb-4">
                                            <div class="">
                                                <label class="form-label fw-bolder">Metódologia</label>
                                                <select class="form-select" name="metodologia.id">
                                                    <option th:each="item : ${metodologias}" th:value="${item.id}" th:text="${item.designacao}"
                                                    th:selected="${item.id == 7}"></option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row mb-2" th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil
                    or dadoConta.tipoConta.designacao == gestorProjectoPerfil or dadoConta.tipoConta.designacao == investigadorIndependentePerfil}">
                                            <div class="form-check form-switch ms-3 ">
                                                <input class="form-check-input" type="checkbox" name="ehDeInvestimento" role="switch">
                                                <label class="form-check-label" for="flexSwitchCheckDefault">Projecto de investimento?</label>
                                            </div>
                                        </div>
                                        <div class="row mb-4">
                                            <div class="">
                                                <div class="">
                                                    <label class="form-label fw-bolder">Orçamento</label>
                                                    <input type="number" class="form-control" id="orcamentoValidate" onfocusout="validOrcamentoP()" name="orcamento">
                                                    <div class="valid-feedback">

                                                    </div>
                                                    <div class="invalid-feedback">
                                                        Preencher o campo com o orçamento da empresa.
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col">
                                        <div class="col">
                                            <div class="">
                                                <label class="form-label fw-bolder">Descrição</label>
                                                <textarea type="text" class="form-control" id="descricaoValidate" onfocusout="validDescricaoP()" name="descricao" style="height: 303px" placeholder=""></textarea>
                                                <div class="valid-feedback">

                                                </div>
                                                <div class="invalid-feedback">
                                                    Preencher o campo com a descrição da empresa.
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col mb-4 mt-2">
                                            <label for="formFile" class="form-label fw-bolder">Carregar o relatório - pdf</label>
                                            <input class="form-control" type="file" id="formFile" name="ficheiro">
                                        </div>
                                        <div class="col">
                                            <div class="">
                                                <label class="form-label fw-bolder">Data de criação</label>
                                                <input class="form-control" id="dataValidate" onfocusout="validDataCriacaoP()" name="data" type="date">
                                                <div class="valid-feedback">

                                                </div>
                                                <div class="invalid-feedback">
                                                    Preencher o campo com a data de criação da empresa.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>




                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Voltar</button>
                        <button class="btn btn-warning me-2 px-3 shadow-sm" type="reset">Limpar</button>
                        <button th:if="${!dadoConta.ehSubscrito}" class="btn btn-primary shadow-sm" type="button" data-bs-toggle="modal" data-bs-target="#pagamentoModal">Criar</button>
                        <button th:if="${dadoConta.ehSubscrito}" class="btn btn-primary shadow-sm" type="submit">Criar</button>
                    </div>
                </div>

            </div>
        </div>
        </form>


        <!--Modal de solicitações-->
        <form>
        <div class="modal fade" id="solicitacoes" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">

                <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h1 class="modal-title fs-5" id="exampleModalLabel">Solicitações de investimento</h1>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <h5 class="card-title fs-6 mb-1 mt-3" style="color:#AB81A3">Projecto</h5>
                            <p id="projecto-nome"></p>

                            <table id="lista-solicitacoes" class="table table-hover">
                                <tr>
                                    <th scope="col" class="col-10">Investidor</th>
                                    <th scope="col">Acção</th>
                                    <th scope="col"></th>
                                    <th scope="col"></th>
                                </tr>
                            </table>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                        </div>
                    </div>
                </div>

        </div>
        </form>

        <!--Modal de negociação-->
        <form method="post" th:action="@{/sakidila_ic/portal/projecto/tarefa/criar}" >
        <div class="modal fade" id="negociacao" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">

                <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h1 class="modal-title fs-5">Processo de Negociação</h1>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <h4 class="card-title fs-5 mb-1 mt-3" style="color:#AB81A3">Projecto</h4>
                            <p id="projecto-nome-negociar"></p>
                            <input type="hidden" id="idProjectoNegociar">
                            <h5 class="card-title fs-5 mb-1 mt-3" style="color:#AB81A3">Meios de negociação com o investidor</h5>
                            <p id="investidor-nome-negociar" class="m-0 my-1"></p>
                            <p id="investidor-tel" class="m-0 my-1"></p>
                            <p id="investidor-email" class="m-0 mt-1 mb-5"></p>

                        </div>
                        <div class="modal-footer">
                            <a class="btn btn-primary" id="buttonAceitarProposta">Aceitar</a>
                            <a class="btn btn-danger" id="buttonNegarProposta">Rejeitar</a>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                        </div>
                    </div>
                </div>
        </div>
        </form>

       <!--Modal de nova tarefa-->
        <!--  <form method="post" th:action="@{/sakidila_ic/portal/projecto/tarefa/criar}" >
         <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">

                 <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered">
                     <div class="modal-content">
                         <div class="modal-header">
                             <h1 class="modal-title fs-5" id="exampleModalLabel">Nova tarefa</h1>
                             <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                         </div>
                         <div class="modal-body">

                             <div class="mb-3">
                                 <label for="exampleFormControlInput1" class="form-label fw-bolder">Descrição</label>
                                 <input type="text" class="form-control" id="exampleFormControlInput1" name="designacao" placeholder="Nova tarefa">
                             </div>

                             <div class="mb-3">
                                 <label for="exampleFormControlInput1" class="form-label fw-bolder">Especifique o projecto</label>
                                 <select class="form-select" id="projecto" name="projecto.id">
                                     <option>Selecione o projecto</option>
                                     <option th:each="item : ${projectos}" th:if="${item.gestor.id == dadoConta.id}" th:text="${item.designacao}" th:value="${item.id}"></option>
                                 </select>
                             </div>
                             <div class="mb-3">
                                 <label for="exampleFormControlInput1" id="metodologia" class="form-label fw-bolder">Metodologia •</label>
                                 <select class="form-select" id="fases" name="fase.id">
                                     <option>Selecione a fase</option>
                                 </select>
                             </div>
                             <div class="mb-3">
                                 <label for="exampleFormControlInput1" class="form-label fw-bolder">Atribuir tarefa</label>
                                 <select class="form-select" id="investigadores" name="investigador.id">
                                     <option>Selecione o investigador</option>
                                 </select>
                             </div>
                             <div class="mb-3 row">
                                 <div class="col">
                                     <label for="exampleFormControlInput1" class="form-label fw-bolder">Começa em</label>
                                     <input type="date" class="form-control" id="dataInicio" name="dataInicio">
                                 </div>
                                 <div class="col">
                                     <label for="exampleFormControlInput1" class="form-label fw-bolder">Termina em</label>
                                     <input type="date" class="form-control" id="datafim" name="dataFim">
                                 </div>
                             </div>

                         </div>
                         <div class="modal-footer">
                             <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                             <input type="submit"  class="btn btn-primary" value="Criar">
                         </div>
                     </div>
                 </div>

         </div>
         </form>-->

         <!--Modal de ver ou editar um projecto-->
        <form method="post" th:action="@{/sakidila_ic/portal/projecto/salvar}" name="myFormEditar" id="myFormEditar" enctype="multipart/form-data" onsubmit="return validateFormEditar()" required>
        <div class="modal fade" id="modalVer" tabindex="-1" aria-labelledby="ModalLabelVer" aria-hidden="true">
            <div class="modal-dialog modal-dialog-scrollable">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="ModalLabelVer">Detalhes do projeto</h1>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" id="idProjectoAux" name="id">
                        <div th:if="${dadoConta.tipoConta.designacao == gestorProjectoPerfil
                                                or dadoConta.tipoConta.designacao == gestorCentroPerfil
                                                or dadoConta.tipoConta.designacao == investigadorPerfil}">
                            <h4 class="fw-bolder" style="color: #AB81A3">Centro de investigação</h4>
                            <h5 class="form-label ">[[${dadoConta.centro.designacao}]]</h5>
                            <input type="hidden" name="centro.id" th:value="${dadoConta.centro.id}">
                            <input type="hidden" name="logotipo" id="logotipoAux">
                        </div>
                        <div class="row">
                            <div class="ver">
                                <h6>Imagem do projecto</h6>
                                <img id="imagemAux" width="100" height="100">
                            </div>
                            <div class="p-3 editar  d-none">
                                <label class="form-label fw-bolder">Adicione o logotipo da Empresa</label>
                                <input class="form-control" type="file" id="imagem" name="imagem">
                            </div>
                        </div>
                        <div class="row">
                            <div class="ver">
                                <h6 class="mb-1">Designação</h6>
                                <p class="" id="designacaoAux"></p>
                            </div>
                            <div class="p-3 editar d-none">
                                    <label class="form-label fw-bolder">Designação</label>
                                    <input type="text" class="form-control" onfocusout="validDesignacao()" id="designacaoAltera" name="designacao">
                                    <div class="invalid-feedback">
                                        Preencher o campo com a designação do projecto.
                                    </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="ver">
                                <h6 class="mb-1">Descrição</h6>
                                <p class="" id="descricaoAux"></p>
                            </div>
                            <div class="p-3 editar  d-none">

                                <div class="form-floating">
                                    <textarea type="text" class="form-control" id="descricaoAltera" onfocusout="validDescricao()" name="descricao" style="height: 100px" placeholder=""></textarea>
                                    <label class="label">Descrição</label>
                                    <div class="invalid-feedback">
                                        Preencher o campo com a descrição do projecto.
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="ver">
                            <h6 class="mb-1">Estado de aprovação</h6>
                            <p id="aprovacaoAux"></p>
                        </div>
                        <div class="row">
                            <div class="ver">
                                <h6 class="mb-1">Orçamento</h6>
                                <p class="" id="orcamentoAux"></p>
                            </div>
                            <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Orçamento</label>
                                    <input type="text" class="form-control" id="orcamentoAltera" onfocusout="validOrcamento()" name="orcamento">
                                    <div class="invalid-feedback">
                                        Preencher o campo com o orçamento do projecto.
                                    </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="ver">
                                <h6 class="mb-1">Data de criação</h6>
                                <p class="" id="dataCriacaoAux"></p>
                            </div>
                            <div class="p-3 editar d-none">
                                    <label class="form-label fw-bolder">Data de criação</label>
                                    <input class="form-control" id="dataCriacaoAltera" onfocusout="validOrcamento()" name="data" type="date">
                                    <div class="invalid-feedback">
                                        Preencher o campo com a data de criação do projecto.
                                    </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="ver">
                                <h6 class="mb-1">Tipo de projecto</h6>
                                <p class="" id="tipoProjectoAux"></p>
                            </div>
                            <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Tipo de Projecto</label>
                                    <select class="form-select" id="tipoProjectoAltera" name="tipoProjecto.id">
                                        <option th:each="item : ${ tipoProjectos}" th:value="${item.id}" th:text="${item.designacao}"></option>
                                    </select>
                            </div>
                        </div>
                        <!--<div class="row">
                            <div class="ver">
                                <h6 class="mb-1">Centro de Investigação</h6>
                                <p class="" id="centroAux"></p>
                            </div>
                            <div class="p-3 editar  d-none">
                                <div class="">
                                    <label class="form-label fw-bolder">Centro de Investigação</label>
                                    <select class="form-select" id="centroAltera" name="centro.id">
                                        <option th:each="item : ${centros}" th:value="${item.id}" th:text="${item.designacao}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>-->
                        <div class="row" >
                            <div class="ver">
                                <h6 class="mb-1">Gestor do Projecto</h6>
                                <p class="" id="gestorAux"></p>
                            </div>
                            <div class="p-3 editar  d-none" th:if="${dadoConta.tipoConta.designacao != gestorCentroPerfil or dadoConta.tipoConta.designacao != gestorProjectoPerfil}">
                                <div th:if="${dadoConta.centro != null}">
                                    <label class="form-label fw-bolder">Gestor do projecto</label>
                                    <select class="form-select" id="gestorAltera" name="gestor.id">
                                        <option th:each="item : ${investigadores}" th:if="${item.tipoConta.designacao != gestorCentroPerfil and item.centro.id == dadoConta.centro.id}" th:value="${item.id}" th:text="${item.pessoa.nome+' '+item.pessoa.apelido}"></option>
                                    </select>
                                </div>
                                <input type="hidden" name="ehAprovado" id="aprovacaoAltera" value="true">
                                <div th:if="${dadoConta.centro == null}">
                                    <input type="hidden" name="gestor.id" th:value="${dadoConta.id}">


                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="ver">
                                <h6 class="mb-1">Estado do projecto</h6>
                                <p class="" id="estadoProjectoAux"></p>
                            </div>
                            <div class="p-3 editar  d-none">
                                <div class="form-check form-switch ms-3 ">
                                    <input class="form-check-input" type="checkbox" name="ehDesenvolvimento" role="switch" id="estadoProjectoAltera">
                                    <label class="form-check-label fw-bolder" for="flexSwitchCheckDefault">Projecto em desenvolvimento?</label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="ver">
                                <h6 class="mb-1">Metódologia</h6>
                                <p class="" id="metodologiaAux"></p>
                            </div>
                            <div class="p-3 editar  d-none">
                                <div class="">
                                    <label class="form-label fw-bolder">Metódologia</label>
                                    <select class="form-select" id="metodologiaAltera" name="metodologia.id">
                                        <option th:each="item : ${metodologias}" th:value="${item.id}" th:text="${item.designacao}">Selecione...</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="ver">
                                <h6 class="mb-1">Projecto de Investimento?</h6>
                                <p class="" id="investimentoAux"></p>
                            </div>
                            <div class="p-3 editar  d-none">
                                <div class="form-check form-switch ms-3 ">
                                    <input class="form-check-input" type="checkbox" name="ehDeInvestimento" role="switch" id="investimentoAltera">
                                    <label class="form-check-label fw-bolder" for="flexSwitchCheckDefault">Projecto de investimento?</label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="ver">
                                <h6 class="mb-1">Relatorio PDF</h6>
                                <a id="link" class="link-item" href="#">Sem relatorio do projecto</a>
                            </div>
                            <div class="p-3 editar d-none">
                                <label for="linkAltera" class="form-label fw-bolder">Carregar o relatório - pdf</label>
                                <input class="form-control" type="file" id="linkAltera" name="ficheiro">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                        <button type="button" class="btn btn-warning" id="modificarButton" value="0">Modificar</button>
                        <button type="submit" class="btn btn-primary d-none" id="salvarButton">Guardar</button>
                    </div>
                </div>
            </div>
        </div>
        </form>
    </div>

    <!-- Modal -->
    <form action="/sakidila_ic/usuario/subscrever" method="post">
        <div class="modal fade" id="pagamentoModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <input type="hidden" name="idConta" th:value="${dadoConta.id}">
            <input type="hidden" name="pagina" th:value="${'sakidila_ic/portal/projecto/registrar'}">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="exampleModalLabel">Plano de subscrição</h1>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <h6>Torna-te uma empresa Premium na MosalaLinkUp!</h6>
                        <div class="mb-3 mt-3">
                            <!--<div class="mb-2">
                                <input type="checkbox" class="btn-check" name="packLite" id="btn-check-lite" autocomplete="off">
                                <label class="btn btn-secondary" for="btn-check-1">Pacote Lite</label>
                            </div>-->

                            <div class="mb-2">
                                <input type="checkbox" class="btn-check" name="packPremium" id="btn-check-premium" autocomplete="off">
                                <label class="btn btn-primary" for="btn-check-2">Pacote Premium</label>
                            </div>
                            <small>Para poder procurar e dar soluções de seus problemas com os investigadores disponiveis subscreva-se na nossa plataforma.</small>
                            <p class="mt-2"> Por apenas <i class="fw-bold fs-5"> 2.000,00 <i class="text-muted fw-normal">AOA/Mensal</i></i></p>
                        </div>
                        <h6 class="mb-3">Selecione o metodo de pagamento</h6>
                        <div class="d-flex flex-row">
                            <div class="mb-4 me-2">
                                <input type="checkbox" class="btn-check" name="checkpagamento" id="btn-check1" autocomplete="off">
                                <label class="btn btn-outline-secondary" for="btn-check1">Visa Card</label>
                            </div>
                            <div class="mb-4 me-2">
                                <input type="checkbox" class="btn-check" name="checkpagamento" id="btn-check2" autocomplete="off">
                                <label class="btn btn-outline-secondary" for="btn-check2">Paypal</label>
                            </div>
                            <div class="mb-4 me-2">
                                <input type="checkbox" class="btn-check" name="checkpagamento" id="btn-check3" autocomplete="off">
                                <label class="btn btn-outline-secondary" for="btn-check3">Multicaixa Express</label>
                                <div class="valid-feedback">

                                </div>
                                <div class="invalid-feedback">
                                    Preencher campo com o número do cartão.
                                </div>
                            </div>
                        </div>
                        <h6 class="mb-2">Dados do cartão</h6>
                        <div class="row">
                            <div class="col">
                                <div class="mb-3">
                                    <label for="numCartao" class="form-label">Nº do Cartão</label>
                                    <input type="number" class="form-control" id="numCartao" name="numCartao" onfocusout="validNumcartao()" placeholder="1000 2345 6000 7890">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher campo com o número do cartão.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <div class="mb-3">
                                    <label for="dataCartaoExp" class="form-label">Data de Expiração</label>
                                    <input type="date" class="form-control" id="dataCartaoExp" name="dataCartaoExp" onfocusout="validDataCartaoExp()" placeholder="<EMAIL>">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher campo com a data de expiração do cartão.
                                    </div>
                                </div>
                            </div>

                            <div class="col">
                                <div class="mb-3">
                                    <label for="cvvValidate" class="form-label">CVC</label>
                                    <input type="number" class="form-control" name="cvvValidate" id="cvvValidate" onfocusout="validCVV()" placeholder="890">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher campo com o CVV do cartão.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Voltar</button>
                        <button type="submit" name="subscrever" class="btn" style="background-color: #AB81A3; color: white">Subscrever agora!</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="toast-container position-fixed bottom-0 end-0 p-3 "  data-bs-autohide="false">
    <div id="liveToast" class="toast  border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto" id="titlo-alerta">MusalaLinkUp</strong>
            <small class="text-body-secondary">Agora</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="mensagem">
            Nesta secção podes ver os teus projectos, adicionar um novo, aplicar alterações e muito mais!
        </div>
    </div>
</div>
<script>

    window.onload = (event) => {
        const toastLiveExample = document.getElementById('liveToast')

        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        setTimeout(() => {
            toastBootstrap.show()
        },1000)
    };

    $(document).ready(function () {
        $("#projecto").change(function () {
            $("#metodologia").empty();
            $("#fases").empty();
            $("#investigadores").empty();
            getMetodologia();
            getFases();
            getInvestigadores()
        })

        $("#modificarButton").click(function (){
            modificarView();
        })

        $(".teste").click(function (){
        })

        /* $("#enviar").click(function (e){
             e.preventDefault();
             alert("OK")
             saveTarefa()
         })*/
    })

    function getFases() {
        projectoId = $("#projecto").val();
        url = "http://localhost:8080/projectoAjax/" + projectoId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                fases = $("#fases")
                $.each(responseJson, function (index, metodologia) {
                    $("<option>")
                        .val(metodologia.id)
                        .text(metodologia.designacao)
                        .appendTo(fases);
                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }

    function getMetodologia() {
        projectoId = $("#projecto").val();
        url = "http://localhost:8080/projectoAjax/metodologia/" + projectoId;
        $.ajax({method: "GET", url,
            success:  function (data){
                $("#metodologia").text(data.designacao)
                    .val(data.id)
            }});

    }

    /*function saveTarefa(){
        url = "http://localhost:8080/sakidila_ic/portal/projecto/tarefa/criar";
        formData = $("#formulario").serialize();
        $.ajax({
            type: 'post',
            url: url,
            data: formData,
            success: function (data) {
                console.log('Submission was successful.');
                console.log(data);
            },
            error: function (data) {
                console.log('An error occurred.');
                console.log(data);
            },
        });
    }*/


    function modificarView(){
        if($("#modificarButton").val() == "0") {
            $(".editar").removeClass("d-none")
            $(".ver").addClass("d-none")
            $("#modificarButton").removeClass("btn-warning")
            $("#modificarButton").addClass("btn-outline-warning")
            $("#modificarButton").text("Apenas ver")
            $("#modificarButton").val("1")
            $("#salvarButton").removeClass("d-none")
        }
        else if ($("#modificarButton").val() == "1"){
            $(".ver").removeClass("d-none")
            $(".editar").addClass("d-none")
            $("#modificarButton").removeClass("btn-outline-warning")
            $("#modificarButton").addClass("btn-warning")
            $("#modificarButton").text("Modificar")
            $("#modificarButton").val("0")
            $("#salvarButton").addClass("d-none")
        }
    }

    function getInvestigadores() {
        projectoId = $("#projecto").val();
        url = "http://localhost:8080/projectoAjax/contas/" + projectoId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                investigadoresSelect = $("#investigadores")
                $.each(responseJson, function (index, investigadores) {
                    $("<option>")
                        .text(investigadores.pessoa.nome+" "+investigadores.pessoa.apelido)
                        .val(investigadores.id)
                        .appendTo(investigadoresSelect)

                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }



    function getInvestidoresPeloIdProjecto(idProjecto) {
        url = "http://localhost:8080/sakidila_ic/investidor_projecto/investidores/" + idProjecto;

        $.ajax({method: "GET", url})
            .done(function (responseJson) {

                tabela = $("#lista-solicitacoes")
                tabela.find($(".info")).empty();

                $.each(responseJson, function (index, item) {
                    linha = $("<tr class='info'>")

                    $("<td>")
                        .text(item.id.investidor.entidade.designacao)
                        .appendTo(linha)

                    if (!item.ehAprovado) {

                        buttonAprovar = $("<a class='btn btn-primary btn-sm'>Aceitar proposta</a>")
                        buttonAprovar.attr("href", "/sakidila_ic/investidor/aprovar/" + item.id.projecto.id + "/" + item.id.investidor.id)
                        tdAprovar = $("<td>")

                        buttonDelete = $("<a class='btn btn-danger btn-sm'>Rejeitar proposta</a>")
                        buttonDelete.attr("href","/sakidila_ic/investidor/delete/"+item.id.projecto.id+"/"+item.id.investidor.id)
                        tdDelete = $("<td>")
                        alert("id projecto: "+item.id.projecto.id+", id investidor: "+item.id.investidor.id)
                        buttonNegociar = $("<button type='button' class='btn btn-warning btn-sm negociar' id='buttonNegociar' data-bs-toggle='modal' data-bs-target='#negociacao'>Negociar</button>")
                        tdNegociar = $("<td>")
                        investidor = $("<input type='hidden' id='idInvestidorNegociar'>")
                        investidor.attr("value",item.id.investidor.id)
                        investidor_nome = $("<input type='hidden' name='investidor-nome'>")
                        investidor_nome.attr("value",'Empresa: '+item.id.investidor.entidade.designacao)
                        tel = $("<input type='hidden' name='telNegociar'>")
                        tel.attr("value",'contacto: '+item.id.investidor.contacto)
                        email = $("<input type='hidden' name='emailNegociar'>")
                        email.attr("value",'E-mail: '+item.id.investidor.email)
                        investidor.appendTo(linha)
                        investidor_nome.appendTo(tdNegociar)
                        tel.appendTo(tdNegociar)
                        email.appendTo(tdNegociar)
                        buttonNegociar.appendTo(tdNegociar)
                        tdNegociar.appendTo(linha)
                        buttonDelete.appendTo(tdDelete)
                        tdDelete.appendTo(linha)
                    }
                    else
                    {
                        buttonAprovar = $("<p>Proposta aprovada</p>")
                        tdAprovar = $("<td>")

                    }
                    buttonAprovar.appendTo(tdAprovar)
                    tdAprovar.appendTo(linha)




                    /*
                    buttonDelete = $("<a class='btn btn-danger btn-sm'>Rejeitar proposta</a>")
                    buttonDelete.attr("href","/sakidila_ic/investidor/delete/"+item.id.projecto.id+"/"+item.id.investidor.id)
                    tdDelete = $("<td>")

                    buttonDelete.appendTo(tdDelete)
                    tdDelete.appendTo(linha)
                    */
                    linha.appendTo(tabela)
                })
            })
    }

    $(document).on("click", "#buttonNegociar", function sms() {
        $("#buttonAceitarProposta").attr("href","/sakidila_ic/investidor/aprovar/"+$("#idProjectoNegociar").val()+"/"+$("#idInvestidorNegociar").val())
        $("#buttonNegarProposta").attr("href","/sakidila_ic/investidor/delete/"+$("#idProjectoNegociar").val()+"/"+$("#idInvestidorNegociar").val())
    })

    $(document).on("click", "#lista-solicitacoes button.negociar", function sms() {
        let table = $(this).closest('tr');
        $("#projecto-nome-negociar").text($("#projecto-nome").text())
        $("#investidor-tel").text(table.find($("[name=telNegociar]")).val())
        $("#investidor-email").text(table.find($("[name=emailNegociar]")).val())
        $("#investidor-nome-negociar").text(table.find($("[name=investidor-nome]")).val())
    })

    $(document).on("click", "#projectos button.load-solicitacoes", function sms() {

        let tr = $(this).closest('td');
        $("#projecto-nome").text(tr.find($("[name=designacao]")).val());
        idProjecto = tr.find($("[name=idProjecto]")).val();
        $("#idProjectoNegociar").val(idProjecto)
        getInvestidoresPeloIdProjecto(idProjecto);
    })

    $(document).on("click", "#projectos button.detalhe", function sms() {

        let tr = $(this).closest('tr');
        $("#idProjectoAux").val(tr.find($("[name=idProjecto]")).val());
        $("#imagemAux").attr("src", tr.find($("[name=imagem]")).val() != "" ? tr.find($("[name=imagem]")).val() : '/img/empty.jpg');
        $("#logotipoAux").val(tr.find($("[name=imagem]")).val())
        $("#designacaoAux").text(tr.find($("[name=designacao]")).val());
        $("#designacaoAltera").val(tr.find($("[name=designacao]")).val());
        $("#descricaoAux").text(tr.find($("[name=descricao]")).val());
        $("#descricaoAltera").val(tr.find($("[name=descricao]")).val());
        $("#centroAux").text(tr.find($("[name=centro]")).val());
        //$("#centroAltera").val(tr.find($("[name=centro]")).val());
        $("#gestorAux").text(tr.find($("[name=gestorProjecto]")).val());
        //$("#gestorAltera").val(tr.find($("[name=gestorProjecto]")).val());
        $("#tipoProjectoAux").text(tr.find($("[name=tipoProjecto]")).val());
        //$("#tipoProjectoAltera").val(tr.find($("[name=tipoProjecto]")).val());
        $("#dataCriacaoAux").text(tr.find($("[name=dataCriacao]")).val());
        $("#dataCriacaoAltera").val(tr.find($("[name=dataCriacao]")).val());
        $("#orcamentoAux").text(tr.find($("[name=orcamento]")).val());
        $("#orcamentoAltera").val(tr.find($("[name=orcamentoNumerico]")).val());
        $("#metodologiaAux").text(tr.find($("[name=Metodologia]")).val());
        //$("#metodologiaAltera").val(tr.find($("[name=Metodologia]")).val());
        $("#estadoProjectoAux").val(tr.find($("[name=ehDesenvolvimento]")).val());
        $("#estadoProjectoAltera").val(tr.find($("[name=ehDesenvolvimento]")).val());
        $("#investimentoAux").val(tr.find($("[name=ehDeInvestimento]")).val());
        $("#investimentoAltera").val(tr.find($("[name=ehDeInvestimento]")).val());
        $("#aprovacaoAux").val(tr.find($("[name=aprovacao]")).val());
        $("#aprovacaoAltera").val(tr.find($("[name=aprovacao]")).val());
        $("#linkAltera").val(tr.find($("[name=ficheiro]")).val());

        //Script que apresenta o estado de um projecto
        if($("#estadoProjectoAux").val() == 'true') {
            $("#estadoProjectoAux").text("Em desenvolvimento")
            $("#estadoProjectoAux").removeClass("text-success")
            $("#estadoProjectoAux").addClass("text-warning")
            $("#estadoProjectoAltera").attr("checked",true);
            $("#estadoProjectoAltera").val("true");
            $("#estadoProjectoAltera").text("true");
        }
        else {
            $("#estadoProjectoAux").text("Concluido")
            $("#estadoProjectoAux").addClass("text-success")
            $("#estadoProjectoAux").removeClass("text-warning")
            $("#estadoProjectoAltera").attr("checked",false);
            $("#estadoProjectoAltera").val("false");
            $("#estadoProjectoAltera").text("false");
        }

        if($("#aprovacaoAux").val() == 'false') {
            $("#aprovacaoAux").text("Pendente")
            $("#aprovacaoAux").removeClass("text-success")
            $("#aprovacaoAux").addClass("text-warning")
        }
        else {
            $("#aprovacaoAux").text("Aprovado para divulgação")
            $("#aprovacaoAux").addClass("text-success")
            $("#aprovacaoAux").removeClass("text-warning")
        }


        if($("#investimentoAux").val() == 'false') {
            $("#investimentoAux").text("Não")
            $("#investimentoAux").removeClass("text-success")
            $("#investimentoAux").addClass("text-warning")
            $("#investimentoAltera").attr("checked",false);
            $("#investimentoAltera").val("false");
            $("#investimentoAltera").text("false");
        }
        else {
            $("#investimentoAux").text("Sim")
            $("#investimentoAux").addClass("text-success")
            $("#investimentoAux").removeClass("text-warning")
            $("#investimentoAltera").val("true");
            $("#investimentoAltera").text("true");
        }

        idDocumento = tr.find($("[name=documento]")).val();
        docNome = tr.find($("[name=docNome]")).val();
        url = "http://localhost:8080/Ajax/projecto/" + tr.find($("[name=idProjecto]")).val();
        urlFicheiro = "http://localhost:8080/documento/" + idDocumento;
        documentoLink = $("#link")
        documentoLink.val(idDocumento)
        documentoLink.text(docNome)
        documentoLink.attr({
            target: "_blank",
            href: urlFicheiro
        })
    })

    $(document).on("click", "#projectosInvestidos button.detalhe", function sms() {

        let tr = $(this).closest('tr');
        $("#idProjectoAux").val(tr.find($("[name=idProjecto]")).val());
        $("#imagemAux").attr("src", tr.find($("[name=imagem]")).val() != "" ? tr.find($("[name=imagem]")).val() : '/img/empty.jpg');
        $("#logotipoAux").val(tr.find($("[name=imagem]")).val())
        $("#designacaoAux").text(tr.find($("[name=designacao]")).val());
        $("#designacaoAltera").val(tr.find($("[name=designacao]")).val());
        $("#descricaoAux").text(tr.find($("[name=descricao]")).val());
        $("#descricaoAltera").val(tr.find($("[name=descricao]")).val());
        $("#centroAux").text(tr.find($("[name=centro]")).val());
        //$("#centroAltera").val(tr.find($("[name=centro]")).val());
        $("#gestorAux").text(tr.find($("[name=gestorProjecto]")).val());
        //$("#gestorAltera").val(tr.find($("[name=gestorProjecto]")).val());
        $("#tipoProjectoAux").text(tr.find($("[name=tipoProjecto]")).val());
        //$("#tipoProjectoAltera").val(tr.find($("[name=tipoProjecto]")).val());
        $("#dataCriacaoAux").text(tr.find($("[name=dataCriacao]")).val());
        $("#dataCriacaoAltera").val(tr.find($("[name=dataCriacao]")).val());
        $("#orcamentoAux").text(tr.find($("[name=orcamento]")).val());
        $("#orcamentoAltera").val(tr.find($("[name=orcamentoNumerico]")).val());
        $("#metodologiaAux").text(tr.find($("[name=Metodologia]")).val());
        //$("#metodologiaAltera").val(tr.find($("[name=Metodologia]")).val());
        $("#estadoProjectoAux").val(tr.find($("[name=ehDesenvolvimento]")).val());
        $("#estadoProjectoAltera").val(tr.find($("[name=ehDesenvolvimento]")).val());
        $("#investimentoAux").val(tr.find($("[name=ehDeInvestimento]")).val());
        $("#investimentoAltera").val(tr.find($("[name=ehDeInvestimento]")).val());
        $("#aprovacaoAux").val(tr.find($("[name=aprovacao]")).val());
        $("#aprovacaoAltera").val(tr.find($("[name=aprovacao]")).val());
        $("#linkAltera").val(tr.find($("[name=ficheiro]")).val());

        //Script que apresenta o estado de um projecto
        if($("#estadoProjectoAux").val() == 'true') {
            $("#estadoProjectoAux").text("Em desenvolvimento")
            $("#estadoProjectoAux").removeClass("text-success")
            $("#estadoProjectoAux").addClass("text-warning")
            $("#estadoProjectoAltera").attr("checked",true);
            $("#estadoProjectoAltera").val("true");
            $("#estadoProjectoAltera").text("true");
        }
        else {
            $("#estadoProjectoAux").text("Concluido")
            $("#estadoProjectoAux").addClass("text-success")
            $("#estadoProjectoAux").removeClass("text-warning")
            $("#estadoProjectoAltera").attr("checked",false);
            $("#estadoProjectoAltera").val("false");
            $("#estadoProjectoAltera").text("false");
        }

        if($("#aprovacaoAux").val() == 'false') {
            $("#aprovacaoAux").text("Pendente")
            $("#aprovacaoAux").removeClass("text-success")
            $("#aprovacaoAux").addClass("text-warning")
        }
        else {
            $("#aprovacaoAux").text("Aprovado para divulgação")
            $("#aprovacaoAux").addClass("text-success")
            $("#aprovacaoAux").removeClass("text-warning")
        }


        if($("#investimentoAux").val() == 'false') {
            $("#investimentoAux").text("Não")
            $("#investimentoAux").removeClass("text-success")
            $("#investimentoAux").addClass("text-warning")
            $("#investimentoAltera").attr("checked",false);
            $("#investimentoAltera").val("false");
            $("#investimentoAltera").text("false");
        }
        else {
            $("#investimentoAux").text("Sim")
            $("#investimentoAux").addClass("text-success")
            $("#investimentoAux").removeClass("text-warning")
            $("#investimentoAltera").val("true");
            $("#investimentoAltera").text("true");
        }

        idDocumento = tr.find($("[name=documento]")).val();
        docNome = tr.find($("[name=docNome]")).val();
        url = "http://localhost:8080/Ajax/projecto/" + tr.find($("[name=idProjecto]")).val();
        urlFicheiro = "http://localhost:8080/documento/" + idDocumento;
        documentoLink = $("#link")
        documentoLink.val(idDocumento)
        documentoLink.text(docNome)
        documentoLink.attr({
            target: "_blank",
            href: urlFicheiro
        })
    })


    function validDesignacaoP(){
        info = document.forms["novo"]["designacao"].value;
        component = $("#designacaoValidate")
        if(info != "") {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDescricaoP(){
        info = document.forms["novo"]["descricao"].value;
        component = $("#descricaoValidate")
        if(info != "") {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validOrcamentoP(){
        info = document.forms["novo"]["orcamento"].value;
        component = $("#orcamentoValidate")
        if(info != "") {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataCriacaoP(){
        info = document.forms["novo"]["data"].value;
        component = $("#dataValidate")
        if(info != "") {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDesignacao(){
        info = document.forms["myFormEditar"]["designacao"].value;
        component = $("#designacaoAltera")
        if(info != "") {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDescricao(){
        info = document.forms["myFormEditar"]["descricao"].value;
        component = $("#descricaoAltera")
        if(info != "") {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validOrcamento(){
        info = document.forms["myFormEditar"]["orcamento"].value;
        component = $("#orcamentoAltera")
        if(info != "") {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataCriacao(){
        info = document.forms["myFormEditar"]["data"].value;
        component = $("#dataCriacaoAltera")
        if(info != "") {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    //Validação da subscrição

    function validCVV(){
        info = document.forms["myForm"]["cvvValidate"].value;
        component = $("#cvvValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataCartaoExp(){
        info = document.forms["myForm"]["dataCartaoExp"].value;
        component = $("#dataCartaoExp")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validNumcartao(){
        info = document.forms["myForm"]["numCartao"].value;
        component = $("#numCartao")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validCheck(){
        info = document.forms["myForm"]["checkpagamento"].value;
        if(info != "" ) {
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
        }
    }

    function validateForm() {
        nome = document.forms["novo"]["designacao"].value;
        descricao = document.forms["novo"]["descricao"].value;
        orcamento = document.forms["novo"]["orcamento"].value;
        data = document.forms["novo"]["data"].value;
        const toastLiveExample = document.getElementById('liveToast')
        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        if (nome == ""
            || descricao == ""
            || orcamento ==""
            || data == "") {
            validDescricaoP()
            validDesignacaoP()
            validDataCriacaoP()
            validOrcamentoP()
            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
            $("#liveToast").addClass("text-bg-danger")
            $("#liveToast").removeClass("text-bg-warning")
            $("#titlo-alerta").empty()
            $("#titlo-alerta").text("Erro ao criar conta estudante")
            $("#mensagem").empty()
            $("#mensagem").text("Verifique os campos se estão vazios ou devidamente preenchidos.")
            toastBootstrap.show()
            submit = false;
            return false;
        }
        else {
            submit = submitValidation;
            $("#liveToast").removeClass("text-bg-danger")
            $("#liveToast").removeClass("text-bg-warning")
            $("#titlo-alerta").empty()
            $("#titlo-alerta").text("MusalaLinkUp")
            $("#mensagem").empty()
            $("#mensagem").text("Projecto adicionado com sucesso!")
            toastBootstrap.show()
            setTimeout(() => {
                document.myForm.submit()
            }, 5000)
        }
        return false;
    }

    function validateFormEditar() {
        nome = document.forms["myFormEditar"]["designacao"].value;
        descricao = document.forms["myFormEditar"]["descricao"].value;
        orcamento = document.forms["myFormEditar"]["orcamento"].value;
        data = document.forms["myFormEditar"]["data"].value;
        const toastLiveExample = document.getElementById('liveToast')
        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        if (nome == ""
            || descricao == ""
            || orcamento ==""
            || data == "") {
            validDescricao()
            validDesignacao()
            validDataCriacao()
            validOrcamento()
            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
            $("#liveToast").addClass("text-bg-danger")
            $("#liveToast").removeClass("text-bg-warning")
            $("#titlo-alerta").empty()
            $("#titlo-alerta").text("Erro ao criar conta estudante")
            $("#mensagem").empty()
            $("#mensagem").text("Verifique os campos se estão vazios ou devidamente preenchidos.")
            toastBootstrap.show()
            submit = false;
            return false;
        }
        else {
            submit = submitValidation;
            $("#liveToast").removeClass("text-bg-danger")
            $("#liveToast").removeClass("text-bg-warning")
            $("#titlo-alerta").empty()
            $("#titlo-alerta").text("MusalaLinkUp")
            $("#mensagem").empty()
            $("#mensagem").text("Projecto adicionado com sucesso!")
            toastBootstrap.show()
            setTimeout(() => {
                document.myForm.submit()
            }, 5000)
        }
        return false;
    }

</script>
</body>
</html>