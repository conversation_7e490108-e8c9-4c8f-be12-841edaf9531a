package com.sakidila.projectoic.service;

import com.sakidila.projectoic.entity.InvestidorProjecto;
import com.sakidila.projectoic.entity.Projecto;
import com.sakidila.projectoic.repository.InvestidorProjectoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class InvestidorProjectoService {

    @Autowired
    private InvestidorProjectoRepository repository;

    @Autowired
    private ProjectoServico projectoServico;

    public InvestidorProjecto criarInvestidorProjecto(InvestidorProjecto investidorProjecto){

        return repository.save(investidorProjecto);
    }

    public List<InvestidorProjecto> criarListaInvestidorProjecto(List<InvestidorProjecto> lista){
        return repository.saveAll(lista);
    }

    public Page<InvestidorProjecto> encontrarPagina(int pagina, int tamanho,Long idProjecto) {
        Pageable pageable = PageRequest.of(pagina - 1, tamanho, Sort.by("id.projecto.designacao"));
        return  repository.findAllByIdProjectoId(idProjecto,pageable);
    }

    public List<InvestidorProjecto> encontrarInvestidoresPeloIdProjecto(Long idProjecto){
        return repository.findAllByIdProjectoId(idProjecto);
    }

    public List<InvestidorProjecto> encontrarProjectosPeloIdInvestidor(Long idInvestidor){
        return repository.findAllByIdInvestidorId(idInvestidor);
    }

    public List<Projecto> encontrarPeloIdInvestidor(Long idInvestidor){
        List<InvestidorProjecto> investidorProjectos = repository.findAllByIdInvestidorId(idInvestidor);
        List<Projecto> lista = new ArrayList<>();
        if(investidorProjectos != null){
            for (InvestidorProjecto aux: investidorProjectos) {
                lista.add(aux.getId().getProjecto());
                System.out.println("projecto: "+aux.getId().getProjecto().getDesignacao());
            }
            return lista;
        }
        return null;
    }

    public void deletar(Long idProjecto, Long idInvestidor) {
        repository.deleteInvestidorProjectoByIdProjectoAndIdInvestidor(idProjecto,idInvestidor);
    }

    public void aprovar(Long idProjecto, Long idInvestidor) {
        repository.aprovarInvestidorProjectoByIdProjectoAndIdInvestidor(idProjecto,idInvestidor);
        //excluirTodosExceptoInvestidorId(idProjecto,idInvestidor);
        projectoServico.tornarInvestido(idProjecto);
    }

    public void excluirTodosExceptoInvestidorId(Long idProjecto,Long idInvestidor){
        List<InvestidorProjecto> lista = encontrarInvestidoresPeloIdProjecto(idProjecto);
        for (InvestidorProjecto investidorProjecto:lista) {
            if(investidorProjecto.getId().getInvestidor().getId() != idInvestidor)
                deletar(idProjecto,investidorProjecto.getId().getInvestidor().getId());
        }
    }
}
