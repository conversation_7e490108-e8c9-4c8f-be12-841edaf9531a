package com.sakidila.projectoic.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Centro {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String designacao;

    @Column(length = 2048)
    private String descricao;

    private String sigla;

    @Lob
    @Nullable
    private String logotipo;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "fk_curso")
    @Nullable
    private Curso curso;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "fk_faculdade")
    @Nullable
    private Faculdade faculdade;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "fk_responsavel")
    @Nullable
    private Conta conta;

    @JsonIgnore
    @OneToMany(mappedBy = "centro")
    private List<Conta> membros;

    @JsonIgnore
    @OneToMany(mappedBy = "centro")
    private List<Projecto> projectos;

    @Column(columnDefinition = "boolean default false")
    @Nullable
    private boolean disabled;
}
