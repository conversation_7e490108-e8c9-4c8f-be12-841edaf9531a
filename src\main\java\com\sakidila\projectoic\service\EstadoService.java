package com.sakidila.projectoic.service;

import com.sakidila.projectoic.entity.EstadoCivil;
import com.sakidila.projectoic.repository.EstadoCivilRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EstadoService {

    @Autowired
    private EstadoCivilRepository repository;

        public EstadoCivil criarSexo(EstadoCivil estadoCivil){
            return repository.save(estadoCivil);
        }

        public List<EstadoCivil> listaEstadoCivil(){
            return repository.findAll();
        }

}
