package com.sakidila.projectoic.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Entity
@Getter
@Setter
public class Entidade {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String sigla;
    private String designacao;
    private String nif;

    @Lob
    @Nullable
    private String logotipo;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "fk_tipo_entidade")
    @Nullable
    private TipoEntidade tipo;

    @Column(nullable = true)
    private String endereco;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "fk_localidade")
    @Nullable
    private Localidade localidade;

    @JsonIgnore
    @OneToMany(mappedBy = "entidade")
    @Nullable
    private List<Conta> ListaContas;

    @Column(columnDefinition = "boolean default false")
    @Nullable
    private boolean disabled;

}
