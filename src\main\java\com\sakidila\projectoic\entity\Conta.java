package com.sakidila.projectoic.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import lombok.*;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Conta implements UserDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

        private String username;
    private String email;
    private String senha;

    @Column(nullable = true)
    private String contacto;

    @Nullable
    @Lob
    private String foto;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "fk_pessoa")
    @Nullable
    private Pessoa pessoa;

    @ManyToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "fk_entidade")
    @Nullable
    private Entidade entidade;

    @Column(columnDefinition = "boolean default false")
    @Nullable
    private boolean ehSubscrito;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "fk_faculdade",nullable = true)
    private Faculdade faculdade;

    @ManyToOne
    @JoinColumn(name = "fk_tipo_conta")
        private TipoConta tipoConta;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "fk_nivel_academico")
    @Nullable
    private NivelAcademico nivelAcademico;

    @JsonIgnore
    @OneToMany(mappedBy = "conta",cascade = CascadeType.ALL)
    private List<Centro> listaCentros;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "fk_centro")
    private Centro centro;

    @JsonIgnore
    @OneToMany(mappedBy = "investigador")
    private List<Tarefa> tarefas;

    @Column(columnDefinition = "boolean default false")
    @Nullable
    private boolean disabled;


    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        HashSet<SimpleGrantedAuthority> set = new HashSet<SimpleGrantedAuthority>();
        set.add(new SimpleGrantedAuthority(tipoConta.getDesignacao()));

        return set;
    }

    @Override
    public String getPassword() {
        return senha;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public String getUsername() {return username;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }


}
