package com.sakidila.projectoic.service;

import com.sakidila.projectoic.entity.TipoConta;
import com.sakidila.projectoic.repository.TipoContaRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class TipoContaService {

    @Autowired
    private TipoContaRepository tipoContaRepository;

    public void criarTipoContaPadrao(){
        List<TipoConta> lista = new ArrayList<>();
        lista.add(new TipoConta(Long.parseLong("1"),"ROLE_ADMIN","Administrador",null));
        lista.add(new TipoConta(Long.parseLong("2"),"ROLE_ESTUDANTE","estudante",null));
        lista.add(new TipoConta(Long.parseLong("3"),"ROLE_INVESTIGATOR","investigador",null));
        lista.add(new TipoConta(Long.parseLong("4"),"ROLE_GESTOR_INSTITUICAO","Gestor de Instituição",null));
        lista.add(new TipoConta(Long.parseLong("5"),"ROLE_EMPRESA","Instituição",null));
        lista.add(new TipoConta(Long.parseLong("6"),"ROLE_INVESTIGADOR_INDEPENDENTE","investigador",null));
        lista.add(new TipoConta(Long.parseLong("7"),"ROLE_RESPONSAVEL_CENTRO","gestor de centro",null));
        lista.add(new TipoConta(Long.parseLong("8"),"ROLE_GESTOR_PROJECTO","gestor de projecto",null));

        TipoConta tipoConta = tipoContaRepository.findByDesignacao(lista.get(0).getDesignacao());
        if(tipoConta == null) {
            tipoContaRepository.saveAll(lista);
        }
    }

}
