/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/springframework/Service.java to edit this template
 */
package com.sakidila.projectoic.service;

import com.sakidila.projectoic.entity.Conta;
import com.sakidila.projectoic.entity.Entidade;
import com.sakidila.projectoic.repository.EntidadeRepository;
import com.sakidila.projectoic.repository.LocalidadeRepository;
import com.sakidila.projectoic.repository.TipoEntidadeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 */
@Service
public class EntidadeServico {
    
    @Autowired
    private EntidadeRepository repositorio;

    @Autowired
    private TipoEntidadeRepository tipoEntidadeRepository;

    @Autowired
    private LocalidadeRepository localidadeRepository;
    
    public Entidade salvar(Entidade entidade) {

        entidade.setTipo(tipoEntidadeRepository.findById(entidade.getTipo().getId()).get());
        entidade.setLocalidade(localidadeRepository.findById(entidade.getLocalidade().getId()).get());
        return repositorio.save(entidade);
    }
    
    public Entidade ApagarPelaChave(Long chave) {
        Optional<Entidade> optional = repositorio.findById(chave);
        if (optional.isPresent()) {
            repositorio.deleteById(chave);
            return optional.get();
        }
        return null;
    }

    public Page<Entidade> encontrarPagina(int pagina, int tamanho) {
        Pageable pageable = PageRequest.of(pagina - 1, tamanho);
        return  repositorio.findAll(pageable);
    }
    
    
    public List<Entidade> listaInstitutos() {
        return repositorio.findAllInstituto();
    }
    
    public Entidade editar(Entidade Entidade) {
        Optional<Entidade> optional = repositorio.findById(Entidade.getId());
        if (optional.isPresent()) {
            repositorio.save(Entidade);
            return optional.get();
        }
        return null;
    }
    
    public Entidade findById(Long chave) {
        Optional<Entidade> optional = repositorio.findById(chave);
        if (optional.isPresent()) {
            return optional.get();
        }
        return null;
    }

    public Entidade deletar(Long id){
        Entidade entidade = repositorio.findById(id).get();
        if(entidade!=null)
            repositorio.deleteById(id);
        return entidade;
    }

    public Entidade desativar(Long id){
        Entidade entidade = repositorio.findById(id).get();
        if(entidade!=null)
            repositorio.disabledByIdEntidade(id);
        return entidade;
    }
}
