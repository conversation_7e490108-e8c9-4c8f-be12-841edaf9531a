package com.sakidila.projectoic.controller;

import com.sakidila.projectoic.entity.Conta;
import com.sakidila.projectoic.entity.Entidade;
import com.sakidila.projectoic.service.*;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Controller
@RequestMapping("sakidila_ic/entidade/")
public class EntidadeController {

    @Autowired
    private EntidadeServico servico;
    
    @Autowired
    private FicheiroServicoImpl ficheiroServico;

    @Autowired
    private LocalidadeServico localidadeServico;

    @Autowired
    private ContaService contaService;


    @Autowired
    private TipoEntidadeService tipoEntidadeService;
    
    @GetMapping("/registrar/{pagina}")
    public String Registrar(Model model, @PathVariable(required = false) int pagina) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication.isAuthenticated() && !authentication.getName().equals("anonymousUser")) {
            int tamanho = 10;
            Conta conta = contaService.mostrarDadoConta(authentication.getName());
            model.addAttribute("conta", authentication.getName());
            model.addAttribute("userDefault", "anonymousUser");
            model.addAttribute("dadoConta", conta);
            model.addAttribute("codInstituicao",Long.parseLong("2"));
            model.addAttribute("gestorInstituicaoPerfil", "ROLE_GESTOR_INSTITUICAO");
            model.addAttribute("gestorCentroPerfil", "ROLE_RESPONSAVEL_CENTRO");
            model.addAttribute("gestorProjectoPerfil", "ROLE_GESTOR_PROJECTO");
            model.addAttribute("investigadorPerfil", "ROLE_INVESTIGATOR");
            model.addAttribute("adminPerfil", "ROLE_ADMIN");
            model.addAttribute("localidades",localidadeServico.listaLocalidadesPrimitiva());
            model.addAttribute("entidades",tipoEntidadeService.listaEntidades());
            Page<Entidade> paginaNova = servico.encontrarPagina(pagina,tamanho);
            List<Entidade> entidadeLista = paginaNova.getContent();
            model.addAttribute("paginaActual",pagina);
            model.addAttribute("totalPaginas",paginaNova.getTotalPages());
            model.addAttribute("totalItems",paginaNova.getTotalElements());
            model.addAttribute("listaEntidades",entidadeLista);
            return "entidade_registrar";
        }else{
            SecurityContextHolder.clearContext();
            model.addAttribute("conta",null);
            model.addAttribute("dadoConta",null);
            return "login";
        }
    }

    @PostMapping("/salvar")
    public String salvarEntidade(@ModelAttribute Entidade entidade, HttpSession session, @RequestParam(name = "imagem") MultipartFile multipartFile){
        System.out.println(entidade.getId());
        String imagem = ficheiroServico.uploadImage(multipartFile);
        entidade.setLogotipo(imagem);
        entidade = servico.salvar(entidade);
        if(entidade != null)
            session.setAttribute("msg", "Usuario " + entidade.getDesignacao() + " criado com sucesso!");
        else
            session.setAttribute("msg", "O E-mail " + entidade.getDesignacao() + " já existe no sistema");
        return "redirect:/sakidila_ic/entidade/registrar/1";
    }

    @GetMapping("/delete/{id}")
    public String deletar(@PathVariable Long id){
        servico.deletar(id);
        return "redirect:/sakidila_ic/entidade/registrar/1";

    }

    @GetMapping("/desativar/{id}")
    public String disabledByIdEntidade(@PathVariable Long id){
        servico.desativar(id);
        return "redirect:/sakidila_ic/entidade/registrar/1";

    }
}
