package com.sakidila.projectoic.controller;

import com.sakidila.projectoic.entity.Centro;
import com.sakidila.projectoic.entity.Conta;
import com.sakidila.projectoic.entity.EntidadeFaculdade;
import com.sakidila.projectoic.service.CentroServico;
import com.sakidila.projectoic.service.EntidadeFaculdadeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/CentroAjax")
public class CentroAjaxController {

    @Autowired
    private CentroServico service;

    @GetMapping("/{id}")
    public ResponseEntity<List<?>> encontrarCentrosById(@PathVariable Long id){
        List<Centro> lista = new ArrayList<>();
            lista = service.findAllCentrosByidFaculdade(id);
        return new ResponseEntity<>(lista, HttpStatus.OK);
    }

    @GetMapping("/{id}/investigadores")
    public ResponseEntity<List<?>> MembrosByIdCentro(@PathVariable Long id){
        List<Conta> lista = new ArrayList<>();
        lista = service.findById(id).getMembros();
        return new ResponseEntity<>(lista, HttpStatus.OK);
    }



}
