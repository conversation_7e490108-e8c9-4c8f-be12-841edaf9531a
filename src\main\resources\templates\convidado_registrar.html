<!DOCTYPE html>
<html lang="en" xmlns:th="https://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">

    <!-- Importação do Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
            crossorigin="anonymous"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style th:inline="text">
        body{
            font-family: Poppins;
        }
    </style>
    <title>Registra-se na Sakidila!</title>
</head>
<body style="background-color: ghostwhite">

<nav class="navbar navbar-expand-lg p-0 sticky-top" style="margin: 0%;">
    <button class="navbar-toggler ms-2" type="button" data-bs-toggle="collapse" data-bs-target="#navbarTogglerDemo01" aria-controls="navbarTogglerDemo01" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarTogglerDemo01">
        <div class=" d-flex flex-column bg-white shadow-sm" style="width: 100%">
            <div class="container-fluid">
                <div class="container d-flex flex-row align-items-center">
                    <a class="navbar-brand" href="/sakidila_ic/portal/">
                        <img src="/img/logotipo.jpg" alt="Bootstrap" width="120">
                    </a>

                    <div class="container d-flex flex-row justify-content-end align-items-center">
                        <a href="/sakidila_ic/usuario/principal" class="nav-link">Registrar</a>
                        <div class="dropdown btn">
                            <a href="#" class="d-block link-body-emphasis text-decoration-none dropdown dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <img src="/img/user.png" alt="mdo" width="32" height="32" class="rounded-circle">
                            </a>
                            <ul class="dropdown-menu text-small">
                                <li><a class="dropdown-item" href="/sakidila_ic/usuario/login">Entrar</a></li>
                                <!--<li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#">Sair</a></li>-->
                            </ul>
                        </div>
                        <i class="fs-5 bi bi-facebook me-2" style="color: #7c6aa2"></i>
                        <i class="fs-5 bi bi-instagram me-2" style="color: #7c6aa2"></i>
                        <i class="fs-5 bi bi-whatsapp" style="color: #7c6aa2"></i>
                    </div>
                </div>

            </div>
            <div class="container-fluid py-1" style="background-color: #AB81A3">
                <ul class="container navbar-nav d-flex justify-content-end ">
                    <li class="nav-item">
                        <a class="nav-link text-white active px-3" aria-current="page" href="/sakidila_ic/portal/">Portal</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white px-3 active" href="/sakidila_ic/portal/listagem/1">Projectos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white px-3" href="#">Sobre</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white ps-3 me-3" href="#">Contacte-nos</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</nav>                            <!--<li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#">Sair</a></li>-->

<div class="container mt-5">
    <h5 class="display-6 mt-5 mb-1 fw-bold" style="color: #AB81A3">Criar Conta <i class="fw-bold text-dark">Estudante!</i></h5>
    <hr>
    <form name="myForm" method="post" th:action="@{/sakidila_ic/usuario/registrar/salvar}" enctype="multipart/form-data" onsubmit="return validateForm()" required>
        <fieldset class="bg-white px-5 py-4 shadow-sm rounded-4 mb-3">
            <legend>Dados Pessoais</legend>
            <div class="row mb-3">
                <div class="col">
                        <div class="">
                            <label class="form-label" id="nome">Nome</label>
                            <input type="text"  class="form-control" placeholder="" id="nomeValidate" onfocusout="validNome()" name="pessoa.nome" aria-label="Nome" aria-describedby="Nome">
                            <div class="valid-feedback">

                            </div>
                            <div class="invalid-feedback">
                                Preencher o campo com o seu nome.
                            </div>
                        </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" id="apelido">Apelido</label>
                        <input type="text" class="form-control" id="apelidoValidate" onfocusout="validApelido()" name="pessoa.apelido" placeholder="" aria-label="Apelido" aria-describedby="apelido">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher o campo com o seu ultimo nome.
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label">Adicione sua foto de perfil</label>
                        <input class="form-control" type="file" id="foto" name="imagem">
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col">
                    <div class="">
                        <label class="form-label" id="dataNascimento">Data de Nascimento</label>
                        <input type="date" class="form-control" id="Nascimentovalidate" onfocusout="validDataNascimento()" name="pessoa.dataNascimento" placeholder="" aria-label="dataNascimento" aria-describedby="dataNascimento">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Especifique a sua data de nascimento
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label">Sexo</label>
                        <select class="form-select"  aria-label="Sexo" id="sexo" name="pessoa.sexo.id" aria-describedby="sexo">
                            <option th:each="sexo : ${sexos}" th:value="${sexo.id}"
                                    th:text="${sexo.designacao}"></option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" id="estadoCivil.id">Estado Civil</label>
                        <select class="form-select" placeholder="" name="pessoa.estadoCivil.id" aria-label="estadoCivil" id="estado" aria-describedby="estadoCivil">
                            <option th:each="estado : ${estados}" th:value="${estado.id}"
                                    th:text="${estado.designacao}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col">
                    <div class="">
                        <label class="form-label" id="identidade">Nº Identidade</label>
                        <input type="text" class="form-control" placeholder="" id="biValidate" onfocusout="validBi()" name="pessoa.bi" aria-label="identidade" aria-describedby="identidade">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher o campo com o número de identidade.
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" id="dataEmissao">Data de Emissão</label>
                        <input type="date" class="form-control"  aria-label="dataEmissao" id="emissaoValidate" onfocusout="validDataEmissao()" name="pessoa.dataEmissao" aria-describedby="dataEmissao">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Especifique a data de emissão.
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" id="dataValidade">Data de Validade</label>
                        <input type="date" class="form-control" placeholder="" id="validadeValidate" onfocusout="validDataValidade()" name="pessoa.dataValidade" aria-label="dataValidade" aria-describedby="dataValidade">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Especifique a data de validade.
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col">
                    <div class="">
                        <label class="form-label">Provincia</label>
                        <select class="form-select" id="provincia" placeholder="" aria-label="provincia" aria-describedby="provincia">
                            <option th:each="localidade : ${localidades}" th:value="${localidade.id}"
                                    th:text="${localidade.designacao}"></option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label">Municipio</label>
                        <select class="form-select"  aria-label="municipio"  id="municipio" aria-describedby="municipio"></select>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" >Bairro</label>
                        <select class="form-select" id="bairro" name="pessoa.localidade.id" placeholder="" aria-label="bairro" aria-describedby="bairro"></select>
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col">
                    <div class="">
                        <label class="form-label" id="tel">Contacto telefónico</label>
                        <input type="text" class="form-control" placeholder="" id="contactoValidate" onfocusout="validContacto()" name="pessoa.contacto" aria-label="tel" aria-describedby="tel">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher campo com o contacto telefônico.
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" id="email">Correio Electrônico</label>
                        <input type="email" class="form-control" id="emailValidate" onfocusout="validEmail()" name="pessoa.email"  aria-label="email" aria-describedby="email">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher campo com o email.
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" id="rua">Endereço</label>
                        <input type="text" class="form-control" placeholder="" id="enderecoValidate" onfocusout="validEndereco()" name="pessoa.endereco" aria-label="rua" aria-describedby="rua">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Especifique a rua, nº da casa.
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="bg-white px-5 py-4 shadow-sm rounded-4 mb-3">
            <legend>Dados Acadêmicos</legend>
            <div class="row mb-3">
                <div class="col">
                    <div class="">
                        <label class="form-label">Instituto</label>
                        <select class="form-select" placeholder="" aria-label="instituto" name="entidade.id" id="instituto" aria-describedby="instituto">
                            <option th:each="instituto : ${Institutos}" th:value="${instituto.id}"
                                    th:text="${instituto.designacao}"></option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label">Faculdade</label>
                        <select class="form-select" placeholder="" aria-label="faculdade" id="faculdade" name="faculdade.id" aria-describedby="faculdade">
                            <!--<option th:each="item : ${faculdades}" th:value="${item.id}" th:text="${item.designacao}"></option>-->
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class=" ">
                        <label class="form-label" id="nivelAcademico">Nível Acadêmico</label>
                        <select class="form-select" placeholder="" aria-label="nivelAcademico" name="nivelAcademico.id" aria-describedby="nivelAcademico">
                            <option th:each="nivel : ${niveis}" th:value="${nivel.id}"
                                    th:text="${nivel.designacao}"></option>
                        </select>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="bg-white px-5 py-4 shadow-sm rounded-4 mb-3">
            <legend>Dados da Conta</legend>
            <div class="row mb-3">
                <div class="col-4">
                    <div class="">
                        <label class="form-label" id="usuario">Username</label>
                        <input type="text" class="form-control" placeholder="" name="username" onfocusout="validUsuario()" id="usuarioValidate" aria-label="usuario" aria-describedby="usuario">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher campo com o nome de usuario.
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="">
                        <label class="form-label" id="emailConta">E-mail</label>
                        <input type="email" class="form-control" placeholder="" id="emailLoginValidate" onfocusout="validEmailLogin()" name="email" aria-label="emailConta" aria-describedby="emailConta">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher campo com o email de login.
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="">
                        <label class="form-label" id="senha">Senha</label>
                        <input type="password" class="form-control" placeholder="" id="senhaValidate" onfocusout="validSenha()" name="senha" aria-label="senha" aria-describedby="senha">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher campo com a nova senha da conta.
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <!--<div class="col-4">
                    <div class="input-group ">
                        <span class="input-group-text" id="tipoConta">Tipo de Conta</span>
                        <select class="form-select" placeholder="" aria-label="tipoConta" aria-describedby="tipoConta"></select>
                    </div>
                </div>-->

                <div class="col-4">
                    <div>
                        <label class="form-label">Confirmar Senha</label>
                        <input type="password" class="form-control" placeholder="" id="senhaconfirmaValidate" onfocusout="validSenhaConfirma()" name="senha1" aria-label="senha1" aria-describedby="senha1">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher campo com mesma senha para confirmar.
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <div class="col-12 d-flex flex-row justify-content-end">
            <button class="btn btn-warning mb-3 me-2 px-3 shadow-sm rounded-4" type="submit">Limpar</button>
            <button class="btn mb-3 shadow-sm rounded-4 text-white" type="submit" style="background-color: #AB81A3" id="enviarButton">Registrar</button>
        </div>
    </form>
</div>
<div class="toast-container position-fixed bottom-0 end-0 p-3 "  data-bs-autohide="false">
    <div id="liveToast" class="toast  border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto" id="titlo-alerta">MusalaLinkUp</strong>
            <small class="text-body-secondary">Agora</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="mensagem">
            Seja bem vindo a nossa plataforma, faça login ou registra-te para ver projectos divulgados e muito mais...
        </div>
    </div>
</div>
<script>

    window.onload = (event) => {
       getMunicipios();

        getFaculdades();
        getBairros();

    };

    $(document).ready(function () {
                $("#provincia").change(function () {
                    $("#municipio").empty();

                    getMunicipios();
                })

                $("#municipio").change(function () {
                    $("#bairro").empty();
                    getBairros();
        })

        $("#instituto").change(function () {
            $("#faculdade").empty();
            getFaculdades();
        })
    })

    function getFaculdades() {
        institutoId = $("#instituto").val();
        url = "http://localhost:8080/entidadeFaculdadeAjax/" + institutoId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                faculdades = $("#faculdade")
                $.each(responseJson, function (index, entidadeFaculdade) {
                    $("<option>")
                        .val(entidadeFaculdade.id.faculdade.id)
                        .text(entidadeFaculdade.id.faculdade.designacao)
                        .appendTo(faculdades);

                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }

    function getMunicipios() {
        provinciaId = $("#provincia").val();
        url = "http://localhost:8080/localidadesAjax/" + provinciaId;

        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                municipios = $("#municipio")
                $.each(responseJson, function (index, localidade) {
                    $("<option>")
                        .val(localidade.id)
                        .text(localidade.designacao)
                        .appendTo(municipios);

                })
                getBairros()
            })
            .fail(function () {
            })
            .always(function () {
            });
    }


    function getBairros() {
        municipioId = $("#municipio").val();
        url = "http://localhost:8080/localidadesAjax/" + municipioId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                bairros = $("#bairro")
                $.each(responseJson, function (index, localidade) {
                    $("<option>")
                        .val(localidade.id)
                        .text(localidade.designacao)
                        .appendTo(bairros);

                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }

    function verificarContaExiste() {
        projectoId = $("#projecto").val();
        url = "http://localhost:8080/projectoAjax/contas/" + projectoId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                investigadoresSelect = $("#investigadores")
                $.each(responseJson, function (index, investigadores) {
                    $("<option>")
                        .text(investigadores.pessoa.nome+" "+investigadores.pessoa.apelido)
                        .val(investigadores.id)
                        .appendTo(investigadoresSelect)

                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }

    function validNome(){
        info = document.forms["myForm"]["pessoa.nome"].value;
        component = $("#nomeValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validApelido(){
        info = document.forms["myForm"]["pessoa.apelido"].value;
        component = $("#apelidoValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataNascimento(){
        info = document.forms["myForm"]["pessoa.dataNascimento"].value;
        component = $("#dataNascimento")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validBi(){
        info = document.forms["myForm"]["pessoa.bi"].value;
        component = $("#identidade")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataEmissao(){
        info = document.forms["myForm"]["pessoa.dataEmissao"].value;
        component = $("#dataEmissao")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataValidade(){
        info = document.forms["myForm"]["pessoa.dataValidade"].value;
        component = $("#dataValidade")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validContacto(){
        info = document.forms["myForm"]["pessoa.contacto"].value;
        component = $("#contactoValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validEmail(){
        info = document.forms["myForm"]["pessoa.email"].value;
        component = $("#emailValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validEndereco(){
        info = document.forms["myForm"]["pessoa.endereco"].value;
        component = $("#enderecoValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validUsuario(){
        info = document.forms["myForm"]["username"].value;
        component = $("#usuarioValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validEmailLogin(){
        info = document.forms["myForm"]["email"].value;
        component = $("#emailLoginValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenha(){
        info = document.forms["myForm"]["senha"].value;
        component = $("#senhaValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenhaConfirma(){
        info = document.forms["myForm"]["senha1"].value;
        component = $("#senhaconfirmaValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function verificarExisteConta() {
        email = document.forms["myForm"]["username"].value;
        alert(email)
        url = "http://localhost:8080/usuarioAjax/"+email;
        $.ajax({method: "GET",
            url,
            async:false,
            success: function (data){
            alert(data)
                return data;
            }
        })
    }

    var dadosGlobais;

    function fazerRequisicao() {
        email = document.forms["myForm"]["username"].value;

        url = "http://localhost:8080/usuarioAjax/"+email;
        // Retorna uma Promise que resolve quando a requisição é bem-sucedida
        return new Promise((resolve, reject) => {
            // Faz a requisição usando a função fetch
            fetch(url)
                .then(response => {

                    // Verifica se a resposta da requisição é bem-sucedida (status 2xx)
                    if (!response.ok) {
                        throw new Error('Erro na requisição');
                    }
                    // Parseia a resposta como JSON
                    return response.json();
                })
                .then(data => {
                    // Armazena os dados na variável global
                    dadosGlobais = data;
                    // Resolve a Promise
                    submitValidation = true
                    resolve(submitValidation);
                })
                .catch(error => {
                    // Rejeita a Promise em caso de erro
                    reject(error);
                });
        });
    }

    function enviarFormulario(){
        document.getElementById("myForm").addEventListener("submit", function(event) {
            event.preventDefault(); // Impede o comportamento padrão do formulário (recarregar a página)
            // Realize operações adicionais aqui, se necessário
        })
    }

     function validateForm() {
        const toastLiveExample = document.getElementById('liveToast')
        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        let submit = false;
         fazerRequisicao().then((submitValidation) => {
            nome = document.forms["myForm"]["pessoa.nome"].value;
            apelido = document.forms["myForm"]["pessoa.apelido"].value;
            dtNascimento = document.forms["myForm"]["pessoa.dataNascimento"].value;
            bi = document.forms["myForm"]["pessoa.bi"].value;
            dtEmissao = document.forms["myForm"]["pessoa.dataEmissao"].value;
            dtValidade = document.forms["myForm"]["pessoa.dataValidade"].value;
            contacto = document.forms["myForm"]["pessoa.contacto"].value;
            email = document.forms["myForm"]["pessoa.email"].value;
            endereco = document.forms["myForm"]["pessoa.endereco"].value;
            username = document.forms["myForm"]["username"].value;
            email2 = document.forms["myForm"]["email"].value;
            senha = document.forms["myForm"]["senha"].value;
            senhaConfirmar = document.forms["myForm"]["senha1"].value;
            senha = $("#senhaValidate").val()
            confirmarSenha = $("#senhaconfirmaValidate").val()
            if (nome == ""
            || apelido == ""
            || dtNascimento == ""
            || bi ==""
            || dtEmissao == ""
            || dtValidade == ""
            || contacto == ""
            || email == ""
            || endereco == ""
            || username == ""
            || email2 == ""
            || senha == ""
            || senhaConfirmar == ""
            || senha != confirmarSenha) {
                validNome()
                validApelido()
                validBi()
                validContacto()
                validDataEmissao()
                validDataNascimento()
                validDataValidade()
                validEmail()
                validEmailLogin()
                validEndereco()
                validSenha()
                validSenhaConfirma()
                validUsuario()
                //confirmar senha
                if(senha != confirmarSenha){

                    $("#liveToast").addClass("text-bg-danger")
                    $("#titlo-alerta").empty()
                    $("#titlo-alerta").text("Erro ao criar conta estudante")
                    $("#mensagem").empty()
                    $("#mensagem").text("A senha deve ser a mesma no campo confirmar senha")
                    toastBootstrap.show()
                    submit = false;
                }
                setTimeout(() => {
                    const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
                    $("#liveToast").addClass("text-bg-danger")
                    $("#liveToast").removeClass("text-bg-warning")
                    $("#titlo-alerta").empty()
                    $("#titlo-alerta").text("Erro ao criar conta estudante")
                    $("#mensagem").empty()
                    $("#mensagem").text("Verifique os campos se estão vazios ou devidamente preenchidos.")
                    toastBootstrap.show()
                    submit = false;
                },4000)
            }
            else if(dadosGlobais)
            {
                submit = false;
                const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
                $("#liveToast").addClass("text-bg-warning")
                $("#liveToast").removeClass("text-bg-danger")
                $("#titlo-alerta").empty()
                $("#titlo-alerta").text("Erro ao criar conta estudante")
                $("#mensagem").empty()
                $("#mensagem").text("Já existe uma conta com este username")
                toastBootstrap.show()
            }
            else {
                submit = submitValidation;
                $("#liveToast").removeClass("text-bg-danger")
                $("#liveToast").removeClass("text-bg-warning")
                $("#titlo-alerta").empty()
                $("#titlo-alerta").text("MusalaLinkUp")
                $("#mensagem").empty()
                $("#mensagem").text("A tua conta foi criada com sucesso. Agora podes iniciar sua sessão.")
                toastBootstrap.show()
                setTimeout(() => {
                    document.myForm.submit()

                },5000)
            }
        })
    return submit;
    }
</script>
</body>
</html>