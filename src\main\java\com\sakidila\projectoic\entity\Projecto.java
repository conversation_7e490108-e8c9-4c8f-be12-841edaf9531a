package com.sakidila.projectoic.entity;


import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Entity
@Getter
@Setter
public class Projecto {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Lob
    private String logotipo;

    private String designacao;

    @Lob
    @Column(name="descricão")
    private String descricao;

    @Column(columnDefinition = "boolean default false")
    @Nullable
    private Boolean ehAprovado;

    @Column(columnDefinition = "boolean default false")
    @Nullable
    private Boolean ehDesenvolvimento;

    @Column
    private double orcamento;

    @Column
    private String orcamentoTexto;

    @Column(columnDefinition = "boolean default false")
    @Nullable
    private boolean ehInvestido;

    @Column(columnDefinition = "boolean default false")
    @Nullable
    private boolean ehDeInvestimento;

    @Temporal(TemporalType.DATE)
    private Date dataCriacao;

    @Temporal(TemporalType.TIMESTAMP)
    private LocalDateTime createdAt;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "fk_tipo_projecto")
    private TipoProjecto tipoProjecto;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "fk_centro")
    private Centro centro;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "fk_gestor")
    private Conta gestor;

    @JsonIgnore
    @OneToOne
    @JoinColumn(name = "fk_documento")
    private Documento documento;

    @ManyToOne
    @JoinColumn(name = "fk_metodologia")
    private Metodologia metodologia;

    @JsonIgnore
    @OneToMany(mappedBy = "projecto")
    private List<Tarefa> tarefas;

    @Column(columnDefinition = "boolean default false")
    @Nullable
    private boolean disabled;



}
