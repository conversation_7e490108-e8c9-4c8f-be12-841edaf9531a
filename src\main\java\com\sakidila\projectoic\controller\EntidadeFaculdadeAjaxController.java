package com.sakidila.projectoic.controller;

import com.sakidila.projectoic.entity.EntidadeFaculdade;
import com.sakidila.projectoic.service.EntidadeFaculdadeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/entidadeFaculdadeAjax")
public class EntidadeFaculdadeAjaxController {

    @Autowired
    private EntidadeFaculdadeService service;

    @GetMapping("/{id}")
    public List<EntidadeFaculdade> encontrarFaculdadesPeloInstituto(@PathVariable Long id){
        List<EntidadeFaculdade> lista = new ArrayList<>();
            lista = service.ListaEntidadeFaculdadePelaInstituto(id);
        return lista;
    }

}
