package com.sakidila.projectoic.controller;

import com.sakidila.projectoic.entity.EntidadeFaculdade;
import com.sakidila.projectoic.entity.InvestidorProjecto;
import com.sakidila.projectoic.service.InvestidorProjectoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

@RestController
@RequestMapping("/sakidila_ic/investidor_projecto")
public class InvestidorProjectoAjaxController {

    @Autowired
    private InvestidorProjectoService service;

    @GetMapping("/{id}")
    public List<InvestidorProjecto> encontrarProjectosPeloIdInvestidor(@PathVariable Long id){
        List<InvestidorProjecto> lista = new ArrayList<>();
            lista = service.encontrarProjectosPeloIdInvestidor(id);
        return lista;
    }

    @GetMapping("/investidores/{id}")
    public List<InvestidorProjecto> encontrarInvestidoresPeloIdProjecto(@PathVariable Long id){
        List<InvestidorProjecto> lista = new ArrayList<>();
        lista = service.encontrarInvestidoresPeloIdProjecto(id);
        return lista;
    }

    @PostMapping("/salvar")
    public String salvar(@ModelAttribute InvestidorProjecto investidorProjecto){
         InvestidorProjecto resultado = service.criarInvestidorProjecto(investidorProjecto);
         return "redirect:/sakidila_ic/portal/listagem/1";
    }

}
