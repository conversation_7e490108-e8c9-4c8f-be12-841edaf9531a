package com.sakidila.projectoic.repository;

import com.sakidila.projectoic.entity.Projecto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional
public interface ProjectoRespository extends JpaRepository<Projecto,Long>, JpaSpecificationExecutor<Projecto> {


    @Modifying
    @Query("update Projecto p set p.ehInvestido = true where p.id=:idProjecto")
    public void fecharInvestimento(Long idProjecto);

    public List<Projecto> findFirst4By();
    public List<Projecto> findTop4ByEhDeInvestimentoIsTrueAndEhInvestidoIsFalseOrderByOrcamentoDesc();
    public List<Projecto> findTop4ByOrderByCreatedAtDesc();
    public List<Projecto> findAllByGestorId(Long idGestor);
    public List<Projecto> findAllByCentroId(Long idCentro);
    public List<Projecto> findAllByEhAprovadoIsFalseAndCentroIsNullAndGestorEntidadeId(Long id);
    public Page<Projecto> findAllByCentroContaEntidadeId(Long idInstituto, Pageable pageable);

    public Page<Projecto> findAllByDisabledIsFalseAndEhAprovadoIsTrueOrderByDesignacaoAsc(Pageable pageable);
    public Page<Projecto> findAllByGestorEntidadeIdAndDisabledFalseAndEhAprovadoIsTrueOrderByDesignacaoAsc(Long idInstituto, Pageable pageable);

    public Page<Projecto> findAllByGestorEntidadeIdAndDisabledFalseAndEhAprovadoIsTrueOrderByDesignacaoDesc(Long idInstituto, Pageable pageable);

    public Page<Projecto> findAllByGestorEntidadeIdAndDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsTrueOrderByDesignacaoDesc(Long idInstituto, Pageable pageable);

    public Page<Projecto> findAllByDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsTrueOrderByDesignacaoDesc(Pageable pageable);

    public Page<Projecto> findAllByDisabledFalseAndEhAprovadoIsTrueOrderByDesignacaoDesc(Pageable pageable);

    public Page<Projecto> findAllByDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsTrueOrderByDesignacaoAsc(Pageable pageable);

    public Page<Projecto> findAllByDisabledFalseAndEhAprovadoIsTrueOrderByDataCriacaoDesc(Pageable pageable);


    public Page<Projecto> findAllByDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsFalseOrderByDesignacaoAsc(Pageable pageable);

    public Page<Projecto> findAllByGestorEntidadeIdAndDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsFalseOrderByDesignacaoDesc(Long idInstituto, Pageable pageable);

    public Page<Projecto> findAllByDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsFalseOrderByDesignacaoDesc(Pageable pageable);

    public Page<Projecto> findAllByGestorEntidadeIdAndDisabledFalseAndEhAprovadoIsTrueOrderByDataCriacaoDesc(Long idInstituto, Pageable pageable);

    public Page<Projecto> findAllByGestorEntidadeIdAndDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsTrueOrderByDataCriacaoDesc(Long idInstituto, Pageable pageable);

    public Page<Projecto> findAllByDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsTrueOrderByDataCriacaoDesc(Pageable pageable);

    public Page<Projecto> findAllByGestorEntidadeIdAndDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsFalseOrderByDataCriacaoDesc(Long idInstituto, Pageable pageable);

    public Page<Projecto> findAllByDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsFalseOrderByDataCriacaoDesc(Pageable pageable);

    @Modifying
    @Query("update Projecto p set p.disabled = true where p.id =:idProjecto")
    void disabledByIdProjecto(Long idProjecto);
}
