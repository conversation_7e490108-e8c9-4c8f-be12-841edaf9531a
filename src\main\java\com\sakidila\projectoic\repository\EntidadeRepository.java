package com.sakidila.projectoic.repository;

import com.sakidila.projectoic.entity.Entidade;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional
public interface EntidadeRepository extends JpaRepository<Entidade,Long> {

    @Query("SELECT e FROM Entidade e WHERE e.tipo.id=2")
    public List<Entidade> findAllInstituto();

    @Modifying
    @Query("update Entidade e set e.disabled = true where e.id =:idEntidade")
    public void disabledByIdEntidade(Long idEntidade);

}
