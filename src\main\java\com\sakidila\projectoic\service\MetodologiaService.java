package com.sakidila.projectoic.service;

import com.sakidila.projectoic.entity.Metodologia;
import com.sakidila.projectoic.entity.TipoProjecto;
import com.sakidila.projectoic.repository.MetodologiaRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class MetodologiaService {

    @Autowired
    private MetodologiaRepository repository;

        public Metodologia criarMetodologia(Metodologia metodologia){
            return repository.save(metodologia);
        }

        public List<Metodologia> listaTodasMetodologias(){
            return repository.findAll();
        }

        public List<Metodologia> listarFasesPeloIdMetodologia(Long id){
            return repository.findAllByFasePaiId(id);
        }

        public List<Metodologia> listaMetodologias(){ return  repository.findAllByFasePaiIsNull();}

    public Metodologia findById(Long chave) {
        Optional<Metodologia> optional = repository.findById(chave);
        if (optional.isPresent()) {
            return optional.get();
        }
        return null;
    }
}
