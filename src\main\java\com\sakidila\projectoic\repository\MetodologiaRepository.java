package com.sakidila.projectoic.repository;

import com.sakidila.projectoic.entity.Metodologia;
import com.sakidila.projectoic.entity.TipoProjecto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional
public interface MetodologiaRepository extends JpaRepository<Metodologia,Long> {

    //Encontrar todas metodologias
    public List<Metodologia> findAllByFasePaiIsNull();

    //Encontrar todas subfases de uma fase
    public List<Metodologia> findAllByFasePaiId(Long idFase);

}
