package com.sakidila.projectoic.repository;

import com.sakidila.projectoic.entity.Tarefa;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional
public interface TarefaRepository extends JpaRepository<Tarefa,Long> {

    public List<Tarefa> findAllByFaseId(Long id);
    public List<Tarefa> findAllByInvestigadorId(long id);
    public List<Tarefa> findAllByEstadoIdEstado(long id);
    public List<Tarefa> findAllByProjectoId(Long id);
}
