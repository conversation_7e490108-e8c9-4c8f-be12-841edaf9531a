<!DOCTYPE html>
<html lang="en"  xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">

    <!-- Importação do Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
            crossorigin="anonymous"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
    <style th:inline="text">
        body{
            font-family: Poppins;
        }
    </style>
    <title>Registra-se na Sakidila!</title>
</head>
<body class="bg-light row" style="overflow-x: hidden" >


<!--Menu da App-->
<div class="col-xl-2 col-lg-3 col-md-4">
<div class="d-flex flex-column flex-shrink-0 p-3 bg-white shadow-sm position-fixed" style="width: 280px; height: 100vh">
    <a href="/sakidila_ic/portal/" class="d-flex align-items-center mb-3 mb-md-0 me-md-auto link-body-emphasis text-decoration-none">
        <img src="/img/logotipo.jpg" alt="Bootstrap" width="100"> <span class="fw-bold text-dark text-center">Gestão Acadêmica</span></a>
    </a>
    <hr>
    <ul class="nav nav-pills flex-column mb-auto">
        <li class="nav-item">
            <a href="/sakidila_ic/portal/" class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Inicio
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/entidade/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == adminPerfil}" class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Entidade
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/entidade_faculdade/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link active" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Faculdade
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/usuario/gestor_instituicao_registrar"
               th:if="${dadoConta.tipoConta.designacao == adminPerfil}"
               class="nav-link link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#table"></use></svg>
                Gestor de Instituição
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/centro/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link  link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use h:href="/sakidila_ic/centro/registrar"></use></svg>
                Centro de Investigação
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/centro/investigador/associar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil}"
               class="nav-link  link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use h:href="/sakidila_ic/centro/registrar"></use></svg>
                Centro de Investigação
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/portal/projecto/registrar"
               th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil
               or dadoConta.tipoConta.designacao == gestorProjectoPerfil
               or dadoConta.tipoConta.designacao == investigadorPerfil}"
               class="nav-link">
                <svg class="bi pe-none me-2" width="16" height="16"><use href="/sakidila_ic/centro/registrar"></use></svg>
                Projectos
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/portal/projecto/pendentes/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link">
                <svg class="bi pe-none me-2" width="16" height="16"><use href="/sakidila_ic/centro/registrar"></use></svg>
                Projectos pendentes
            </a>
        </li>
        <li>
            <a href="#" class="nav-link link-body-emphasis"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}">
                <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#table"></use></svg>
                Estudante
            </a>
        </li>
    </ul>
    <hr>
    <div class="dropdown">
        <a href="#" class="d-flex align-items-center link-body-emphasis text-decoration-none dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
            <img th:if="${conta == userDefault}" th:src="@{/img/user.png}" alt="mdo" width="32" height="32" class="rounded-circle">
            <img th:if="${conta != null}" th:src="${dadoConta.foto}" alt="mdo" width="32" height="32" class="rounded-circle">
            <strong>[[${dadoConta.username}]]</strong>
        </a>
        <ul class="dropdown-menu text-small shadow">
            <li><a th:if="${conta == userDefault}" class="dropdown-item" href="/sakidila_ic/usuario/login">Entrar</a></li>
            <li><a th:if="${conta != null}" class="dropdown-item" href="">Meu Perfil</a></li>
            <li><a th:if="${conta != null}" class="dropdown-item" href="/logout">Sair</a></li>
        </ul>
    </div>
</div>
</div>

<div class="col-xl-10 col-lg-9 col-md-8">
<div class="container">
    <div class="d-flex flex-row mt-4 justify-content-end">
        <button class="me-2" style="border:none; background-color:transparent;text-decoration: none; color: #7c6aa2"
        data-bs-target="#registrarFaculdade" data-bs-toggle="modal">Associar faculdades</button>
    </div>
    <!-- Modal -->
    <form method="post" th:action="@{/sakidila_ic/entidade_faculdade/salvar}">
    <div class="modal fade" id="registrarFaculdade" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="" id="staticBackdropLabel"><h5 class="modal-title fs-4 fw-bold" style="color: rgba(158,158,151)">Atribuir faculdades à<i class="fw-bold text-dark"> Instituição!</i></h5></h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <fieldset class=" px-5 py-4 mb-3">

                            <div class="row mb-3">
                                <div class="col">
                                    <div class=" mb-4">
                                        <h5 class="fw-bolder" style="color: #AB81A3">Instituição</h5>
                                        <h5 class="" th:text="${instituto.designacao}"></h5>
                                        <input type="hidden" name="idEntidade" style="display: none" th:value="${instituto.id}">
                                    </div>
                                    <label class="form-label">Selecione as respectivas faculdades</label>
                                    <div class="input-group">
                                        <span class="input-group-text" id="apelido">Faculdades</span>
                                        <select class="form-select" name="idFalcudade">
                                            <option th:each="faculdade : ${faculdades}" th:value="${faculdade.id}"
                                                    th:text="${faculdade.designacao}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                    </fieldset>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button class="btn btn-warning  shadow-sm" type="reset">Limpar</button>
                    <button class="btn btn-primary shadow-sm" type="submit">Adicionar</button>
                </div>
            </div>
        </div>
    </div>
    </form>


    <h5 class="display-6 mt-1 mb-1 fw-bold" style="color: rgba(158,158,151)">Visualização <i class="fw-bold text-dark"></i></h5>
    <hr>
    <fieldset class="bg-white px-5 py-4 shadow-sm rounded mb-3">
        <table class="table">
            <tr>
                <th scope="col">Instituição</th>
                <th scope="col">Faculdade</th>
                <th scope="col">Remover</th>
            </tr>
            <tr th:each="item : ${entidadesFaculdades}" th:if="${!item.disabled}">
                <td>[[${item.id.instituto.designacao}]]</td>
                <td>[[${item.id.faculdade.designacao}]]</td>
                <td><a class="btn btn-danger btn-sm shadow-sm" th:href="@{/sakidila_ic/entidade_faculdade/desativar/{idInstituto}/{idFaculdade}(idInstituto=${item.id.instituto.id},idFaculdade=${item.id.faculdade.id})}">Remover</a></td>
            </tr>
        </table>
        <div class="d-flex justify-content-center">
            <nav class="Page navigation example" th:if="${totalPaginas > 1}">
                <ul class="pagination">
                    <li class="page-item"><a class="page-link">Total de projectos: [[${totalItems}]]</a></li>
                    <div th:each="i : ${#numbers.sequence(1,totalPaginas)}">
                        <li class="page-item">
                            <a class="page-link" th:if="${paginaActual != i}"
                               th:href="@{'/sakidila_ic/entidade_faculdade/registrar/'+ ${i}}">[[${i}]]</a>
                            <a class="page-link" th:unless="${paginaActual != i}">[[${i}]]</a> &nbsp; &nbsp;
                        </li>
                    </div>
                    <li class="page-item">
                        <a class="page-link" th:if="${paginaActual < totalPaginas}"
                           th:href="@{'/sakidila_ic/entidade_faculdade/registrar/'+${paginaActual + 1}}">Próximo</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" th:if="${paginaActual < totalPaginas}"
                           th:href="@{'/sakidila_ic/entidade_faculdade/registrar/'+${paginaActual + 1}}">Ultimo</a>
                        <a class="page-link" th:unless="${paginaActual < totalPaginas}">Ultimo</a>
                    </li>
                </ul>
            </nav>
        </div>
    </fieldset>



</div>
</div>

<script>

    window.onload = (event) => {
        $("#municipio").empty();
        getMunicipios();
        $("#faculdade").empty();
        getFaculdades();
    }

    $(document).ready(function () {
        $("#provincia").change(function () {
            $("#municipio").empty();
            getMunicipios();
        })

        $("#municipio").change(function () {
            $("#bairro").empty();
            getBairros();
        })

        $("#instituto").change(function () {
            $("#faculdade").empty();
            getFaculdades();
        })
    })

    function getFaculdades() {
        institutoId = $("#instituto").val();
        url = "http://localhost:8080/entidadeFaculdadeAjax/" + institutoId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                faculdades = $("#faculdade")
                $.each(responseJson, function (index, entidadeFaculdade) {
                    $("<option>")
                        .val(entidadeFaculdade.id.faculdade.id)
                        .text(entidadeFaculdade.id.faculdade.designacao)
                        .appendTo(faculdades);

                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }

    function getMunicipios() {
        provinciaId = $("#provincia").val();
        url = "http://localhost:8080/localidadesAjax/" + provinciaId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                municipios = $("#municipio")
                $.each(responseJson, function (index, localidade) {
                    $("<option>")
                        .val(localidade.id)
                        .text(localidade.designacao)
                        .appendTo(municipios);

                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }


    function getBairros() {
        municipioId = $("#municipio").val();
        url = "http://localhost:8080/localidadesAjax/" + municipioId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                bairros = $("#bairro")
                $.each(responseJson, function (index, localidade) {
                    $("<option>")
                        .val(localidade.id)
                        .text(localidade.designacao)
                        .appendTo(bairros);

                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }
</script>
</body>
</html>