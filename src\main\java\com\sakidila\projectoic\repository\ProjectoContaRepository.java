package com.sakidila.projectoic.repository;

import com.sakidila.projectoic.entity.Curso;
import com.sakidila.projectoic.entity.EntidadeFaculdade;
import com.sakidila.projectoic.entity.Projecto;
import com.sakidila.projectoic.entity.ProjectoConta;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
@Transactional
public interface ProjectoContaRepository extends JpaRepository<ProjectoConta,Long> {
    public List<ProjectoConta> findAllByChaveContaId(Long idConta);
    public List<ProjectoConta> findAllByChaveProjectoId(Long idProjecto);

    @Query("select pc FROM ProjectoConta pc where pc.chave.projecto.id =:idProjecto and pc.chave.conta.id =:idConta")
    Optional<ProjectoConta> findByIdProjectoAndIdConta(Long idProjecto, Long idConta);

    @Modifying
    @Query("update ProjectoConta pc set pc.disabled = true where pc.chave.projecto.id =:idProjecto and pc.chave.conta.id =:idConta")
    void disabledByIdProjectoAndIdConta(Long idProjecto,Long idConta);


}
