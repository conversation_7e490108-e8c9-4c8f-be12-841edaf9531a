package com.sakidila.projectoic.entity;

import jakarta.annotation.Nullable;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import lombok.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EntidadeFaculdade{

    @EmbeddedId
    private EntidadeFaculdadeId id;

    @Column(columnDefinition = "boolean default false")
    @Nullable
    private boolean disabled;
}
