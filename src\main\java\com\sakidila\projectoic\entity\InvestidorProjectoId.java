package com.sakidila.projectoic.entity;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Embeddable;
import jakarta.persistence.ManyToOne;
import lombok.*;

import java.io.Serializable;

@Embeddable
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
public class InvestidorProjectoId implements Serializable {

    @ManyToOne(cascade = CascadeType.REMOVE)
    private Conta investidor;

    @ManyToOne(cascade = CascadeType.REMOVE)
    private Projecto projecto;
}
