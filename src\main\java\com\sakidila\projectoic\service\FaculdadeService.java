package com.sakidila.projectoic.service;

import com.sakidila.projectoic.entity.Conta;
import com.sakidila.projectoic.entity.Entidade;
import com.sakidila.projectoic.entity.EntidadeFaculdade;
import com.sakidila.projectoic.entity.Faculdade;
import com.sakidila.projectoic.repository.FaculdadeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class FaculdadeService {

    @Autowired
    private FaculdadeRepository repository;

        public Faculdade criarFaculdade(Faculdade faculdade){
            return repository.save(faculdade);
        }

        public List<Faculdade> listaFaculdades(){
            return repository.findAll();
        }

        public Faculdade findById(Long id){return repository.findById(id).get();}

    public List<Faculdade> encontrarTodasFaculdadePeloInstituto(List<EntidadeFaculdade> entidadeFaculdades){
        List<Faculdade> faculdades = new ArrayList<>();
        for (EntidadeFaculdade entidadeFaculdade:entidadeFaculdades) {
            faculdades.add(entidadeFaculdade.getId().getFaculdade());
        }
        return faculdades;
    }

    public Faculdade desativar(Long id){
        Faculdade faculdade = repository.findById(id).get();
        if(faculdade!=null)
            repository.disabledByIdFaculdade(id);
        return faculdade;
    }
}
