/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/springframework/Service.java to edit this template
 */
package com.sakidila.projectoic.service;

import com.sakidila.projectoic.entity.Curso;
import com.sakidila.projectoic.repository.CursoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 *
 * <AUTHOR> Mona
 */
@Service
public class CursoServico {
    
    @Autowired
    private CursoRepository repositorio;

    public Curso salvar(Curso curso) {
        return repositorio.save(curso);
    }
    
    public Curso ApagarPelaChave(Long chave) {
        Optional<Curso> optional = repositorio.findById(chave);
        if (optional.isPresent()) {
            repositorio.deleteById(chave);
            return optional.get();
        }
        return null;
    }

    public Page<Curso> encontrarPagina(int pagina, int tamanho) {
        Pageable pageable = PageRequest.of(pagina - 1, tamanho, Sort.by("designacao"));
        return  repositorio.findAll(pageable);
    }

    public List<Curso> listarTodos(){
        return repositorio.findAll();
    }

    
    public Curso editar(Curso Curso) {
        Optional<Curso> optional = repositorio.findById(Curso.getId());
        if (optional.isPresent()) {
            repositorio.save(Curso);
            return optional.get();
        }
        return null;
    }
    
    public Curso findById(Long chave) {
        Optional<Curso> optional = repositorio.findById(chave);
        if (optional.isPresent()) {
            return optional.get();
        }
        return null;
    }

    public Curso deletar(Long id){
        Curso entidade = repositorio.findById(id).get();
        if(entidade!=null)
            repositorio.deleteById(id);
        return entidade;
    }
}
