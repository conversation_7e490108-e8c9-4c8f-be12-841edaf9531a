package com.sakidila.projectoic.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Getter
@Setter
public class Documento {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idDocumento;

    private String designacao;

    @Lob
    private byte[] ficheiro;

    private LocalDateTime dataCriacao;


}
