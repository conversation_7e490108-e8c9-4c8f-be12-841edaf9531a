package com.sakidila.projectoic.controller;

import com.sakidila.projectoic.entity.Conta;
import com.sakidila.projectoic.entity.Estado;
import com.sakidila.projectoic.entity.Projecto;
import com.sakidila.projectoic.entity.Tarefa;
import com.sakidila.projectoic.repository.EstadoRepository;
import com.sakidila.projectoic.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Controller
@RequestMapping("sakidila_ic/portal")
public class PortalController {

    @Autowired
    private ContaService contaService;

    @Autowired
    private TipoContaService tipoContaService;

    @Autowired
    private EntidadeServico entidadeServico;

    @Autowired
    private ProjectoServico projectoServico;

    @Autowired
    private TarefaService tarefaService;

    @Autowired
    private MetodologiaService metodologiaService;

    @Autowired
    private EstadoRepository estadoRepository;

    @GetMapping("/")
    public String index(Model model) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        tipoContaService.criarTipoContaPadrao();
        contaService.criarContaPadrao();
        System.out.println("dados :"+authentication.getName());
        if (authentication.isAuthenticated() && !authentication.getName().equals("anonymousUser")) {
            Conta conta = contaService.mostrarDadoConta(authentication.getName());
            model.addAttribute("conta", authentication.getName());
            model.addAttribute("userDefault", "anonymousUser");
            model.addAttribute("dadoConta", conta);
            model.addAttribute("gestorCentroPerfil", "ROLE_RESPONSAVEL_CENTRO");
            model.addAttribute("investigadorPerfil", "ROLE_INVESTIGATOR");
            model.addAttribute("gestorInstituicaoPerfil","ROLE_GESTOR_INSTITUICAO");
            model.addAttribute("adminPerfil","ROLE_ADMIN");
            model.addAttribute("gestorProjectoPerfil", "ROLE_GESTOR_PROJECTO");
            model.addAttribute("gestorPerfil", "ROLE_GESTOR");
            model.addAttribute("estudantePerfil", "ROLE_ESTUDANTE");
            model.addAttribute("investigadorIndependentePerfil", "ROLE_INVESTIGADOR_INDEPENDENTE");
            model.addAttribute("empresaPerfil","ROLE_EMPRESA");
            model.addAttribute("top",projectoServico.listaProjectosRecentes());

            model.addAttribute("dadoConta", conta);
            System.out.println("dados :"+authentication.getName());
            model.addAttribute("instituicoes",entidadeServico.listaInstitutos());

            return "index";
        }else{
            SecurityContextHolder.clearContext();
            model.addAttribute("conta",null);
            model.addAttribute("dadoConta",null);
            model.addAttribute("instituicoes",entidadeServico.listaInstitutos());
            model.addAttribute("recentes",projectoServico.listaProjectosRecentes());
            model.addAttribute("top",projectoServico.listaProjectostop());
            return "index";
        }

    }

    @GetMapping("/template")
    public String template (Model model) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        System.out.println("dados :"+authentication.getName());
        if (authentication.isAuthenticated() && !authentication.getName().equals("anonymousUser")) {
            Conta conta = contaService.mostrarDadoConta(authentication.getName());
            model.addAttribute("conta", authentication.getName());
            model.addAttribute("userDefault", "anonymousUser");
            model.addAttribute("gestorPerfil", "ROLE_GESTOR");
            model.addAttribute("dadoConta", conta);
            System.out.println("dados :"+authentication.getName() + " tipo:"+conta.getTipoConta().getDesignacao()       );
            return "template";
        }else{
            SecurityContextHolder.clearContext();
            model.addAttribute("conta",null);
            model.addAttribute("dadoConta",null);
            return "login";
        }

    }

    @GetMapping("/modulo_gestao")
    public String gestao (Model model) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        System.out.println("dados :"+authentication.getName());
        if (authentication.isAuthenticated() && !authentication.getName().equals("anonymousUser")) {
            Conta conta = contaService.mostrarDadoConta(authentication.getName());
            List<Estado> estados = estadoRepository.findAll();
            List<Projecto> projectos = projectoServico.encontrarProjectosPeloGestor(conta.getId());
            List<Tarefa> tarefas = tarefaService.encontrarTarefasPelosProjectos(projectos);
            model.addAttribute("conta", authentication.getName());
            model.addAttribute("userDefault", "anonymousUser");
            model.addAttribute("gestorInstituicaoPerfil", "ROLE_GESTOR_INSTITUICAO");
            model.addAttribute("gestorCentroPerfil", "ROLE_RESPONSAVEL_CENTRO");
            model.addAttribute("gestorProjectoPerfil", "ROLE_GESTOR_PROJECTO");
            model.addAttribute("investigadorPerfil", "ROLE_INVESTIGATOR");
            model.addAttribute("estudantePerfil", "ROLE_ESTUDANTE");
            model.addAttribute("empresaPerfil", "ROLE_EMPRESA");
            model.addAttribute("adminPerfil", "ROLE_ADMIN");
            model.addAttribute("projectos",projectos);
            model.addAttribute("tarefas",tarefas);
            model.addAttribute("estados",estados);
            model.addAttribute("dadoConta", conta);
            System.out.println("dados :"+authentication.getName() + " tipo:"+conta.getTipoConta().getDesignacao());
            return "gestor_pagina_principal";
        }else{
            SecurityContextHolder.clearContext();
            model.addAttribute("conta",null);
            model.addAttribute("dadoConta",null);
            return "login";
        }


    }
}

