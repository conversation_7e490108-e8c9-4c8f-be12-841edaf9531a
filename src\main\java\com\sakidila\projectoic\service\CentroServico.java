/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/springframework/Service.java to edit this template
 */
package com.sakidila.projectoic.service;

import com.sakidila.projectoic.entity.Centro;
import com.sakidila.projectoic.entity.Conta;
import com.sakidila.projectoic.entity.Entidade;
import com.sakidila.projectoic.entity.Faculdade;
import com.sakidila.projectoic.repository.CentroRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 *
 * <AUTHOR> Mona
 */
@Service
public class CentroServico {
    
    @Autowired
    private CentroRepository repositorio;

    public Centro salvar(Centro curso) {
        return repositorio.save(curso);
    }

    public List<Centro> findAllCentrosByidFaculdade(Long id){
        return repositorio.findAllByFaculdadeId(id);
    }
    
    public Centro ApagarPelaChave(Long chave) {
        Optional<Centro> optional = repositorio.findById(chave);
        if (optional.isPresent()) {
            repositorio.deleteById(chave);
            return optional.get();
        }
        return null;
    }

    public Page<Centro> encontrarPagina(int pagina, int tamanho) {
        Pageable pageable = PageRequest.of(pagina - 1, tamanho, Sort.by("designacao"));
        return  repositorio.findAll(pageable);
    }

    public Centro findCentroBIdyResponsavel(Long idConta){
            return repositorio.findCentroByContaId(idConta);
    }

    public List<Centro> encontrarTodosCentrosPelaIdInstituicao(List<Faculdade> faculdades, Conta conta){
        List<Centro> lista = new ArrayList<>();

        for (Faculdade faculdade:faculdades) {

            for (Centro centro:faculdade.getListaCentros()) {
                if(conta.getEntidade().getId() == centro.getConta().getEntidade().getId())
                lista.add(centro);
            }
        }
        return lista;
    }

    public List<Centro> listarTodos(){
        return repositorio.findAll();
    }

    
    public Centro editar(Centro Centro) {
        Optional<Centro> optional = repositorio.findById(Centro.getId());
        if (optional.isPresent()) {
            repositorio.save(Centro);
            return optional.get();
        }
        return null;
    }
    
    public Centro findById(Long chave) {
        Optional<Centro> optional = repositorio.findById(chave);
        if (optional.isPresent()) {
            return optional.get();
        }
        return null;
    }


    public Centro desativar(Long id){
        Centro centro = repositorio.findById(id).get();
        if(centro!=null)
            repositorio.disabledByIdCentro(id);
        return centro;
    }

    public Page<?> convertListToPage(int page, int size,List<?> lista) {

        Pageable pageRequest = PageRequest.of(page-1, size);

        int start = (int) pageRequest.getOffset();
        int end = Math.min((start + pageRequest.getPageSize()), lista.size());

        return new PageImpl<>(lista.subList(start, end), pageRequest, lista.size());
    }

}
