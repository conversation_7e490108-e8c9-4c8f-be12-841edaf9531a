package com.sakidila.projectoic.repository;

import com.sakidila.projectoic.entity.Centro;
import com.sakidila.projectoic.entity.EntidadeFaculdade;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional
public interface CentroRepository extends JpaRepository<Centro,Long> {

    public Centro findCentroByContaEntidadeId(Long id);

    public List<Centro> findAllByFaculdadeId(Long id);

    Centro findCentroByContaId(Long idConta);

    @Modifying
    @Query("update Centro c set c.disabled = true where c.id =:idCentro")
    public void disabledByIdCentro(Long idCentro);
}
