package com.sakidila.projectoic.repository;

import com.sakidila.projectoic.entity.Localidade;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional
public interface LocalidadeRepository extends JpaRepository<Localidade,Long> {
    @Query("SELECT c FROM Localidade c WHERE c.localidadePai IS NULL")
    public List<Localidade> findAllLocalidadesPrimitivas();

    @Query("SELECT c FROM Localidade c WHERE c.localidadePai.id = ?1")
    public List<Localidade> findAllLocalidadesByChaveLocalidadePrimitiva(Long chave);
}
