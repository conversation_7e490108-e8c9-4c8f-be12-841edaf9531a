package com.sakidila.projectoic.repository;

import com.sakidila.projectoic.entity.Entidade;
import com.sakidila.projectoic.entity.EntidadeFaculdade;
import com.sakidila.projectoic.entity.InvestidorProjecto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional
public interface InvestidorProjectoRepository extends JpaRepository<InvestidorProjecto,Long> {

    public List<InvestidorProjecto> findAllByIdProjectoId(Long idProjecto);

    public List<InvestidorProjecto> findAllByIdInvestidorId(Long idInvestidor);

    @Modifying
    @Query("delete from InvestidorProjecto ip where ip.id.projecto.id =:idProjecto and ip.id.investidor.id=:idInvestidor")
    public void deleteInvestidorProjectoByIdProjectoAndIdInvestidor(@Param("idProjecto") Long idProjecto,@Param("idInvestidor") Long idInvestidor);

    @Modifying
    @Query("update InvestidorProjecto ip set ip.ehAprovado = true where ip.id.projecto.id =:idProjecto and ip.id.investidor.id=:idInvestidor")
    public void aprovarInvestidorProjectoByIdProjectoAndIdInvestidor(@Param("idProjecto") Long idProjecto,@Param("idInvestidor") Long idInvestidor);

    Page<InvestidorProjecto> findAllByIdProjectoId(Long idProjecto, Pageable pageable);

}
