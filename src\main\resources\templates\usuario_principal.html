<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"  xmlns:th="http://www.thymeleaf.org">

    <!-- Importação do Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
            crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style th:inline="text">
        body{
            font-family: "poppins";
        }

        ul {list-style-type: none;}
        /* Month header */
        .month {
            padding: 20px 25px;
            width: 100%;
            background: white;
            text-align: center;
        }

        /* Month list */
        .month ul {
            margin: 0;
            padding: 0;
        }

        .month ul li {
            color: black;
            font-size: 20px;
            text-transform: uppercase;
            letter-spacing: 3px;
        }

        /* Previous button inside month header */
        .month .prev {
            float: left;
            padding-top: 10px;
        }

        /* Next button */
        .month .next {
            float: right;
            padding-top: 10px;
        }

        /* Weekdays (Mon-Sun) */
        .weekdays {
            margin: 0;
            padding: 10px 0;
            background-color:#3c8aff;
            box-shadow: 0 0 2px #3c8aff;
            z-index: 5;
        }

        .weekdays li {
            display: inline-block;
            width: 12%;
            color: white;
            text-align: center;
        }

        /* Days (1-31) */
        .days {
            padding: 10px 0;
            background: white;
            margin: 0;
            z-index: 4;
        }

        .days li {
            list-style-type: none;
            display: inline-block;
            width: 12%;
            text-align: center;
            margin-bottom: 5px;
            font-size:12px;
            color: black;
        }

        /* Highlight the "current" day */
        .days li .active {
            padding: 5px;
            background: #3c8aff;
            color: white !important;
            border-radius: 100%;
            box-shadow: 0 0 3px silver;
        }
        .opcao{
            background-color: white;
            transition: background-color 0.5s;
        }

        .opcao:hover{
            background-color: aliceblue;
        }

        .opcao button{
            background-color: aliceblue;
            transition: transform 0.5s ease, background-color 0.5s;
        }

        .opcao:hover button{
            background-color: #AB81A3;
            color: white;
            transform: scale(1.05);
        }
    </style>

    <title>Title</title>
</head>
<body class="" style="margin: 0%;background-color: ghostwhite;font-family: 'Poppins'">
<nav class="navbar navbar-expand-lg p-0 sticky-top" style="margin: 0%;">
    <button class="navbar-toggler ms-2" type="button" data-bs-toggle="collapse" data-bs-target="#navbarTogglerDemo01" aria-controls="navbarTogglerDemo01" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarTogglerDemo01">
        <div class=" d-flex flex-column bg-white shadow-sm" style="width: 100%">
            <div class="container-fluid">
                <div class="container d-flex flex-row align-items-center">
                    <a class="navbar-brand" href="/sakidila_ic/portal/">
                        <img th:src="@{/img/logotipo.jpg}" alt="Bootstrap" width="120">
                    </a>
                    <div class="container d-flex flex-row justify-content-end align-items-center">
                        <a href="/sakidila_ic/usuario/principal" class="nav-link">Registrar</a>
                        <div class="dropdown btn">
                            <a href="#" class="d-block link-body-emphasis text-decoration-none dropdown dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <img th:src="@{/img/user.png}" alt="mdo" width="32" height="32" class="rounded-circle">
                            </a>
                            <ul class="dropdown-menu text-small">
                                <li><a class="dropdown-item" href="/sakidila_ic/usuario/login">Entrar</a></li>
                                <!--<li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#">Sair</a></li>-->
                            </ul>
                        </div>
                        <i class="fs-5 bi bi-facebook me-2" style="color: #7c6aa2"></i>
                        <i class="fs-5 bi bi-instagram me-2" style="color: #7c6aa2"></i>
                        <i class="fs-5 bi bi-whatsapp" style="color: #7c6aa2"></i>
                    </div>
                </div>

            </div>
            <div class="container-fluid py-1" style="background-color: #AB81A3">
                <ul class="container navbar-nav d-flex justify-content-end ">
                    <li class="nav-item">
                        <a class="nav-link text-white active px-3" aria-current="page" href="/sakidila_ic/portal/">Portal</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link text-white  px-3" data-bs-toggle="dropdown" aria-expanded="false" href="#">Instituições</a>
                        <ul class="dropdown-menu rounded-top-0 rounded-bottom-4 border-top-0 text-white" style="background-color: #AB81A3">
                            <li><a class="dropdown-item text-white" href="#"><i class="fst-normal fw-bold">UCAN </i>Universidade Católica de Angola</a></li>
                            <li><a class="dropdown-item text-white" href="#"><i class="fst-normal fw-bold">UAN </i>Universidade Agostinho Neto</a></li>
                            <li><a class="dropdown-item text-white" href="#"><i class="fst-normal fw-bold">UMA </i>Universidade Metódista de Angola</a></li>
                            <li><a class="dropdown-item text-white" href="#"><i class="fst-normal fw-bold">UTANGA </i>Universidade Técnica de Angola</a></li>
                            <li><a class="dropdown-item text-white" href="#"><i class="fst-normal fw-bold">ISPITEC </i>Instituto Politecnico de Inovação Tecnologica</a></li>
                            <li><a class="dropdown-item text-white" href="#">Ver mais...</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white px-3" href="/sakidila_ic/portal/listagem/1">Projectos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white px-3" href="#">Sobre</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white ps-3 me-3" href="#">Contacte-nos</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</nav>

<div class="container-fluid">

    <div class="d-flex flex-column align-items-center my-2">
        <div><h5 class="display-6 ms-5 mt-3 mb-1 fw-bold mb-2" style="color: #AB81A3">Que conta desejas criar?</h5></div>
        <a href="/sakidila_ic/usuario/convidado_registrar" style="text-decoration: none" class="col-6 d-flex ms-5 text-dark shadow-sm py-3 px-5 rounded-4 mb-2 opcao">
            <div class="">
                <img th:src="@{/img/estudante1.png}" class="" height="150">
            </div>
            <div class="">
                <h4 class="text-dark">Conta Estudante</h4>
                <p>Crie sua conta estudante e esteja aberto para divulgar seus projectos academicos.</p>
                <div class="d-flex flex-row justify-content-end">
                    <button class="btn rounded-4">Criar agora!</button>
                </div>
            </div>
        </a>
        <a href="/sakidila_ic/usuario/investigador_independente_registrar" style="text-decoration: none" class="col-6 opcao d-flex ms-5  shadow-sm py-3 px-5 rounded-4 mb-2 text-dark">
            <div class="">
                <img th:src="@{/img/teacher1.png}" class="" height="150">
            </div>
            <div>
                <h4 class="text-dark">Conta Investigador Independente</h4>
                <p>Crie sua conta estudante e esteja aberto para divulgar seus projectos academicos.</p>
                <div class="d-flex flex-row justify-content-end">
                    <button class="btn rounded-4">Criar agora!</button>
                </div>
            </div>
        </a>
        <a href="/sakidila_ic/usuario/empresa_registrar" style="text-decoration: none" class="col-6 opcao d-flex ms-5 text-dark shadow-sm py-3 px-5 rounded-4 mb-2">
            <div class="">
                <img th:src="@{/img/teacher.png}" class="" height="150">
            </div>
            <div>
                <h4 class="text-dark">Conta Empresa</h4>
                <p>Crie sua conta estudante e esteja aberto para divulgar seus projectos academicos.</p>
                <div class="d-flex flex-row justify-content-end">
                    <button class="btn rounded-4" >Criar agora!</button>
                </div>
            </div>
        </a>
    </div>

</div>

</body>
</html>