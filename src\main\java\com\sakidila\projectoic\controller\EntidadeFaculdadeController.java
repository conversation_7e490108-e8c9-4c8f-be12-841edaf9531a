package com.sakidila.projectoic.controller;

import com.sakidila.projectoic.entity.Conta;
import com.sakidila.projectoic.entity.EntidadeFaculdade;
import com.sakidila.projectoic.entity.EntidadeFaculdadeId;
import com.sakidila.projectoic.service.ContaService;
import com.sakidila.projectoic.service.EntidadeFaculdadeService;
import com.sakidila.projectoic.service.EntidadeServico;
import com.sakidila.projectoic.service.FaculdadeService;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Controller
@RequestMapping("sakidila_ic/entidade_faculdade/")
public class EntidadeFaculdadeController {

    @Autowired
    private EntidadeFaculdadeService servico;

    @Autowired
    private EntidadeServico entidadeServico;

    @Autowired
    private FaculdadeService faculdadeService;

    @Autowired
    private ContaService contaService;
    
    @GetMapping("/registrar/{pagina}")
    public String Registrar(Model model, @PathVariable int pagina) {
        int tamanho = 10;
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        System.out.println("dados :"+authentication.getName());
        if (authentication.isAuthenticated() && !authentication.getName().equals("anonymousUser")) {
            Conta conta = contaService.mostrarDadoConta(authentication.getName());
            model.addAttribute("conta", authentication.getName());
            model.addAttribute("userDefault", "anonymousUser");
            model.addAttribute("dadoConta", conta);
            model.addAttribute("gestorInstituicaoPerfil", "ROLE_GESTOR_INSTITUICAO");
            model.addAttribute("gestorCentroPerfil", "ROLE_RESPONSAVEL_CENTRO");
            model.addAttribute("gestorProjectoPerfil", "ROLE_GESTOR_PROJECTO");
            model.addAttribute("investigadorPerfil", "ROLE_INVESTIGATOR");
            model.addAttribute("adminPerfil", "ROLE_ADMIN");
            model.addAttribute("instituto",conta.getEntidade());
            model.addAttribute("faculdades",faculdadeService.listaFaculdades());
            Page<EntidadeFaculdade> paginaNova = servico.encontrarPagina(pagina,tamanho,conta.getEntidade().getId());
            List<EntidadeFaculdade> entidadeFaculdadeLista = paginaNova.getContent();
            model.addAttribute("paginaActual",pagina);
            model.addAttribute("totalPaginas",paginaNova.getTotalPages());
            model.addAttribute("totalItems",paginaNova.getTotalElements());
            model.addAttribute("entidadesFaculdades",entidadeFaculdadeLista);
            return "entidade_faculdade_registrar";
        }else{
            SecurityContextHolder.clearContext();
            model.addAttribute("conta",null);
            model.addAttribute("dadoConta",null);
            return "login";
        }
    }

    @PostMapping("/salvar")
    public String salvarEntidadeFaculdade(@ModelAttribute EntidadeFaculdade entidadeFaculdade,@RequestParam Long idFalcudade,@RequestParam Long idEntidade, HttpSession session){
    entidadeFaculdade.setId(new EntidadeFaculdadeId());
        entidadeFaculdade.getId().setFaculdade(faculdadeService.findById(idFalcudade));
        entidadeFaculdade.getId().setInstituto(entidadeServico.findById(idEntidade));
        servico.criarEntidadeFaculdade(entidadeFaculdade);
        if(entidadeFaculdade != null)
            session.setAttribute("msg", "Usuario " + entidadeFaculdade.getId() + " criado com sucesso!");
        else
            session.setAttribute("msg", "O E-mail " + entidadeFaculdade.getId() + " já existe no sistema");
        return "redirect:/sakidila_ic/entidade_faculdade/registrar/1";
    }

    @GetMapping("/desativar/{idInstituto}/{idFaculdade}")
    public String disabledByIdCentro(@PathVariable Long idInstituto,@PathVariable Long idFaculdade){
        servico.desativar(idFaculdade,idInstituto);
        return "redirect:/sakidila_ic/entidade_faculdade/registrar/1";

    }

}
