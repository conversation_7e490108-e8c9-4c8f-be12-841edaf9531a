<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">

    <!-- Importação do Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
            crossorigin="anonymous"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style th:inline="text">
        body {
            font-family: Poppins;
        }

        .opcao {
            transform: scale(1);
            transition: box-shadow 0.5s, transform 0.5s;
        }

        .sombra {
            box-shadow: 0 0 3px silver;
        }

        .sombra:hover {
            box-shadow: 0 0 5px #AB81A3;
        }

        .opcao:hover {
            transform: scale(1.05);
        }

        .item-off {
            display: none;
            transition: display 0.5s;
        }

        .opcao:hover .item-off {
            display: block;
        }

        .assistente:hover label{
            visibility: visible;
        }

        .assistente label{
            visibility: hidden;
        }

        .chat-receber{
            position: relative;
            float: left;
            clear: left;
        }

        .chat-enviar{
            position: relative;
            float: right;
            clear: right;
        }


    </style>
    <title>Nossos projectos a disposição!</title>
</head>
<body class="" style="background-color: ghostwhite">
<nav class="navbar navbar-expand-lg p-0 " style="margin: 0%;">
    <button class="navbar-toggler ms-2" type="button" data-bs-toggle="collapse" data-bs-target="#navbarTogglerDemo01"
            aria-controls="navbarTogglerDemo01" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarTogglerDemo01">
        <div class=" d-flex flex-column bg-white shadow-sm" style="width: 100%">
            <div class="container-fluid">
                <div class="container d-flex flex-row align-items-center">
                    <a class="navbar-brand" href="/sakidila_ic/portal/">
                        <img th:src="@{/img/logotipo.jpg}" alt="Bootstrap" width="120">
                    </a>
                    <div class="container d-flex flex-row justify-content-end align-items-center">
                        <a th:if="${conta == userDefault}" href="/sakidila_ic/usuario/principal" class="nav-link">Registrar</a>
                        <i style="font-style: normal;font-weight: bold" th:if="${conta != null}">Olá,</i>
                        <div th:if="${dadoConta != null}" class="me-1 ms-1"><i style="color: #AB81A3"> [[${dadoConta.tipoConta.nomenclatura}]]</i>
                            <i style="font-style: normal;font-weight: normal"> [[${dadoConta.username}]]</i>
                        </div>
                        <div class="dropdown btn">
                            <a href="#" class="d-block link-body-emphasis text-decoration-none dropdown dropdown-toggle"
                               data-bs-toggle="dropdown" aria-expanded="false">
                                <img th:if="${conta == userDefault}" th:src="@{/img/user.png}" alt="mdo" width="32"
                                     height="32" class="rounded-circle">
                                <img th:if="${conta != null and dadoConta.foto != null}" th:src="${dadoConta.foto}" alt="mdo" width="32"
                                     height="32" class="rounded-circle">
                                <img th:if="${conta != null and dadoConta.foto == null}" th:src="@{/img/user.png}" alt="mdo" width="32"
                                     height="32" class="rounded-circle">

                            </a>
                            <ul class="dropdown-menu text-small">
                                <li><a th:if="${conta == userDefault}" class="dropdown-item"
                                       href="/sakidila_ic/usuario/login">Entrar</a></li>
                                <li><button th:if="${conta != null}" class="dropdown-item" data-bs-target="#perfil" href="#" data-bs-toggle="modal">Meu Perfil</button></li>
                                <li><a th:if="${conta != null}" class="dropdown-item" href="/logout">Sair</a></li>
                                <!--<li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#">Sair</a></li>-->
                            </ul>
                        </div>
                        <i class="fs-5 bi bi-facebook me-2" style="color: #7c6aa2"></i>
                        <i class="fs-5 bi bi-instagram me-2" style="color: #7c6aa2"></i>
                        <i class="fs-5 bi bi-whatsapp" style="color: #7c6aa2"></i>
                    </div>
                </div>

            </div>
            <div class="container-fluid py-1" style="background-color: #AB81A3">
                <ul class="container navbar-nav d-flex justify-content-end ">
                    <li class="nav-item">
                        <a class="nav-link text-white active px-3" aria-current="page" href="/sakidila_ic/portal/">Portal</a>
                    </li>
                    <li class="nav-item" th:if="${dadoConta != null}">
                        <a class="nav-link text-white active px-3"
                           th:if="${dadoConta.tipoConta.designacao == adminPerfil}"  href="/sakidila_ic/entidade/registrar/1">Gestão Administrativa</a>
                    </li>
                    <li class="nav-item" th:if="${dadoConta != null}">
                        <a class="nav-link text-white active px-3"
                           th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"  href="/sakidila_ic/centro/registrar/1">Gestão Administrativa</a>
                    </li>
                    <li class="nav-item" th:if="${dadoConta != null}">
                        <a class="nav-link text-white active px-3"
                           th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil}"  href="/sakidila_ic/centro/investigador/associar/1">Gestão Administrativa</a>
                    </li>
                    <li class="nav-item" th:if="${dadoConta != null}">
                        <a class="nav-link text-white active px-3"
                           th:if="${dadoConta.tipoConta.designacao == estudantePerfil
                           or dadoConta.tipoConta.designacao == investigadorPerfil
                             or dadoConta.tipoConta.designacao == investigadorIndependentePerfil}"  href="/sakidila_ic/portal/projecto/registrar/1">Gestão Administrativa</a>
                    </li>
                    <li class="nav-item" th:if="${dadoConta != null}">
                        <a class="nav-link text-white active px-3"
                           th:if="${dadoConta.tipoConta.designacao == gestorProjectoPerfil}"  href="/sakidila_ic/portal/modulo_gestao">Gestão Administrativa</a>
                    </li>
                    <li class="nav-item" th:if="${dadoConta != null}">
                        <a class="nav-link text-white active px-3"
                           th:if="${dadoConta.tipoConta.designacao == empresaPerfil}"  href="/sakidila_ic/portal/projecto/registrar/1">Meus investimentos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white px-3" href="/sakidila_ic/portal/listagem/1">Projectos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white px-3" href="#">Sobre</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white ps-3 me-3" href="#">Contacte-nos</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</nav>

<div class="container-fluid">
    <form action="/sakidila_ic/portal/listagem/1" method="get">
    <div class="row border-3 rounded-bottom-4 px-3 mx-2 mb-3 sticky-top shadow-sm" style="background-color:white;z-index: 4">
        <div class="col">
            <div class="my-2 form-floating">
                <select class="form-select rounded-0 border-top-0 border-start-0 border-end-0" id="ordem" name="ordem"
                        style="border-color: #AB81A3;background-color: transparent">
                    <option value="1">Recentes</option>
                    <option value="0">A-Z</option>
                    <option value="2">Z-A</option>
                </select>
                <label>Ordenação</label>
            </div>
            <br>
        </div>
        <div class="col">
            <div class="my-2 form-floating">
                <select class="form-select rounded-0 border-top-0 border-start-0 border-end-0" id="instituto" name="instituto"
                        style="border-color: #AB81A3;background-color: transparent">
                    <option value="0">Todos</option>
                    <option th:each="item : ${institutos}" th:text="${item.designacao}" th:value="${item.id}"></option>
                </select>
                <label>Instituições</label>
            </div>
        </div>
        <div class="col">
            <div class="my-2 form-floating">
                <select class="form-select rounded-0 border-top-0 border-start-0 border-end-0" id="investimento" name="investimento"
                        style="border-color: #AB81A3;background-color: transparent">
                    <option value="0">Todos</option>
                    <option value="1">Projecto de investimento</option>
                    <option value="2">Projectos para promoção</option>
                </select>
                <label>Serviços</label>
            </div>
        </div>
        <div class="col">
            <div class="my-2 form-floating">
                <input type="text" name="texto" class="form-control rounded-0 border-top-0 border-start-0 border-end-0" placeholder="Especifique um projecto" style="border-color: #AB81A3;background-color: transparent">
                <label>Pesquisar</label>
            </div>
        </div>
        <div class="col-1">
            <div class="my-4">
                <button type="submit" class="btn rounded-4 text-white shadow-sm btn" style="background-color: #AB81A3">
                    Procurar
                </button>
            </div>
        </div>
    </div>



    </form>
    <div class="container-fluid mt-2 mb-5">
        <div id="projectos" class="row row-cols-sm-1 row-cols-md-4  row-cols-lg-5 row-cols-lx-5 g-3">
            <div class="col my-2 opcao" th:each="item : ${projectos}" th:if="${((item.centro != null and !item.centro.disabled or item.centro == null) and item.EhAprovado and !item.disabled)}">
                <form>
                    <div class="card border-0 rounded-4 sombra">
                        <div class="m-3 col">

                            <div style="position: absolute;background-color: rgba(255,255,255,0.9);width: 95%" class="item-off rounded-3">

                                <h5 class="card-title fs-6 mb-1 mx-3 mt-3 item-off" style="color:#AB81A3">Resumo do
                                    projecto</h5>
                                <div class="item-off" style="height: 150px;overflow: hidden ;">
                                    <p class="card-text fs-6 mx-3" style="text-align: justify"
                                       th:text="${item.descricao}"></p>

                                </div>
                                <div class="d-flex justify-content-center mt-1 mb-4 ">
                                    <input type="hidden" name="projectoIdAux" th:value="${item.id}">
                                    <input type="hidden" name="investidorIdAux" th:value="${dadoConta.id}">
                                    <input type="hidden" name="projectoDesignacaoAux" th:value="${item.designacao}">
                                    <input type="hidden" name="projectoImagemAux" th:value="${item.logotipo}">
                                    <input type="hidden" name="projectoDescricaoAux" th:value="${item.descricao}">
                                    <input type="hidden" name="projectoGestorAux" th:value="${item.gestor.pessoa.nome+' '+item.gestor.pessoa.apelido}">
                                    <input type="hidden" name="instituto" th:if="${item.gestor.entidade != null}"
                                           th:value="${item.gestor.entidade.designacao}">
                                    <input type="hidden" name="faculdade" th:if="${item.centro != null}"
                                           th:value="${item.centro.faculdade.designacao}">
                                    <input type="hidden" name="centro" th:if="${item.centro != null}"
                                           th:value="${item.centro.designacao}">
                                    <input type="hidden" name="dataCriacao" th:value="${item.dataCriacao}">
                                    <input type="hidden" name="preco" th:value="${item.orcamentoTexto}">
                                    <input type="hidden" name="documento" th:if="${item.documento != null}"
                                           th:value="${item.documento.idDocumento}">
                                    <input type="hidden" name="docNome" th:if="${item.documento != null}"
                                           th:value="${item.documento.designacao}">
                                    <button th:if="${dadoConta.ehSubscrito}" class="btn shadow-sm text-white rounded-4 mx-2 item-off addCarrinho"
                                            style="background-color: #AB81A3" type="button" data-bs-toggle="modal"
                                            data-bs-target="#staticBackdrop">Ver detalhes
                                    </button>
                                    <button th:if="${!dadoConta.ehSubscrito}" class="btn shadow-sm text-white rounded-4 mx-2 item-off addCarrinho"
                                            style="background-color: #AB81A3" type="button" data-bs-toggle="modal"
                                            data-bs-target="#pagamentoModal">Ver detalhes
                                    </button>
                                </div>
                            </div>
                            <img th:src="${item.logotipo}" th:if="${item.logotipo != null}" class="card-img-top rounded-3" width="300" height="250">
                            <img th:src="@{'/img/empty.jpg'}" th:if="${item.logotipo == null}" class="card-img-top rounded-3" width="300" height="250">
                        </div>
                        <div class="col">
                            <div class="card-body">

                                <h5 class="card-title text-center" style="font-size: 19px"
                                    th:text="${item.designacao}"></h5>

                                <div class="d-flex flex-row justify-content-between align-items-center">
                                    <div>
                                        <h5 class="card-title fs-6 mb-1 mt-3" style="color:#AB81A3">Orçamento</h5>
                                        <p class="card-text fs-6" style="text-align: justify"
                                           th:text="${item.orcamentoTexto}"></p>
                                    </div>

                                        <input type="hidden" name="projectoIdAuxIn" th:value="${item.id}">
                                        <input type="hidden" name="investidorIdAuxIn" th:value="${dadoConta.id}">
                                        <input type="hidden" name="projectoDesignacaoAuxIn" th:value="${item.designacao}">
                                        <input type="hidden" name="precoIn" th:value="${item.orcamentoTexto}">
                                        <button class="btn btn-outline-primary shadow-sm rounded-4 mt-4 addCarrinho"
                                                th:if="${dadoConta.tipoConta.designacao == empresaPerfil and !item.ehInvestido and item.ehDeInvestimento}" type="button"
                                                data-bs-toggle="modal" data-bs-target="#investir">Investir
                                        </button>

                                    <!-- <i class="bi bi-heart fs-4 ms-2" style="color: #AB81A3"></i>
                                     <small class="text-muted">10K</small>-->
                                </div>
                                <p class="card-text mt-3" style="text-align: center"><small class="text-muted">[[${item.dataCriacao}]]</small>
                                </p>

                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="d-flex justify-content-center">
            <nav class="Page navigation example" th:if="${totalPaginas > 1}">
                <ul class="pagination">
                    <li class="page-item"><a class="page-link">Total de projectos: [[${totalItems}]]</a></li>
                    <div th:each="i : ${#numbers.sequence(1,totalPaginas)}">
                        <li class="page-item">
                            <a class="page-link" th:if="${paginaActual != i}"
                               th:href="@{'/sakidila_ic/portal/listagem/'+ ${i}}">[[${i}]]</a>
                            <a class="page-link" th:unless="${paginaActual != i}">[[${i}]]</a> &nbsp; &nbsp;
                        </li>
                    </div>
                    <li class="page-item">
                        <a class="page-link" th:if="${paginaActual < totalPaginas}"
                           th:href="@{'/sakidila_ic/portal/listagem/'+${paginaActual + 1}}">Próximo</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" th:if="${paginaActual < totalPaginas}"
                           th:href="@{'/sakidila_ic/portal/listagem/'+${paginaActual + 1}}">Ultimo</a>
                        <a class="page-link" th:unless="${paginaActual < totalPaginas}">Ultimo</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>


<!--Modal de click no projecto-->
<div class="modal fade border-0" id="investir" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
     aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered border-0">
        <div class="modal-content">
            <form method="post" th:action="@{/sakidila_ic/investidor/salvar}">
                <div class="modal-header " style="background-color: #AB81A3">
                    <h1 class="modal-title fs-5 text-white" id="staticBackdropLabel"></h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="background-color: ghostwhite">


                    <div class="col">
                        <h6 class="mb-0" style="color: #7c6aa2">Projecto</h6>
                        <p id="nome"></p>
                        <input type="hidden" name="id.projecto.id" id="investidor">
                        <input type="hidden" name="id.investidor.id" id="projecto">
                    </div>

                    <div class="col">
                        <h6 class="mb-0" style="color: #7c6aa2">Confirmar a solicitação de investimento?</h6>
                    </div>

                </div>
                <div class="modal-footer">

                    <button type="button" class="btn btn-secondary shadow-sm border-0 rounded-4"
                            data-bs-dismiss="modal">Fechar
                    </button>
                    <button type="submit" class="btn btn-primary shadow-sm border-0 rounded-4"
                            style="background-color: #7c6aa2" data-bs-dismiss="modal">Confirmar
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal -->
<form action="/sakidila_ic/usuario/subscrever" method="post" id="myForm" onsubmit="return validateForm()" required>
    <div class="modal fade" id="pagamentoModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <input type="hidden" name="idConta" th:value="${dadoConta.id}">
        <input type="hidden" name="pagina" th:value="${'sakidila_ic/portal/listagem'}">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="exampleModalLabel">Plano de subscrição</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <h6>Torna-te uma empresa Premium na MosalaLinkUp!</h6>
                    <div class="mb-3 mt-3">
                        <!--<div class="mb-2">
                            <input type="checkbox" class="btn-check" name="packLite" id="btn-check-lite" autocomplete="off">
                            <label class="btn btn-secondary" for="btn-check-1">Pacote Lite</label>
                        </div>-->

                        <div class="mb-2">
                            <input type="checkbox" class="btn-check" name="packPremium" id="btn-check-premium" autocomplete="off">
                            <label class="btn btn-primary" for="btn-check-2">Pacote Premium</label>
                        </div>
                        <small>Para poder procurar e dar soluções de seus problemas com os investigadores disponiveis subscreva-se na nossa plataforma.</small>
                        <p class="mt-2"> Por apenas <i class="fw-bold fs-5"> 2.000,00 <i class="text-muted fw-normal">AOA/Mensal</i></i></p>
                    </div>
                    <h6 class="mb-3">Selecione o metodo de pagamento</h6>
                    <div class="d-flex flex-row">
                        <div class="mb-4 me-2">
                            <input type="checkbox" class="btn-check" name="checkpagamento" id="btn-check1" autocomplete="off">
                            <label class="btn btn-outline-secondary" for="btn-check1">Visa Card</label>
                        </div>
                        <div class="mb-4 me-2">
                            <input type="checkbox" class="btn-check" name="checkpagamento" id="btn-check2" autocomplete="off">
                            <label class="btn btn-outline-secondary" for="btn-check2">Paypal</label>
                        </div>
                        <div class="mb-4 me-2">
                            <input type="checkbox" class="btn-check" name="checkpagamento" id="btn-check3" autocomplete="off">
                            <label class="btn btn-outline-secondary" for="btn-check3">Multicaixa Express</label>
                            <div class="valid-feedback">

                            </div>
                            <div class="invalid-feedback">
                                Preencher campo com o número do cartão.
                            </div>
                        </div>
                    </div>
                    <h6 class="mb-2">Dados do cartão</h6>
                    <div class="row">
                        <div class="col">
                            <div class="mb-3">
                                <label for="numCartao" class="form-label">Nº do Cartão</label>
                                <input type="number" class="form-control" id="numCartao" name="numCartao" onfocusout="validNumcartao()" placeholder="1000 2345 6000 7890">
                                <div class="valid-feedback">

                                </div>
                                <div class="invalid-feedback">
                                    Preencher campo com o número do cartão.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <div class="mb-3">
                                <label for="dataCartaoExp" class="form-label">Data de Expiração</label>
                                <input type="date" class="form-control" id="dataCartaoExp" name="dataCartaoExp" onfocusout="validDataCartaoExp()" placeholder="<EMAIL>">
                                <div class="valid-feedback">

                                </div>
                                <div class="invalid-feedback">
                                    Preencher campo com a data de expiração do cartão.
                                </div>
                            </div>
                        </div>

                        <div class="col">
                            <div class="mb-3">
                                <label for="cvvValidate" class="form-label">CVC</label>
                                <input type="number" class="form-control" name="cvvValidate" id="cvvValidate" onfocusout="validCVV()" placeholder="890">
                                <div class="valid-feedback">

                                </div>
                                <div class="invalid-feedback">
                                    Preencher campo com o CVV do cartão.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Voltar</button>
                    <button type="submit" name="subscrever" class="btn" style="background-color: #AB81A3; color: white">Subscrever agora!</button>
                </div>
            </div>
        </div>
    </div>
</form>


<!--Modal de click no projecto-->
<div class="modal fade border-0" id="staticBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
     aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable border-0">
        <div class="modal-content">
            <div class="modal-header " style="background-color: #AB81A3">
                <h1 class="modal-title fs-5 text-white" id="staticBackdropLabel"></h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="background-color: ghostwhite">
                <div class="row d-flex flex-column align-items-center">
                    <div class="container-fluid accordion accordion-flush" id="accordionFlushExample">
                        <div class="container bg-white shadow-sm rounded-4 d-flex justify-content-center">
                            <img id="imagemAux" src="/img/imagem.jpg" class="m-3" width="350" height="350">
                        </div>

                        <div class="rounded-4 bg-white shadow-sm p-3 text-center mt-2">
                            <h6 class="mb-0" style="color: #7c6aa2">Projecto</h6>
                            <p id="designacaoAux"></p>
                            <h6 class="mb-0" style="color: #7c6aa2">Orçamento</h6>
                            <p id="precoAux"></p>
                            <h6 class="mb-0" style="color: #7c6aa2">Instituição Acadêmica</h6>
                            <p id="instituicaoAux"></p>
                            <h6 class="mb-0" style="color: #7c6aa2">Faculdade</h6>
                            <p id="faculdadeAux"></p>
                            <h6 class="mb-0" style="color: #7c6aa2">Centro de Investigação</h6>
                            <p id="centroAux"></p>
                            <h6 class="mb-0" style="color: #7c6aa2">Gestor do projecto</h6>
                            <p id="gestorAux"></p>
                            <small class="mb-0" style="color: #AB81A3">Data de Lançamento</small>
                            <small class="mb-0 text-muted" id="dataCriacaoAux"></small>
                        </div>

                        <!--<div class="accordion-item border-0 my-1 rounded-4">
                            <h2 class="accordion-header rounded-4">
                                <button class="accordion-button collapsed rounded-4 shadow-sm bg-white" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapse1" aria-expanded="false" aria-controls="flush-collapse1">
                                    <h6 class="mb-0">Imagens</h6>
                                </button>
                            </h2>
                            <div id="flush-collapse1" class="accordion-collapse collapse shadow-sm rounded-4 mt-1 bg-white" style="background-color: #AB81A3;" data-bs-parent="#accordionFlushExample">
                                <div class="accordion-body">
                                    <div class="row row-cols-4 d-flex align-items-between">
                                        <div class="col-3 my-3">
                                            <img src="/img/imagem.jpg" class="shadow-sm bg-white rounded-4" width="80" height="70">
                                        </div>
                                        <div class="col-3 my-3">
                                            <img src="/img/imagem.jpg" class="shadow-sm bg-white rounded-4" width="80" height="70">
                                        </div>
                                        <div class="col-3 my-3">
                                            <img src="/img/imagem.jpg" class="shadow-sm bg-white rounded-4" width="80" height="70">
                                        </div>
                                        <div class="col-3 my-3">
                                            <img src="/img/imagem.jpg" class="shadow-sm bg-white rounded-4" width="80" height="70">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>-->
                        <div class="accordion-item border-0 my-1 rounded-4">
                            <h2 class="accordion-header rounded-4">
                                <button class="accordion-button collapsed rounded-4 shadow-sm bg-white" type="button"
                                        data-bs-toggle="collapse" data-bs-target="#flush-collapseOne"
                                        aria-expanded="false" aria-controls="flush-collapseOne">
                                    <h6 class="mb-0">Resumo</h6>
                                </button>
                            </h2>
                            <div id="flush-collapseOne"
                                 class="accordion-collapse collapse shadow-sm rounded-4 mt-1 bg-white"
                                 style="background-color: #AB81A3;" data-bs-parent="#accordionFlushExample">
                                <div class="accordion-body" id="descricaoAux"></div>
                            </div>
                        </div>
                        <div class="accordion-item border-0 my-1 rounded-4">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed rounded-4 shadow-sm bg-white"
                                        style="background-color: #AB81A3;" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#flush-collapseTwo" aria-expanded="false"
                                        aria-controls="flush-collapseTwo">
                                    <h6 class="mb-0">Colaboradores</h6>
                                </button>
                            </h2>
                            <div id="flush-collapseTwo"
                                 class="accordion-collapse collapse shadow-sm rounded-4 mt-1 bg-white"
                                 style="background-color: #AB81A3;" data-bs-parent="#accordionFlushExample">
                                <div class="accordion-body" id="participantes"></div>
                            </div>
                        </div>
                        <div class="accordion-item border-0 my-1 rounded-4">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed rounded-4 shadow-sm bg-white" type="button"
                                        data-bs-toggle="collapse" data-bs-target="#flush-collapseThree"
                                        aria-expanded="false" aria-controls="flush-collapseThree">
                                    <h6 class="mb-0 ">Informações Adicionais</h6>
                                </button>
                            </h2>
                            <div id="flush-collapseThree"
                                 class="accordion-collapse collapse shadow-sm rounded-4 mt-1 bg-white"
                                 data-bs-parent="#accordionFlushExample">
                                <div class="accordion-body">
                                    <a id="link" class="link-item" href="#">Sem relatorio do projecto</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary shadow-sm border-0 rounded-4"
                        style="background-color: #7c6aa2" data-bs-dismiss="modal">Fechar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Perfil-->
<div class="modal fade" id="perfil" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="" id="staticBackdropLabel"><h5 class="modal-title fs-4 fw-bold" style="color: rgba(158,158,151)">Meu <i class=" fw-bold text-dark">Perfil</i></h5></h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row row-cols-4 d-flex justify-content-center">
                    <div class="col d-flex flex-column">
                <figure class="text-center" th:if="${dadoConta.tipoConta != null and dadoConta.tipoConta.designacao != investigadorIndependentePerfil}">
                    <img th:if="${dadoConta.tipoConta.designacao != empresaPerfil}" th:src="${dadoConta.entidade.logotipo}" class="rounded mx-auto d-block">
                    <blockquote class="blockquote my-3">
                        <p class="ver" th:if="${dadoConta.tipoConta != null and dadoConta.tipoConta.designacao != investigadorIndependentePerfil or dadoConta.tipoConta.designacao != empresaPerfil}">[[${dadoConta.entidade.designacao}]] <i class="fw-bolder">[[${dadoConta.entidade.sigla}]]</i></p>
                    </blockquote>

                    <figcaption class="blockquote-footer" th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}">
                        total de centros de investigações <cite title="Source Title"><i class="fw-bolder">[[${centros.size}]]</i></cite>
                    </figcaption>
                </figure>
                        <form method="post" name="formEmpresa" th:action="@{/sakidila_ic/usuario/empresa/registrar/salvar}" enctype="multipart/form-data" onsubmit="return validateFormEmp()" required>
                            <img th:if="${dadoConta.tipoConta.designacao == empresaPerfil}" th:src="${dadoConta.foto}" class="rounded mx-auto d-block" width="150">
                            <div th:if="${dadoConta.tipoConta.designacao == empresaPerfil}" class="p-3 editar  d-none">
                                <label class="form-label fw-bolder">Adicione o logotipo da Empresa</label>
                                <input class="form-control" type="file" id="imagem" name="imagem">
                            </div>
                            <input type="hidden" name="id" th:value="${dadoConta.id}">
                            <input type="hidden" name="entidade.id" th:if="${dadoConta.entidade != null}" th:value="${dadoConta.entidade.id}">
                            <input type="hidden" id="password" th:value="${dadoConta.senha}">
                        <dl class="row" th:if="${dadoConta.tipoConta.designacao == empresaPerfil}">

                            <dt class="col-sm-5 ver">Denominação</dt>
                            <dd class=" mb-3">
                                <p class="p-0 m-0 ver">[[${dadoConta.entidade.designacao}]]</p>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Denominação</label>
                                    <input class="form-control" type="text" id="designacaoEmp" onfocusout="validDenominacao()" name="entidade.designacao" th:value="${dadoConta.entidade.designacao}">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher o campo com o nome da empresa.
                                    </div>
                                </div>
                            </dd>

                            <dt class="col-sm-5 ver">Sigla</dt>
                            <dd class=" mb-3">
                                <p class="p-0 m-0 ver">[[${dadoConta.entidade.sigla}]]</p>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Sigla</label>
                                    <input class="form-control" type="text" id="siglaEmp" onfocusout="validSigla()" name="entidade.sigla" th:value="${dadoConta.entidade.sigla}">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher o campo com a sigla da empresa.
                                    </div>
                                </div>
                            </dd>
                            <dt class="col-sm-5 ver">NIF</dt>
                            <dd class=" mb-3">
                                <p class="p-0 m-0 ver">[[${dadoConta.entidade.nif}]]</p>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">NIF</label>
                                    <input class="form-control" type="text" id="nifEmp" onfocusout="validNif()" name="entidade.nif" th:value="${dadoConta.entidade.nif}">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher o campo com o nif da empresa.
                                    </div>
                                </div>
                            </dd>
                            <dt class="col-sm-5 ver">Residencia</dt>
                            <dd class=" mb-3">
                                <p class="p-0 m-0 ver">[[${dadoConta.entidade.localidade.localidadePai.localidadePai.designacao}]], [[${dadoConta.entidade.localidade.localidadePai.designacao}]], [[${dadoConta.entidade.localidade.designacao}]]</p>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Provincia</label>
                                    <select class="form-select" id="provincia" name="provincia">
                                        <option th:each="localidade : ${localidades}" th:value="${localidade.id}"
                                                th:text="${localidade.designacao}" th:selected="${localidade.id == dadoConta.entidade.localidade.localidadePai.localidadePai.id}"></option>
                                    </select>
                                </div>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Municipio</label>
                                    <select class="form-select" id="municipio" name="municipio"></select>
                                </div>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Bairro</label>
                                    <select class="form-select" id="bairro" name="entidade.localidade.id"></select>
                                </div>
                                <p class="p-0 m-0 ver">Endereço: [[${dadoConta.entidade.endereco}]]</p>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Endereço</label>
                                    <input class="form-control" type="text" id="enderecoEmp" onfocusout="validEnderecoEmp()" name="entidade.endereco" th:value="${dadoConta.entidade.endereco}">
                                </div>
                            </dd>
                            <dt class="col-sm-5 ver">Contactos</dt>
                            <dd class=" mb-3">
                                <p class="p-0 m-0 ver">[[${dadoConta.contacto}]]</p>
                                <p class="p-0 m-0 ver">[[${dadoConta.email}]]</p>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Telefone</label>
                                    <input class="form-control" type="text" id="telEmp" onfocusout="validTelEmp()" name="contacto" th:value="${dadoConta.contacto}">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher o campo com o contacto telefônico da empresa.
                                    </div>
                                </div>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Email</label>
                                    <input class="form-control" type="text" id="emailEmp" onfocusout="validEmailEmp()" name="email" th:value="${dadoConta.email}">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher o campo com o email da empresa.
                                    </div>
                                </div>
                            </dd>
                            <dt class="col-sm-5 ver">Conta Empresa</dt>
                            <dd class=" mb-3">
                                <p class="p-0 m-0 ver">Username: [[${dadoConta.username}]]</p>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Username</label>
                                    <input class="form-control" type="text" id="usernameEmp" onfocusout="validUsernameEmp()" name="username" th:value="${dadoConta.username}">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher o campo com o novo username da empresa.
                                    </div>
                                </div>
                                <div class="form-check form-switch ms-3 editar d-none">
                                    <input class="form-check-input" type="checkbox" role="switch" id="alterarSenha" value="0">
                                    <label class="form-check-label fw-bolder" for="alterarSenha">Modificar a senha?</label>
                                </div>
                                <div class="p-3 editarSenha  d-none">
                                    <label class="form-label fw-bolder">Senha antiga</label>
                                    <input class="form-control" type="password" onfocusout="validSenhaEmp()" name="senha-antiga" id="senha-antiga">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher o campo com a senha antiga.
                                    </div>
                                </div>
                                <div class="p-3 editarSenha  d-none">
                                    <label class="form-label fw-bolder">Senha nova</label>
                                    <input class="form-control" type="password" id="senha-nova" onfocusout="validSenhaNovaEmp()" name="senha" th:value="${dadoConta.senha}">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher o campo com a nova senha.
                                    </div>
                                </div>
                                <div class="p-3 editarSenha  d-none">
                                    <label class="form-label fw-bolder">Confirmar senha nova</label>
                                    <input class="form-control" type="password" onfocusout="validSenhaConfirmarEmp()"  name="senha-confirmar" id="senha-confirmar">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        confirme com a sua senha nova.
                                    </div>
                                </div>
                            </dd>
                            <dt class="col-sm-5 ver">Plano de subcrição</dt>
                            <dd class="col-sm-7 mb-3 ver">
                                <p th:if="${dadoConta.ehSubscrito}" class="text-success">Activo</p>
                                <p th:if="${!dadoConta.ehSubscrito}" class="text-danger">Sem pacote</p>
                                <input type="hidden" name="ehSubscrito" th:value="${dadoConta.ehSubscrito}">
                            </dd>
                        </dl>
                            <button type="submit" class="btn btn-success editar d-none w-100" th:if="${dadoConta.tipoConta.designacao == empresaPerfil}">Guardar</button>
                        </form>
                        <form method="post" name="formUser" th:action="@{/sakidila_ic/usuario/update}" enctype="multipart/form-data" onsubmit="return validateFormP()" required>
                        <dl class="row" th:if="${dadoConta.tipoConta.designacao != empresaPerfil}">
                            <input type="hidden" name="id" th:value="${dadoConta.id}">
                            <input type="hidden" name="pessoa.id" th:value="${dadoConta.pessoa.id}">
                            <input type="hidden" id="password" th:value="${dadoConta.senha}">
                            <input type="hidden" name="tipoConta.id" th:value="${dadoConta.tipoConta.id}">
                            <input type="hidden" name="centro.id" th:if="${dadoConta.centro != null}" th:value="${dadoConta.centro.id}">
                            <div th:if="${dadoConta.tipoConta.designacao != investigadorIndependentePerfil}">
                                <input type="hidden" name="entidade.id" th:value="${dadoConta.entidade.id}">
                            </div>

                            <div th:if="${dadoConta.tipoConta.designacao != gestorInstituicaoPerfil}">
                                <input type="hidden" name="nivelAcademico.id" th:value="${dadoConta.nivelAcademico.id}">
                            </div>

                            <div th:if="${dadoConta.faculdade != null}">
                                <input type="hidden" name="faculdade.id" th:value="${dadoConta.faculdade.id}">
                            </div>

                            <dt class="col-sm-5 ver">Nome completo</dt>
                            <dd class=" mb-3">
                                <div class="ver">[[${dadoConta.pessoa.nome+' '+dadoConta.pessoa.apelido}]]</div>
                                <img th:src="${dadoConta.foto}" class="img-thumbnail ver" width="150">
                                <div th:if="${dadoConta.tipoConta.designacao != empresaPerfil}" class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Actualize a sua foto de perfil</label>
                                    <input class="form-control" type="file" id="imagemP" name="imagem">
                                </div>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Nome</label>
                                    <input class="form-control" type="text" id="nomeP" onfocusout="validNomeP()" name="pessoa.nome" th:value="${dadoConta.pessoa.nome}">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher o campo com o novo nome.
                                    </div>
                                </div>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Apelido</label>
                                    <input class="form-control" type="text" id="apelidoP" onfocusout="validApelidoP()" name="pessoa.apelido" th:value="${dadoConta.pessoa.apelido}">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher o campo com o novo apelido.
                                    </div>
                                </div>
                            </dd>

                            <dt class="col-sm-5 ver">Nº Identidade</dt>
                            <dd class=" mb-3">
                                <p class="p-0 m-0 ver">[[${dadoConta.pessoa.bi}]]</p>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Nº Identidade</label>
                                    <input class="form-control" type="text" id="biP" onfocusout="validBiP()" name="pessoa.bi" th:value="${dadoConta.pessoa.bi}">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher o campo com o novo número do BI.
                                    </div>
                                </div>
                                <p class="p-0 m-0 ver">[[${dadoConta.pessoa.dataNascimento}]]</p>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Data de nascimento</label>
                                    <input class="form-control" type="date" id="dataNascimentoP" onfocusout="validDataNascimentoP()" name="pessoa.dataNascimento" th:value="${dadoConta.pessoa.dataNascimento}">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher o campo com o novo número do BI.
                                    </div>
                                </div>
                                <p class="p-0 m-0 ver">Emitido aos [[${dadoConta.pessoa.dataEmissao}]]</p>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Data de emissão</label>
                                    <input class="form-control" type="date" id="dataEmissaoP" onfocusout="validDataEmissaoP()" name="pessoa.dataEmissao" th:value="${dadoConta.pessoa.dataEmissao}">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher o campo com a nova data de emissão do BI.
                                    </div>
                                </div>
                                <p class="p-0 m-0 ver">Válido até [[${dadoConta.pessoa.dataValidade}]]</p>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Data de validade</label>
                                    <input class="form-control" type="date" id="dataValidadeP" onfocusout="validDataValidadeP()" name="pessoa.dataValidade" th:value="${dadoConta.pessoa.dataValidade}">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher o campo com a nova data de validade do BI.
                                    </div>
                                </div>
                            </dd>

                            <dt class="col-sm-5 ver">Estado civil</dt>
                            <dd class=" mb-3">
                                <div class="ver">[[${dadoConta.pessoa.estadoCivil.designacao}]]</div>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Estado civil</label>
                                    <select class="form-select" id="estadoCivilP" onfocusout="validEstadoCivilP()" name="pessoa.estadoCivil.id">
                                        <option th:each="item : ${estados}" th:text="${item.designacao}" th:value="${item.id}" th:selected="${item.id == dadoConta.pessoa.estadoCivil.id}"></option>
                                    </select>
                                </div>
                            </dd>

                            <dt class="col-sm-5 text-truncate ver">Genêro</dt>
                            <dd class=" mb-3">
                                <div class="ver">[[${dadoConta.pessoa.sexo.designacao}]]</div>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Genêro</label>
                                    <select class="form-select" id="sexoP" onfocusout="validSexoP()" name="pessoa.sexo.id">
                                        <option th:each="item : ${sexos}" th:text="${item.designacao}" th:value="${item.id}" th:selected="${item.id == dadoConta.pessoa.sexo.id}"></option>
                                    </select>
                                </div>
                            </dd>

                            <dt class="col-sm-5 ver">Residencia</dt>
                            <dd class=" mb-3">
                                <p class="p-0 m-0 ver">[[${dadoConta.pessoa.localidade.localidadePai.localidadePai.designacao}]], [[${dadoConta.pessoa.localidade.localidadePai.designacao}]], [[${dadoConta.pessoa.localidade.designacao}]]</p>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Provincia</label>
                                    <select class="form-select" id="provincia" name="provincia">
                                        <option th:each="localidade : ${localidades}" th:value="${localidade.id}"
                                                th:text="${localidade.designacao}" th:selected="${localidade.id == dadoConta.pessoa.localidade.localidadePai.localidadePai.id}"></option>
                                    </select>
                                </div>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Municipio</label>
                                    <select class="form-select" id="municipio" name="municipio"></select>
                                </div>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Bairro</label>
                                    <select class="form-select" id="bairro" name="pessoa.localidade.id"></select>
                                </div>
                                <p class="p-0 m-0 ver">Endereço: [[${dadoConta.pessoa.endereco}]]</p>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Endereço</label>
                                    <input class="form-control" type="text" id="enderecoP" onfocusout="validEnderecoP()" name="pessoa.endereco" th:value="${dadoConta.pessoa.endereco}">
                                </div>
                            </dd>
                            <dt class="col-sm-5 ver">Contactos</dt>
                            <dd class=" mb-3">
                                <p class="p-0 m-0 ver">[[${dadoConta.email}]]</p>
                                <p class="p-0 m-0 ver">[[${dadoConta.contacto}]]</p>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Email</label>
                                    <input class="form-control" type="text" id="emailP" onfocusout="validEmailP()" name="email" th:value="${dadoConta.email}">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher o campo com o email.
                                    </div>
                                </div>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Contacto</label>
                                    <input class="form-control" type="text" id="contactoP" onfocusout="validContactoP()" name="contacto" th:value="${dadoConta.contacto}">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher o campo com o contacto telefônico.
                                    </div>
                                </div>
                            </dd>
                            <dt class="col-sm-5 ver"
                                th:if="${dadoConta.faculdade != null}">Faculdade</dt>
                            <dd class=" mb-3"
                                th:if="${dadoConta.faculdade != null}">
                                <p class="p-0 m-0" th:if="${dadoConta.faculdade != null}">[[${dadoConta.faculdade.designacao}]]</p>
                                <div class="p-3 editar  d-none" th:if="${dadoConta.faculdade != null}">
                                    <label class="form-label fw-bolder">Faculdade</label>
                                    <select class="form-select" id="faculdadeP" name="faculdade.id">
                                        <option th:each="item : ${faculdades}" th:text="${item.designacao}" th:value="${item.id}" th:selected="${item.id == dadoConta.faculdade.id}"></option>
                                    </select>
                                </div>
                            </dd>
                            <dt class="col-sm-5 ver"
                                th:if="${dadoConta.nivelAcademico != null}">Nível Acadêmico</dt>
                            <dd class=" mb-3"
                                th:if="${dadoConta.nivelAcademico != null}">
                                <p class="p-0 m-0 ver">[[${dadoConta.nivelAcademico.designacao}]]</p>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Nível acadêmico</label>
                                    <select class="form-select" id="nivelAcademicoP" name="nivelAcademico.id">
                                        <option th:each="item : ${niveis}" th:text="${item.designacao}" th:value="${item.id}" th:selected="${item.id == dadoConta.nivelAcademico.id}"></option>
                                    </select>
                                </div>
                            </dd>
                            <dt class="col-sm-5 ver">Conta</dt>
                            <dd class="mb-3">
                                <p class="p-0 m-0 ver">Username: [[${dadoConta.username}]]</p>
                                <div class="p-3 editar  d-none">
                                    <label class="form-label fw-bolder">Username</label>
                                    <input class="form-control" type="text" id="usernameP" onfocusout="validUsernameP()" name="username" th:value="${dadoConta.username}">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher o campo com o novo nome de usuario.
                                    </div>
                                </div>
                                <div class="form-check form-switch ms-3 editar d-none">
                                    <input class="form-check-input" type="checkbox" role="switch" id="alterarSenha" value="0">
                                    <label class="form-check-label fw-bolder" for="alterarSenha">Modificar a senha?</label>
                                </div>
                                <div class="p-3 editarSenha  d-none">
                                    <label class="form-label fw-bolder">Senha antiga</label>
                                    <input class="form-control" type="password" onfocusout="validSenhaP()" name="senha-antiga" id="senha-antigaP">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher o campo com a senha antiga.
                                    </div>
                                </div>
                                <div class="p-3 editarSenha  d-none">
                                    <label class="form-label fw-bolder">Senha nova</label>
                                    <input class="form-control" type="password" id="senha-novaP" onfocusout="validSenhaNovaP()" name="senha" th:value="${dadoConta.senha}">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        Preencher o campo com a nova senha.
                                    </div>
                                </div>
                                <div class="p-3 editarSenha  d-none">
                                    <label class="form-label fw-bolder">Confirmar senha nova</label>
                                    <input class="form-control" type="password" onfocusout="validSenhaConfirmarP()"  name="senha-confirmar" id="senha-confirmarP">
                                    <div class="valid-feedback">

                                    </div>
                                    <div class="invalid-feedback">
                                        confirme com a sua senha nova.
                                    </div>
                                </div>
                            </dd>
                            <dt class="col-sm-5 ver">Plano de subcrição</dt>
                            <dd class="col-sm-7 mb-3 ver">
                                <p th:if="${dadoConta.ehSubscrito}" class="text-success p-0 m-0">Activo</p>
                                <p th:if="${!dadoConta.ehSubscrito}" class="text-danger p-0 m-0">Sem pacote</p>
                            </dd>
                        </dl>
                            <button type="submit" class="btn btn-success editar d-none w-100" th:if="${dadoConta.tipoConta.designacao != empresaPerfil}">Guardar</button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Voltar</button>
                <button type="button" class="btn btn-warning" id="modificarButton" value="0">Editar perfil</button>

            </div>
        </div>
    </div>
</div>
<!--Chat design-->
<div class="row container">
    <div class="panelMensagem fixed-bottom pb-3 pe-4" style="visibility: hidden;overflow-y: auto">
        <div class="position-relative row bg-white shadow rounded-3"  style="float: right;z-index: 10;width: 20em;height: 28em">
            <div class="position-relative p-3 rounded-top-3 d-flex flex-row justify-content-between" style="background-color: #AB81A3;">
                <h6 class="text-white m-0 ">Assistente virtual</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="button-fechar-chat"></button>
            </div>
            <ul id="caixa-mensagem" style="text-decoration: none;width: 100%;height:65%;overflow-y: auto">

            </ul>
            <div class="position-absolute fixed-bottom row g-1 p-2">
                <div class="col-9">
                    <input type="text" class="form-control fs-8 rounded-pill" id="myMessage" placeholder="O que precisa saber?">
                </div>
                <div class="col-2">
                    <button type="button" class="btn text-white rounded-pill" id="submeter" style="background-color: #AB81A3">Enviar</button>
                </div>
            </div>
        </div>
    </div>
    <div class="buttonAssitente fixed-bottom pb-3">
        <div class="row position-relative" style="float: right;z-index: 5">
            <div class="flex flex-row assistente">
                <label class="px-2 rounded-3 shadow-sm fw-bold" style="background-color: white;color: #7c6aa2">Assistente virtual</label>
                <button class="btn rounded-4" type="button" style="background-color: #AB81A3;color: white;width: 4em;height: 4em" id="button-chat">
                    <img th:src="@{/img/virtual.png}" style="" width="100%">
                </button>
            </div>
        </div>
    </div>
</div>
<div class="toast-container position-fixed bottom-0 end-0 p-3 "  data-bs-autohide="false">
    <div id="liveToast" class="toast  border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto" id="titlo-alerta">MusalaLinkUp</strong>
            <small class="text-body-secondary">Agora</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="mensagem">
            Nossos projectos estão em alta!
        </div>
    </div>
</div>
<!--<div class="container-fluid bg-white text-center shadow mt-3 fixed-bottom">
    <div class="container d-flex flex-row justify-content-between pt-3">
        <div class="menu-rodape">
            <ul class="nav">
                <li class="nav-item m-0"><a class="nav-link" style="color: #7c6aa2" href="">Portal</a></li>
                <li class="nav-item m-0"><a class="nav-link" style="color: #7c6aa2" href="">Iniciar Sessão</a></li>
                <li class="nav-item m-0"><a class="nav-link" style="color: #7c6aa2" href="">Registrar</a></li>
            </ul>
        </div>
        <div class="d-flex flex-row align-items-center">
            <i class="fs-7 me-2 text-muted" style="font-style: normal">Media Social</i>
            <i class="fs-7 bi bi-facebook me-2" style="color: #7c6aa2"></i>
            <i class="fs-7 bi bi-instagram me-2" style="color: #7c6aa2"></i>
            <i class="fs-7 bi bi-whatsapp" style="color: #7c6aa2"></i>
        </div>
    </div>
    <small class="text-muted" style="font-size: 12px">Software desenvolvido pela Sakidila©, todos direitos
        reservados.</small>
</div>-->

<script>
    var validar=false;

    window.onload = (event) => {
        getMunicipios();

        getFaculdades();
        getBairros();


        //Senha predifinida carregada

        pass = $("#password").val()
        $("#senha-nova").val(pass);
        $("#senha-confirmar").val(pass);
        $("#senha-antiga").val(pass);
        $("#senha-novaP").val(pass);
        $("#senha-confirmarP").val(pass);
        $("#senha-antigaP").val(pass);

        const toastLiveExample = document.getElementById('liveToast')

        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        setTimeout(() => {
            toastBootstrap.show()
        },1000)
    };

    function getParticipantes(id) {
        const idProjecto = id;

    }

    $(document).ready(function () {
        $("#instituto").change(function () {
            getFaculdades()
        })

        $("#faculdade").change(function () {
            getCentros()
        })

        $("#senha-antiga").change(function (){

            decodificar().then(() => {
                component = $("#senha-antiga")
                if(validar) {
                    component.addClass("is-valid")
                    component.removeClass("is-invalid")
                }
                else {
                    component.addClass("is-invalid")
                    component.removeClass("is-valid")
                }
            })
        })

        $("#senha-antigaP").change(function (){
            decodificarP().then(() => {
                component = $("#senha-antigaP")
                if(validar) {
                    component.addClass("is-valid")
                    component.removeClass("is-invalid")
                }
                else {
                    component.addClass("is-invalid")
                    component.removeClass("is-valid")
                }
            })
        })

        $("#provincia").change(function () {
            $("#municipio").empty();

            getMunicipios();
        })

        $("#button-chat").click(function (){
            $(".panelMensagem").attr("style","overflow-y: auto;visibility:visible")
            $(".buttonAssitente").attr("style","visibility:hidden")
        })

        $("#button-fechar-chat").click(function (){
            $(".panelMensagem").attr("style","visibility:hidden")
            $(".buttonAssitente").attr("style","visibility:visible")
        })

        $("#municipio").change(function () {
            $("#bairro").empty();
            getBairros();
        })

        $("#modificarButton").click(function (){
            modificarView();
        })

        $("#alterarSenha").click(function (){
            showInputSenhas();
        })

        $("#submeter").click(function (){

           chatEnviar = $("<li class='chat-enviar pt-2 d-flex flex-column'>");
           mySms = $("<small style='color: #AB81A3' class='p-2 rounded-3 bg-light text-end'>");
           mySms.text($("#myMessage").val());
           mySms.appendTo(chatEnviar);
           if(mySms.text() != '') {
               chatEnviar.appendTo($("#caixa-mensagem"))
               getRespostaBot(mySms.text())
           }


        })
    })


    function showInputSenhas(){

        var password = $("#password").val();

        if($("#alterarSenha").val() == "0"){
            $(".editarSenha").removeClass("d-none")
            $(".verSenha").addClass("d-none")
            $("#alterarSenha").val("1")
            $("#senha-nova").val("");
            $("#senha-antiga").val("");
            $("#senha-confirmar").val("");
            $("#senha-antigaP").val("");
            $("#senha-novaP").val("");
            $("#senha-confirmarP").val("");
        }
        else{
            $(".verSenha").removeClass("d-none")
            $(".editarSenha").addClass("d-none")
            $("#alterarSenha").val("0")
            $("#senha-nova").val(password);
            $("#senha-confirmar").val(password);
            $("#senha-antiga").val(password);
            $("#senha-novaP").val(password);
            $("#senha-confirmarP").val(password);
            $("#senha-antigaP").val(password);
        }
    }

    function getMunicipios() {
        provinciaId = $("#provincia").val();
        url = "http://localhost:8080/localidadesAjax/" + provinciaId;

        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                municipios = $("#municipio")
                $.each(responseJson, function (index, localidade) {
                    $("<option>")
                        .val(localidade.id)
                        .text(localidade.designacao)
                        .appendTo(municipios);

                })
                getBairros()
            })
            .fail(function () {
            })
            .always(function () {
            });
    }


    function getBairros() {
        municipioId = $("#municipio").val();
        url = "http://localhost:8080/localidadesAjax/" + municipioId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                bairros = $("#bairro")
                $.each(responseJson, function (index, localidade) {
                    $("<option>")
                        .val(localidade.id)
                        .text(localidade.designacao)
                        .appendTo(bairros);

                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }

    function getFaculdades() {
        idInstituto = $("#instituto").val();
        url = "http://localhost:8080/entidadeFaculdadeAjax/" + idInstituto
        $.ajax({method: "GET", url})
            .done(function (responseJson) {

                tabela = $("#faculdade")
                tabela.empty();
                $.each(responseJson, function (index, item) {
                    $("<option>")
                        .text(item.id.faculdade.designacao)
                        .val(item.id.faculdade.id)
                        .appendTo(tabela)
                })
            })
    }

    function getRespostaBot(mensagem) {
        alert(mensagem)
        url = "http://localhost:8080/chat"
        $.ajax({method: "GET",
            url,
            data: {
                "prompt": mensagem
            },
            success: function(response) {
                chatReceber = $("<li class='chat-receber pt-2 d-flex flex-column'>")
                imagemBot = $("<span class='mb-1'>")
                foto = $("<img src='/img/virtual.png' width='30' style='background-color: #AB81A3; padding: 5px' class ='rounded-circle'>")
                foto.appendTo(imagemBot)
                imagemBot.appendTo(chatReceber)
                botSms = $("<small style='background-color: #AB81A3' class='p-2 rounded-3 text-white'>")
                botSms.text(response)
                botSms.appendTo(chatReceber)
                chatReceber.appendTo($("#caixa-mensagem"))
            }
        })
    }

    function getCentros() {
        idFaculdade = $("#faculdade").val();
        url = "http://localhost:8080/CentroAjax/" + idFaculdade
        $.ajax({method: "GET", url})
            .done(function (responseJson) {

                tabela = $("#centro")
                tabela.empty();
                $.each(responseJson, function (index, item) {
                    $("<option>")
                        .text(item.designacao)
                        .val(item.id)
                        .appendTo(tabela)
                })
            })
    }

    function modificarView(){
        if($("#modificarButton").val() == "0") {
            $(".editar").removeClass("d-none")
            $(".ver").addClass("d-none")
            $("#modificarButton").removeClass("btn-warning")
            $("#modificarButton").addClass("btn-outline-warning")
            $("#modificarButton").text("Apenas ver")
            $("#modificarButton").val("1")
            $("#salvarButton").removeClass("d-none")
        }
        else if ($("#modificarButton").val() == "1"){
            $(".ver").removeClass("d-none")
            $(".editar").addClass("d-none")
            $("#modificarButton").removeClass("btn-outline-warning")
            $("#modificarButton").addClass("btn-warning")
            $("#modificarButton").text("Modificar")
            $("#modificarButton").val("0")
            $("#salvarButton").addClass("d-none")
        }
    }

    $(document).on("click", "#projectos button.addCarrinho", function sms() {
        let div = $(this).closest('div');
        $("#nome").text(div.find($("[name=projectoDesignacaoAuxIn]")).val());
        $("#orcamento").text(div.find($("[name=precoIn]")).val());
        $("#investidor").val(div.find($("[name=projectoIdAuxIn]")).val())
        $("#projecto").val(div.find($("[name=investidorIdAuxIn]")).val())

    })

    $(document).on("click", "#projectos button.addCarrinho", function sms() {
        let div = $(this).closest('div');
        $("#designacaoAux").text(div.find($("[name=projectoDesignacaoAux]")).val());
        $("#imagemAux").attr("src", div.find($("[name=projectoImagemAux]")).val());
        $("#centroAux").text(div.find($("[name=centro]")).val());
        $("#descricaoAux").text(div.find($("[name=projectoDescricaoAux]")).val());
        $("#instituicaoAux").text(div.find($("[name=instituto]")).val());
        $("#faculdadeAux").text(div.find($("[name=faculdade]")).val());
        $("#dataCriacaoAux").text(div.find($("[name=dataCriacao]")).val());
        $("#precoAux").text(div.find($("[name=preco]")).val());
        $("#gestorAux").text(div.find($("[name=projectoGestorAux]")).val());
        idDocumento = div.find($("[name=documento]")).val();
        docNome = div.find($("[name=docNome]")).val();
        url = "http://localhost:8080/Ajax/projecto/" + div.find($("[name=projectoIdAux]")).val();
        urlFicheiro = "http://localhost:8080/documento/" + idDocumento;
        documentoLink = $("#link")
        documentoLink.val(idDocumento)
        documentoLink.text(docNome)
        documentoLink.attr({
            target: "_blank",
            href: urlFicheiro
        })

        $.ajax({method: "GET", url})
            .done(function (responseJson) {

                tabela = $("#participantes")
                tabela.empty();
                $.each(responseJson, function (index, item) {
                    $("<p>")
                        .text(item.chave.conta.pessoa.nome + " " + item.chave.conta.pessoa.apelido)
                        .appendTo(tabela)
                })
            })
    })

    function validDataCartaoExp(){
        info = document.forms["myForm"]["dataCartaoExp"].value;
        component = $("#dataCartaoExp")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validNumcartao(){
        info = document.forms["myForm"]["numCartao"].value;
        component = $("#numCartao")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validCheck(){
        info = document.forms["myForm"]["checkpagamento"].value;
        if(info != "" ) {
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
        }
    }

    function validCVV(){
        info = document.forms["myForm"]["cvvValidate"].value;
        component = $("#cvvValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDenominacao(){
        info = document.forms["formEmpresa"]["entidade.designacao"].value;
        component = $("#designacaoEmp")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSigla(){
        info = document.forms["formEmpresa"]["entidade.sigla"].value;
        component = $("#siglaEmp")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validNif(){
        info = document.forms["formEmpresa"]["entidade.nif"].value;
        component = $("#nifEmp")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validTelEmp(){
        info = document.forms["formEmpresa"]["contacto"].value;
        component = $("#telEmp")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validEnderecoEmp(){
        info = document.forms["formEmpresa"]["entidade.endereco"].value;
        component = $("#enderecoEmp")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validUsernameEmp(){
        info = document.forms["formEmpresa"]["username"].value;
        component = $("#usernameEmp")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validEmailEmp(){
        info = document.forms["formEmpresa"]["email"].value;
        component = $("#emailEmp")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenhaEmp(){
        info = document.forms["formEmpresa"]["senha-antiga"].value;
        component = $("#senha-antiga")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenhaNovaEmp(){
        info = document.forms["formEmpresa"]["senha"].value;
        component = $("#senha-nova")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenhaConfirmarEmp(){
        info = document.forms["formEmpresa"]["senha-confirmar"].value;
        component = $("#senha-confirmar")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    //Validação pessoa

    function validNomeP(){
        info = document.forms["formUser"]["pessoa.nome"].value;
        component = $("#nomeP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validApelidoP(){
        info = document.forms["formUser"]["pessoa.apelido"].value;
        component = $("#apelidoP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validBiP(){
        info = document.forms["formUser"]["pessoa.bi"].value;
        component = $("#biP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataNascimentoP(){
        info = document.forms["formUser"]["pessoa.dataNascimento"].value;
        component = $("#dataNascimentoP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataEmissaoP (){
        info = document.forms["formUser"]["pessoa.dataEmissao"].value;
        component = $("#dataEmissaoP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataValidadeP (){
        info = document.forms["formUser"]["pessoa.dataValidade"].value;
        component = $("#dataValidadeP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validEnderecoP (){
        info = document.forms["formUser"]["pessoa.endereco"].value;
        component = $("#enderecoP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validUsernameP (){
        info = document.forms["formUser"]["username"].value;
        component = $("#usernameP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenhaP (){
        info = document.forms["formUser"]["senha-antiga"].value;
        component = $("#senha-antigaP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenhaNovaP(){
        info = document.forms["formUser"]["senha"].value;
        component = $("#senha-novaP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenhaConfirmarP(){
        info = document.forms["formUser"]["senha-confirmar"].value;
        component = $("#senha-confirmarP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataNascimentoP(){
        info = document.forms["formUser"]["pessoa.dataNascimento"].value;
        component = $("#dataNascimentoP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validEmailP(){
        info = document.forms["formUser"]["email"].value;
        component = $("#emailP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validContactoP(){
        info = document.forms["formUser"]["contacto"].value;
        component = $("#contactoP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    var dadosGlobais;

    // Empresa
    function fazerRequisicao() {
        email = document.forms["formEmpresa"]["username"].value;

        url = "http://localhost:8080/usuarioAjax/" + email;
        // Retorna uma Promise que resolve quando a requisição é bem-sucedida
        return new Promise((resolve, reject) => {
            // Faz a requisição usando a função fetch
            fetch(url)
                .then(response => {

                    // Verifica se a resposta da requisição é bem-sucedida (status 2xx)
                    if (!response.ok) {
                        throw new Error('Erro na requisição');
                    }
                    // Parseia a resposta como JSON
                    return response.json();
                })
                .then(data => {
                    // Armazena os dados na variável global
                    dadosGlobais = data;
                    // Resolve a Promise
                    submitValidation = true
                    resolve(submitValidation);
                })
                .catch(error => {
                    // Rejeita a Promise em caso de erro
                    reject(error);
                });
        });
    }


    // Empresa
        function decodificar() {
        id = document.forms["formEmpresa"]["id"].value;
        senha = document.forms["formEmpresa"]["senha-antiga"].value;

        url = "http://localhost:8080/usuarioAjax/conta/"+id+"/"+senha;
        // Retorna uma Promise que resolve quando a requisição é bem-sucedida
        return new Promise((resolve, reject) => {
            // Faz a requisição usando a função fetch
            fetch(url)
                .then(response => {

                    // Verifica se a resposta da requisição é bem-sucedida (status 2xx)
                    if (!response.ok) {
                        throw new Error('Erro na requisição');
                    }
                    // Parseia a resposta como JSON
                    return response.json();
                })
                .then(data => {
                    // Armazena os dados na variável global
                    validar = data;
                    // Resolve a Promise
                    submitValidation = true
                    resolve(submitValidation);
                })
                .catch(error => {
                    // Rejeita a Promise em caso de erro
                    reject(error);
                });
        });
    }



    //Empresa
    function validateFormEmp() {

        const toastLiveExample = document.getElementById('liveToast')
        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        let submit = false;
        fazerRequisicao().then((submitValidation) => {
            nome = document.forms["formEmpresa"]["entidade.designacao"].value;
            sigla = document.forms["formEmpresa"]["entidade.sigla"].value;
            nif = document.forms["formEmpresa"]["entidade.nif"].value;
            endereco = document.forms["formEmpresa"]["entidade.endereco"].value;
            username = document.forms["formEmpresa"]["username"].value;
            email = document.forms["formEmpresa"]["email"].value;
            senha = document.forms["formEmpresa"]["senha-nova"].value;
            senhaConfirmar = document.forms["formEmpresa"]["senha-confirmar"].value;
            contacto = document.forms["formEmpresa"]["contacto"].value;
            if (nome == ""
                || sigla == ""
                || nif == ""
                || endereco == ""
                || username == ""
                || email == ""
                || senha == ""
                || senhaConfirmar == ""
                || contacto == ""
                || senha != senhaConfirmar) {

                validDenominacao()
                validSigla()
                validNif()
                validEnderecoEmp()
                validUsernameEmp()
                validTelEmp()
                validEmailEmp()
                validSenhaEmp()
                validSenhaConfirma()

                //confirmar senha

                if (senha != senhaConfirmar) {

                    $("#liveToast").addClass("text-bg-danger")
                    $("#titlo-alerta").empty()
                    $("#titlo-alerta").text("Erro ao criar conta estudante")
                    $("#mensagem").empty()
                    $("#mensagem").text("A senha nova deve ser a mesma no campo confirmar senha")
                    toastBootstrap.show()
                    submit = false;
                }
                setTimeout(() => {
                    const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
                    $("#liveToast").addClass("text-bg-danger")
                    $("#liveToast").removeClass("text-bg-warning")
                    $("#titlo-alerta").empty()
                    $("#titlo-alerta").text("Erro ao criar conta estudante")
                    $("#mensagem").empty()
                    $("#mensagem").text("Verifique os campos se estão vazios ou devidamente preenchidos.")
                    toastBootstrap.show()
                    submit = false;
                }, 4000)
            } else if (dadosGlobais) {
                submit = false
                const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
                $("#liveToast").addClass("text-bg-warning")
                $("#liveToast").removeClass("text-bg-danger")
                $("#titlo-alerta").empty()
                $("#titlo-alerta").text("Erro ao criar conta estudante")
                $("#mensagem").empty()
                $("#mensagem").text("Já existe uma conta com este username")
                toastBootstrap.show()
            } else {
                submit = submitValidation;
                $("#liveToast").removeClass("text-bg-danger")
                $("#liveToast").removeClass("text-bg-warning")
                $("#titlo-alerta").empty()
                $("#titlo-alerta").text("MusalaLinkUp")
                $("#mensagem").empty()
                $("#mensagem").text("Os dados da tua conta foram alterados com sucesso. Agora podes iniciar sua sessão.")
                toastBootstrap.show()
                setTimeout(() => {
                    document.formEmpresa.submit()
                }, 5000)
            }
        })
        return submit;
    }


    // Pessoas
    function fazerRequisicaoP() {
        email = document.forms["formUser"]["username"].value;

        url = "http://localhost:8080/usuarioAjax/" + email;
        // Retorna uma Promise que resolve quando a requisição é bem-sucedida
        return new Promise((resolve, reject) => {
            // Faz a requisição usando a função fetch
            fetch(url)
                .then(response => {

                    // Verifica se a resposta da requisição é bem-sucedida (status 2xx)
                    if (!response.ok) {
                        throw new Error('Erro na requisição');
                    }
                    // Parseia a resposta como JSON
                    return response.json();
                })
                .then(data => {
                    // Armazena os dados na variável global
                    dadosGlobais = data;
                    // Resolve a Promise
                    submitValidation = true
                    resolve(submitValidation);
                })
                .catch(error => {
                    // Rejeita a Promise em caso de erro
                    reject(error);
                });
        });
    }


    // Pessoas
    function decodificarP() {
        id = document.forms["formUser"]["id"].value;
        senha = document.forms["formUser"]["senha-antiga"].value;
        url = "http://localhost:8080/usuarioAjax/conta/"+id+"/"+senha;
        // Retorna uma Promise que resolve quando a requisição é bem-sucedida
        return new Promise((resolve, reject) => {
            // Faz a requisição usando a função fetch
            fetch(url)
                .then(response => {

                    // Verifica se a resposta da requisição é bem-sucedida (status 2xx)
                    if (!response.ok) {
                        throw new Error('Erro na requisição');
                    }
                    // Parseia a resposta como JSON
                    return response.json();
                })
                .then(data => {
                    // Armazena os dados na variável global
                    validar = data;
                    // Resolve a Promise
                    submitValidation = true
                    resolve(submitValidation);
                })
                .catch(error => {
                    // Rejeita a Promise em caso de erro
                    reject(error);
                });
        });
    }

    //Empresa
    function validateFormP() {

        const toastLiveExample = document.getElementById('liveToast')
        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        let submit = false;
        fazerRequisicaoP().then((submitValidation) => {

            nome = document.forms["formUser"]["pessoa.nome"].value;
            apelido = document.forms["formUser"]["pessoa.apelido"].value;
            email = document.forms["formUser"]["email"].value;
            endereco = document.forms["formUser"]["pessoa.endereco"].value;
            dataEmissao = document.forms["formUser"]["pessoa.dataEmissao"].value;
            dataValidade = document.forms["formUser"]["pessoa.dataValidade"].value;
            dataNascimento = document.forms["formUser"]["pessoa.dataNascimento"].value;

            bi = document.forms["formUser"]["pessoa.bi"].value;
            contacto = document.forms["formUser"]["contacto"].value;
            username = document.forms["formUser"]["username"].value;

            senha = document.forms["formUser"]["senha"].value;
            senhaConfirmar = document.forms["formUser"]["senha-confirmar"].value;

            // por mexer aqui!!!!!!!!
            if (nome == ""
                || apelido == ""
                || email == ""
                || endereco == ""
                || dataEmissao == ""
                || dataValidade == ""
                || dataNascimento == ""
                || bi == ""
                || contacto == ""
                || username == ""
                || senha == ""
                || senhaConfirmar == ""
                || senha != senhaConfirmar) {

                validNomeP()
                validApelidoP()
                validEmailP()
                validEnderecoP()
                validDataEmissaoP()
                validDataValidadeP()
                validDataNascimentoP()
                validBiP()
                validContactoP()
                validUsernameP()
                validSenhaP()
                validSenhaNovaP()
                validSenhaConfirmarP()

                //confirmar senha

                if (senha != senhaConfirmar) {

                    $("#liveToast").addClass("text-bg-danger")
                    $("#titlo-alerta").empty()
                    $("#titlo-alerta").text("Erro ao criar conta estudante")
                    $("#mensagem").empty()
                    $("#mensagem").text("A senha nova deve ser a mesma no campo confirmar senha")
                    toastBootstrap.show()
                    submit = false;
                }
                setTimeout(() => {
                    const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
                    $("#liveToast").addClass("text-bg-danger")
                    $("#liveToast").removeClass("text-bg-warning")
                    $("#titlo-alerta").empty()
                    $("#titlo-alerta").text("Erro ao criar conta estudante")
                    $("#mensagem").empty()
                    $("#mensagem").text("Verifique os campos se estão vazios ou devidamente preenchidos.")
                    toastBootstrap.show()
                    submit = false;
                }, 4000)
            } else if (dadosGlobais) {
                submit = false
                const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
                $("#liveToast").addClass("text-bg-warning")
                $("#liveToast").removeClass("text-bg-danger")
                $("#titlo-alerta").empty()
                $("#titlo-alerta").text("Erro ao criar conta estudante")
                $("#mensagem").empty()
                $("#mensagem").text("Já existe uma conta com este username")
                toastBootstrap.show()
            } else {
                submit = submitValidation;
                $("#liveToast").removeClass("text-bg-danger")
                $("#liveToast").removeClass("text-bg-warning")
                $("#titlo-alerta").empty()
                $("#titlo-alerta").text("MusalaLinkUp")
                $("#mensagem").empty()
                $("#mensagem").text("Os dados da tua conta foram alterados com sucesso. Agora podes iniciar sua sessão.")
                toastBootstrap.show()
                setTimeout(() => {
                    document.formUser.submit()
                }, 5000)
            }
        })
        return submit;
    }


    function validateForm() {
        nome = document.forms["myForm"]["numCartao"].value;
        if (nome == "") {
            validCheck()
            validCVV()
            validNumcartao()
            validDataCartaoExp()
            return false;
        }
        return true;
    }
</script>
</body>
</html>