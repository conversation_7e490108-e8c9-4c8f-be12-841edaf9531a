package com.sakidila.projectoic.config;

import com.sakidila.projectoic.repository.ContaRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;

@EnableWebSecurity
@Configuration
public class SecurityConfig {

    @Autowired
    ContaRepository  repository;

    @Bean
    public UserDetailsService userDetailsService() {
        return username -> repository.findByNomeUsuario(username).orElseThrow(()-> new UsernameNotFoundException("Usuario não encontrado"));
    }

    @Bean
    public BCryptPasswordEncoder getPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }


    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        return http.authorizeHttpRequests()
                .requestMatchers("/**").permitAll()
                .requestMatchers("/sakidila_ic/usuario/principal").permitAll()
                .requestMatchers("/sakidila_ic/portal/").permitAll()
                .requestMatchers("/sakidila_ic/usuario/gestor_instituicao_registrar").permitAll()
                .requestMatchers("/sakidila_ic/usuario/empresa_registrar").permitAll()
                .requestMatchers("/sakidila_ic/usuario/investigador_instituicao_registrar").permitAll()
                .requestMatchers("/sakidila_ic/portal/projecto/**").permitAll()
                .and()
                .formLogin().loginPage("/sakidila_ic/portal/")
                .loginProcessingUrl("/sakidila_ic/usuario/login").usernameParameter("username").passwordParameter("senha")
                .defaultSuccessUrl("/sakidila_ic/portal/listagem/1")
                .failureUrl("/sakidila_ic/usuario/login")
                .and()
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED)
                .and()
                .authenticationProvider(getDaoAuthenticationProvider())
                .csrf().disable()
                .build();
    }

    @Bean
    public DaoAuthenticationProvider getDaoAuthenticationProvider() {
        DaoAuthenticationProvider daoAuthenticationProvider = new DaoAuthenticationProvider();
        daoAuthenticationProvider.setUserDetailsService(userDetailsService());
        daoAuthenticationProvider.setPasswordEncoder(getPasswordEncoder());
        return daoAuthenticationProvider;
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration configuration) throws Exception {
        return configuration.getAuthenticationManager();
    }

}
