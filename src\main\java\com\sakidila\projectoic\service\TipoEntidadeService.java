package com.sakidila.projectoic.service;

import com.sakidila.projectoic.entity.TipoEntidade;
import com.sakidila.projectoic.repository.TipoEntidadeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TipoEntidadeService {

    @Autowired
    private TipoEntidadeRepository repository;

        public TipoEntidade criarEntidade(TipoEntidade tipoEntidade){
            return repository.save(tipoEntidade);
        }

        public List<TipoEntidade> listaEntidades(){
            return repository.findAll();
        }
}
