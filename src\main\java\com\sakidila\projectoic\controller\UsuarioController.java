package com.sakidila.projectoic.controller;

import com.sakidila.projectoic.entity.*;
import com.sakidila.projectoic.helper.Mensagem;
import com.sakidila.projectoic.repository.TipoContaRepository;
import com.sakidila.projectoic.service.*;
import com.sakidila.projectoic.utility.Nomenclatura;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

@Controller
@RequestMapping("sakidila_ic/usuario")
public class UsuarioController {

    Nomenclatura nomenclatura = new Nomenclatura();

    @Autowired
    private ContaService service;

    @Autowired
    private TipoContaService contaService;

    @Autowired
    private PessoaService pessoaService;

    @Autowired
    private LocalidadeServico localidadeServico;

    @Autowired
    private NivelAcademicoService nivelAcademicoService;

    @Autowired
    private EntidadeServico entidadeServico;

    @Autowired
    private FaculdadeService faculdadeService;

    @Autowired
    private SexoService sexoService;

    @Autowired
    private TipoContaRepository tipoContaRepository;

    @Autowired
    private EstadoService estadoService;

    @Autowired
    private FicheiroServicoImpl servicoImpl;

    @Autowired
    private EntidadeFaculdadeService entidadeFaculdadeService;



    @Autowired
    private AuthenticationManager authenticationManager;

    @GetMapping("/login")
    public String login(){
        return "login";
    }

    @GetMapping("/convidado_registrar")
    public String convidadoRegistrar(ModelMap model){
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        System.out.println("dados :"+authentication.getName());
        model.addAttribute("localidades",localidadeServico.listaLocalidadesPrimitiva());
        model.addAttribute("niveis",nivelAcademicoService.listaNivelAcademico());
        model.addAttribute("Institutos",entidadeServico.listaInstitutos());
        model.addAttribute("sexos",sexoService.listaSexos());
        model.addAttribute("estados",estadoService.listaEstadoCivil());
        model.addAttribute("faculdades",faculdadeService.listaFaculdades());
        if (authentication.isAuthenticated() && !authentication.getName().equals("anonymousUser")) {
            Conta conta = service.mostrarDadoConta(authentication.getName());
            model.addAttribute("conta", authentication.getName());
            model.addAttribute("userDefault", "anonymousUser");
            model.addAttribute("dadoConta", conta);
            List<EntidadeFaculdade> lista = entidadeFaculdadeService.ListaEntidadeFaculdadePelaInstituto(conta.getEntidade().getId());

            return "convidado_registrar";
        }else{
            SecurityContextHolder.clearContext();
            model.addAttribute("conta",null);
            model.addAttribute("dadoConta",null);
            return "convidado_registrar";
        }
    }

    @GetMapping("/principal")
    public String usuarioEscolher(Model model){
            return "usuario_principal";
    }

    @GetMapping("/gestor_instituicao_registrar")
    public String gestorInstituicaoRegistrar(ModelMap model){
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        System.out.println("dados :"+authentication.getName());
        model.addAttribute("localidades",localidadeServico.listaLocalidadesPrimitiva());
        model.addAttribute("niveis",nivelAcademicoService.listaNivelAcademico());
        model.addAttribute("Institutos",entidadeServico.listaInstitutos());
        model.addAttribute("sexos",sexoService.listaSexos());
        model.addAttribute("estados",estadoService.listaEstadoCivil());
        if (authentication.isAuthenticated() && !authentication.getName().equals("anonymousUser")) {
            Conta conta = service.mostrarDadoConta(authentication.getName());
            model.addAttribute("conta", authentication.getName());
            model.addAttribute("userDefault", "anonymousUser");
            model.addAttribute("dadoConta", conta);

            model.addAttribute("userDefault", "anonymousUser");
            model.addAttribute("gestorInstituicaoPerfil", "ROLE_GESTOR_INSTITUICAO");
            model.addAttribute("gestorCentroPerfil", "ROLE_RESPONSAVEL_CENTRO");
            model.addAttribute("gestorProjectoPerfil", "ROLE_GESTOR_PROJECTO");
            model.addAttribute("investigadorPerfil", "ROLE_INVESTIGATOR");
            model.addAttribute("adminPerfil", "ROLE_ADMIN");

            return "gestor_instituicao_registrar";
        }else{
            SecurityContextHolder.clearContext();
            model.addAttribute("conta",null);
            model.addAttribute("dadoConta",null);
            return "gestor_instituicao_registrar";
        }

    }


    @GetMapping("/empresa_registrar")
    public String empresaRegistrar(ModelMap model){
        model.addAttribute("localidades",localidadeServico.listaLocalidadesPrimitiva());
        return "empresa_registrar";
    }

        @GetMapping("/investigador_instituicao_registrar")
    public String investigadorInstituicaoRegistrar(ModelMap model){
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        System.out.println("dados :"+authentication.getName());
            model.addAttribute("gestorInstituicaoPerfil", "ROLE_GESTOR_INSTITUICAO");
            model.addAttribute("gestorCentroPerfil", "ROLE_RESPONSAVEL_CENTRO");
            model.addAttribute("gestorProjectoPerfil", "ROLE_GESTOR_PROJECTO");
            model.addAttribute("investigadorPerfil", "ROLE_INVESTIGATOR");
        model.addAttribute("localidades",localidadeServico.listaLocalidadesPrimitiva());
        model.addAttribute("niveis",nivelAcademicoService.listaNivelAcademico());
        model.addAttribute("sexos",sexoService.listaSexos());
        model.addAttribute("estados",estadoService.listaEstadoCivil());
        if (authentication.isAuthenticated() && !authentication.getName().equals("anonymousUser")) {
            Conta conta = service.mostrarDadoConta(authentication.getName());
            List<EntidadeFaculdade> lista = entidadeFaculdadeService.ListaEntidadeFaculdadePelaInstituto(conta.getEntidade().getId());
            List<Faculdade> faculdades = faculdadeService.encontrarTodasFaculdadePeloInstituto(lista);
            model.addAttribute("conta", authentication.getName());
            model.addAttribute("userDefault", "anonymousUser");
            model.addAttribute("dadoConta", conta);
            model.addAttribute("instituicao",conta.getEntidade());
            model.addAttribute("faculdades",faculdades);

            return "investigador_instituicao_registrar";
        }else{
            SecurityContextHolder.clearContext();
            model.addAttribute("conta",null);
            model.addAttribute("dadoConta",null);
            return "login";
        }
    }

    @GetMapping("/investigador_independente_registrar")
    public String investigadorInstituicaoIndependenteRegistrar(ModelMap model){
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        System.out.println("dados :"+authentication.getName());
        model.addAttribute("localidades",localidadeServico.listaLocalidadesPrimitiva());
        model.addAttribute("niveis",nivelAcademicoService.listaNivelAcademico());
        model.addAttribute("sexos",sexoService.listaSexos());
        model.addAttribute("estados",estadoService.listaEstadoCivil());
        if (authentication.isAuthenticated() && !authentication.getName().equals("anonymousUser")) {
            Conta conta = service.mostrarDadoConta(authentication.getName());
            model.addAttribute("conta", authentication.getName());
            model.addAttribute("userDefault", "anonymousUser");
            model.addAttribute("dadoConta", conta);

            return "investigador_independente_registrar";
        }else{
            SecurityContextHolder.clearContext();
            model.addAttribute("conta",null);
            model.addAttribute("dadoConta",null);
            return "investigador_independente_registrar";
        }
    }

    @PostMapping("/default")
    public void criarContaPadrao(){
        contaService.criarTipoContaPadrao();
        service.criarContaPadrao();
    }

    @PostMapping("/subscrever")
    public String subscrever(@RequestParam Long idConta,@RequestParam String pagina){
        service.subscrever(idConta);
        return "redirect:/sakidila_ic/portal/listagem/1";
    }

    @PostMapping("/registrar/salvar")
    public String CriarContaEstudante(@ModelAttribute Conta conta, HttpSession session, @RequestParam(name = "imagem") MultipartFile multipartFile) {
        try {
            String imagem = servicoImpl.uploadImage(multipartFile);

            if(imagem != null) {
                conta.setFoto(imagem);
            }
            else
            {

                if(conta.getId() != null) {
                    Conta aux = service.findById(conta.getId());
                    if(aux.getFoto() != null || aux.getFoto() != "")
                        conta.setFoto(aux.getFoto());
                }
            }
            //System.out.println("Data "+conta.getPessoa().getDataEmissao());
            TipoConta tipoConta = tipoContaRepository.findById(Long.parseLong("2")).get();
            conta.setTipoConta(tipoConta);
            conta = service.criarConta(conta,tipoConta.getId());
            session.setAttribute("menssagem", new Mensagem("Conta "+conta.getEmail()+" criada com êxito!","success"));

        }
        catch (Exception e){
            session.setAttribute("menssagem", new Mensagem("Conta "+conta.getEmail()+" não criada,esta conta já existe ou verifique se contem campos vazios!","danger"));
        }

        return "redirect:/sakidila_ic/usuario/login";
    }

    @PostMapping("/investigador/registrar/salvar")
    public String CriarContaInvestigador(@ModelAttribute Conta conta, HttpSession session, @RequestParam(name = "imagem") MultipartFile multipartFile) {
        try {
        String imagem = servicoImpl.uploadImage(multipartFile);
            if(imagem != null) {
                conta.setFoto(imagem);
            }
            else
            {

                if(conta.getId() != null) {
                    Conta aux = service.findById(conta.getId());
                    if(aux.getFoto() != null || aux.getFoto() != "")
                        conta.setFoto(aux.getFoto());
                }
            }
        //System.out.println("Data "+conta.getPessoa().getDataEmissao());
        TipoConta tipoConta = tipoContaRepository.findById(nomenclatura.getINVESTIGADOR()).get();
        conta.setTipoConta(tipoConta);
        conta = service.criarConta(conta,tipoConta.getId());
        session.setAttribute("menssagem", new Mensagem("Conta "+conta.getEmail()+" criada com êxito!","success"));
    }
        catch (Exception e){
        session.setAttribute("menssagem", new Mensagem("Conta "+conta.getEmail()+" não criada,esta conta já existe ou verifique se contem campos vazios!","danger"));
    }
        return "redirect:/sakidila_ic/centro/investigador/associar/1";
    }

    @PostMapping("/investigador_independente/registrar/salvar")
    public String CriarContaInvestigadorIndependente(@ModelAttribute Conta conta, HttpSession session, @RequestParam(name = "imagem") MultipartFile multipartFile) {
        String imagem = servicoImpl.uploadImage(multipartFile);
        conta.setFoto(imagem);
        //System.out.println("Data "+conta.getPessoa().getDataEmissao());
        TipoConta tipoConta = tipoContaRepository.findById(nomenclatura.getINVESTIGADOR_INDEPENDENTE()).get();
        conta.setTipoConta(tipoConta);
        conta = service.criarConta(conta,tipoConta.getId());
        return "redirect:/sakidila_ic/usuario/login";
    }

    @PostMapping("/gestor_instituicao/registrar/salvar")
    public String CriarContaGestorInstituicao(@ModelAttribute Conta conta, HttpSession session, @RequestParam(name = "imagem") MultipartFile multipartFile) {
        try {
        String imagem = servicoImpl.uploadImage(multipartFile);
        conta.setFoto(imagem);
        //System.out.println("Data "+conta.getPessoa().getDataEmissao());
        TipoConta tipoConta = tipoContaRepository.findById(nomenclatura.getGESTOR_INSTITUTO()).get();
        conta.setTipoConta(tipoConta);

        conta = service.criarConta(conta,tipoConta.getId());
        session.setAttribute("menssagem", new Mensagem("Conta "+conta.getEmail()+" criada com êxito!","success"));
    }
        catch (Exception e){
        session.setAttribute("menssagem", new Mensagem("Conta "+conta.getEmail()+" não criada,esta conta já existe ou verifique se contem campos vazios!","danger"));
    }
        return "redirect:/sakidila_ic/entidade/registrar/1";
    }

    @PostMapping("/empresa/registrar/salvar")
    public String CriarContaEmpresa(@ModelAttribute Conta conta, HttpSession session, @RequestParam(name = "imagem") MultipartFile multipartFile) {
        try {
        String imagem = servicoImpl.uploadImage(multipartFile);
            if(imagem != null) {
                conta.setFoto(imagem);
                conta.getEntidade().setLogotipo(imagem);
            }
            else
            {

                if(conta.getId() != null) {
                    Conta aux = service.findById(conta.getId());
                    if(aux.getFoto() != null || aux.getFoto() != "") {
                        conta.setFoto(aux.getFoto());
                        conta.getEntidade().setLogotipo(imagem);
                    }
                }
            }

        //System.out.println("Data "+conta.getPessoa().getDataEmissao());
        TipoConta tipoConta = tipoContaRepository.findById(nomenclatura.getEMPRESA()).get();
        conta.setTipoConta(tipoConta);

        conta = service.criarConta(conta,tipoConta.getId());
        session.setAttribute("menssagem", new Mensagem("Conta "+conta.getEmail()+" criada com êxito!","success"));
    }
        catch (Exception e){
        session.setAttribute("menssagem", new Mensagem("Conta "+conta.getEmail()+" não criada,esta conta já existe ou verifique se contem campos vazios!","danger"));
    }
        return "redirect:/sakidila_ic/usuario/login";
    }

    @PostMapping("/update")
    public String CriarConta(@ModelAttribute Conta conta, HttpSession session, @RequestParam(name = "imagem") MultipartFile multipartFile) {
        try {
            String imagem = servicoImpl.uploadImage(multipartFile);
            if(imagem != null) {
                conta.setFoto(imagem);
            }
            else
            {

                if(conta.getId() != null) {
                    Conta aux = service.findById(conta.getId());
                    if(aux.getFoto() != null || aux.getFoto() != "") {
                        conta.setFoto(aux.getFoto());
                    }
                }
            }
            //System.out.println("Data "+conta.getPessoa().getDataEmissao());
            TipoConta tipoConta = tipoContaRepository.findById(conta.getTipoConta().getId()).get();
            conta.setTipoConta(tipoConta);
            System.out.println("Teste conta: "+conta.toString());
            conta = service.criarConta(conta,tipoConta.getId());
            session.setAttribute("menssagem", new Mensagem("Conta "+conta.getEmail()+" criada com êxito!","success"));

        }
        catch (Exception e){
            session.setAttribute("menssagem", new Mensagem("Conta "+conta.getEmail()+" não criada,esta conta já existe ou verifique se contem campos vazios!","danger"));
        }

        return "redirect:/sakidila_ic/usuario/login";
    }

    @GetMapping("/desativar/{id}")
    public String disabledByIdCentro(@PathVariable Long id){
        service.desativar(id);
        return "redirect:/sakidila_ic/usuario/login";

    }

}
