<!DOCTYPE html>
<html lang="en"  xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">

    <!-- Importação do Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
            crossorigin="anonymous"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
    <style th:inline="text">
        body{
            font-family: Poppins;
        }
    </style>
    <title>Registra-se na Sakidila!</title>
</head>
<body class="bg-light row" style="overflow-x: hidden" >


<!--Menu da App-->
<div class="col-xl-2 col-lg-3 col-md-4">
<div class="d-flex flex-column flex-shrink-0 p-3 bg-white shadow-sm position-fixed" style="width: 280px; height:100vh">
    <a href="/sakidila_ic/portal/" class="d-flex align-items-center mb-3 mb-md-0 me-md-auto link-body-emphasis text-decoration-none">
        <img src="/img/logotipo.jpg" alt="Bootstrap" width="100"> <span class="fw-bold text-dark text-center">Gestão Acadêmica</span></a>
    </a>
    <hr>
    <ul class="nav nav-pills flex-column mb-auto">
        <li class="nav-item">
            <a href="/sakidila_ic/portal/" class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Inicio
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/entidade/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == adminPerfil}" class="nav-link active" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Entidade
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/entidade_faculdade/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Faculdade
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/usuario/gestor_instituicao_registrar"
               th:if="${dadoConta.tipoConta.designacao == adminPerfil}"
               class="nav-link link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#table"></use></svg>
                Gestor de Instituição
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/centro/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link  link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use h:href="/sakidila_ic/centro/registrar"></use></svg>
                Centro de Investigação
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/centro/investigador/associar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil}"
               class="nav-link  link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use h:href="/sakidila_ic/centro/registrar"></use></svg>
                Centro de Investigação
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/portal/projecto/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil
               or dadoConta.tipoConta.designacao == gestorProjectoPerfil
               or dadoConta.tipoConta.designacao == investigadorPerfil}"
               class="nav-link">
                <svg class="bi pe-none me-2" width="16" height="16"><use href="/sakidila_ic/centro/registrar"></use></svg>
                Projectos
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/portal/projecto/pendentes/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link">
                <svg class="bi pe-none me-2" width="16" height="16"><use href="/sakidila_ic/centro/registrar"></use></svg>
                Projectos pendentes
            </a>
        </li>
        <li>
            <a href="#" class="nav-link link-body-emphasis"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}">
                <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#table"></use></svg>
                Estudante
            </a>
        </li>
    </ul>
    <hr>
    <div class="dropdown">
        <a href="#" class="d-flex align-items-center link-body-emphasis text-decoration-none dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
            <img th:if="${conta == userDefault}" th:src="@{/img/user.png}" alt="mdo" width="32" height="32" class="rounded-circle">
            <img th:if="${conta != null}" th:src="${dadoConta.foto}" alt="mdo" width="32" height="32" class="rounded-circle">
            <strong>[[${dadoConta.username}]]</strong>
        </a>
        <ul class="dropdown-menu text-small shadow">
            <li><a th:if="${conta == userDefault}" class="dropdown-item" href="/sakidila_ic/usuario/login">Entrar</a></li>
            <li><a th:if="${conta != null}" class="dropdown-item" href="">Meu Perfil</a></li>
            <li><a th:if="${conta != null}" class="dropdown-item" href="/logout">Sair</a></li>
        </ul>
    </div>
</div>
</div>

<div class="col-xl-10 col-lg-9 col-md-8">
<div class="container">
    <div class="d-flex flex-row mt-4 justify-content-end">
        <button class=" me-3" style="border:none; background-color:transparent;text-decoration: none; color: #7c6aa2" data-bs-toggle="modal" data-bs-target="#criarInstituto">Registrar Instituição</button>
    </div>
    <!-- Modal -->
    <form method="post" name="myForm" th:action="@{/sakidila_ic/entidade/salvar}" enctype="multipart/form-data" onsubmit="return validateForm()" required>
    <div class="modal fade" id="criarInstituto" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable modal-fullscreen">

            <div class="modal-content">

                <div class="modal-header">
                    <h1 class="" id="staticBackdropLabel"><h5 class="modal-title fs-4 fw-bold" th:if="${dadoConta.tipoConta.designacao != empresaPerfil}" style="color: rgba(158,158,151)">Registrar <i class="fw-bold text-dark">Instituição ou Empresa!</i></h5></h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <fieldset class=" px-5 py-4mb-3">
                        <legend class="text-secondary">Dados </legend>

                        <!--Para cadastrar a instituição-->
                        <input type="hidden" name="tipo.id" th:value="${codInstituicao}">

                            <div class="row mb-3">
                                <div class="col">
                                    <div class="">
                                        <label for="formFile" class="form-label">Carregar o logotipo de sua entidade</label>
                                        <input class="form-control" type="file" id="formFile" name="imagem">
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="">
                                        <label class="form-label">Designação</label>
                                        <input type="text"  class="form-control" placeholder="" id="nomeValidate" onfocusout="validNome()" name="designacao" aria-label="Nome" aria-describedby="Nome">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com o nome da empresa.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col">
                                    <div class="">
                                        <label class="form-label" id="sigla">Sigla</label>
                                        <input type="text"  class="form-control" placeholder="" id="siglaValidate" onfocusout="validSigla()" name="sigla" aria-label="Nome" aria-describedby="Nome">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com a Sigla da empresa.
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="">
                                        <label class="form-label" id="nif">Nif</label>
                                        <input type="text" class="form-control" placeholder="" id="nifValidate" onfocusout="validNif()" name="nif" aria-label="Nome" aria-describedby="Nome">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com o NIF da empresa.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <legend class="text-secondary">Residência</legend>
                            <div class="row mb-3">
                                <div class="col">
                                    <div class="">
                                        <label class="form-label" >Provincia</label>
                                        <select class="form-select" id="provincia" placeholder="" aria-label="provincia" aria-describedby="provincia">
                                            <option th:each="localidade : ${localidades}" th:value="${localidade.id}"
                                                    th:text="${localidade.designacao}"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="">
                                        <label class="form-label" >Municipio</label>
                                        <select class="form-select"  aria-label="municipio"  id="municipio" aria-describedby="municipio"></select>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="">
                                        <label class="form-label" >Bairro</label>
                                        <select class="form-select" id="bairro" name="localidade.id" placeholder="" aria-label="bairro" aria-describedby="bairro"></select>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col">
                                    <div class="">
                                        <label class="form-label" id="rua">Endereço</label>
                                        <input type="text" class="form-control" placeholder="" id="enderecoAltera" onfocusout="validEndereco()" name="endereco" aria-label="rua" aria-describedby="rua">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Especifique a rua, nº da casa.
                                        </div>
                                    </div>
                                </div>
                            </div>

                    </fieldset>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Voltar</button>
                    <button class="btn btn-warning  shadow-sm" type="reset">Limpar</button>
                    <button class="btn btn-primary  shadow-sm" id="registrar" type="button">Registrar</button>
                </div>

            </div>

        </div>
    </div>
    </form>
    <h5 class="display-6 mt-1 mb-1 fw-bold" style="color: rgba(158,158,151)">Visualização <i class="fw-bold text-dark"></i></h5>
    <hr>
    <fieldset class="bg-white px-5 py-4 shadow-sm rounded mb-3">
        <table class="table">
            <tr>
                <th scope="col">Logo</th>
                <th scope="col">Entidade</th>
                <th scope="col" class="col-2">NIF</th>
                <th scope="col">Tipo</th>
                <th scope="col" class="col-2">Perfil</th>
                <th scope="col" class="col-1">Detalhes</th>
                <th scope="col" class="col-1">Remover</th>
            </tr>
            <tr th:each="item : ${listaEntidades}" id="entidades" th:if="${!item.disabled}">
                <td>
                    <img th:src="${item.logotipo}" height="50">
                </td>
                <td>[[${item.designacao}]]</td>
                <td>
                   [[${item.nif}]]
                </td>


                <td th:if="${item.tipo != null}">[[${item.tipo.designacao}]]</td>
                <td th:if="${item.tipo == null}"></td>
                <td><a class="btn btn-warning btn-sm shadow-sm" href="/sakidila_ic/usuario/gestor_instituicao_registrar">Criar Gestor </a></td>
                <td>

                    <input type="hidden" name="idEntidade" th:value="${item.id}">
                    <input type="hidden" name="imagem" th:value="${item.logotipo}">
                    <input type="hidden" name="nif" th:value="${item.nif}">
                    <input type="hidden" name="sigla" th:value="${item.sigla}">
                    <input type="hidden" name="designacao" th:value="${item.designacao}">
                    <input type="hidden" name="endereco" th:value="${item.endereco}">
                    <input type="hidden" name="tipoEntidade" th:value="${item.tipo.designacao}">
                    <input type="hidden" name="bairro" th:value="${item.localidade.designacao}">
                    <input type="hidden" name="municipio" th:value="${item.localidade.localidadePai.designacao}">
                    <input type="hidden" name="provincia" th:value="${item.localidade.localidadePai.localidadePai.designacao}">
                    <input type="hidden" name="idLocalidade" th:value="${item.localidade.id}">
                    <input type="hidden" name="idTipoEntidade" th:value="${item.tipo.id}">

                    <button type="button" class="btn btn-primary btn-sm shadow-sm detalhe" data-bs-target="#modalVer" data-bs-toggle="modal">Ver</button>
                </td>
                <td><a class="btn btn-danger btn-sm shadow-sm" th:href="@{/sakidila_ic/entidade/desativar/{id}(id=${item.id})}">Eliminar</a></td>
                </tr>
            </table>
        <div class="d-flex justify-content-center">
            <nav class="Page navigation example" th:if="${totalPaginas > 1}">
                <ul class="pagination">
                    <li class="page-item"><a class="page-link">Total de projectos: [[${totalItems}]]</a></li>
                    <div th:each="i : ${#numbers.sequence(1,totalPaginas)}">
                        <li class="page-item">
                            <a class="page-link" th:if="${paginaActual != i}"
                               th:href="@{'/sakidila_ic/entidade/registrar/'+ ${i}}">[[${i}]]</a>
                            <a class="page-link" th:unless="${paginaActual != i}">[[${i}]]</a> &nbsp; &nbsp;
                        </li>
                    </div>
                    <li class="page-item">
                        <a class="page-link" th:if="${paginaActual < totalPaginas}"
                           th:href="@{'/sakidila_ic/entidade/registrar/'+${paginaActual + 1}}">Próximo</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" th:if="${paginaActual < totalPaginas}"
                           th:href="@{'/sakidila_ic/entidade/registrar/'+${paginaActual + 1}}">Ultimo</a>
                        <a class="page-link" th:unless="${paginaActual < totalPaginas}">Ultimo</a>
                    </li>
                </ul>
            </nav>
        </div>
        </fieldset>



</div>
</div>
<div class="toast-container position-fixed bottom-0 end-0 p-3 "  data-bs-autohide="false">
    <div id="liveToast" class="toast  border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto" id="titlo-alerta">MusalaLinkUp</strong>
            <small class="text-body-secondary">Agora</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="mensagem">
        </div>
    </div>
</div>
<!--Modal de ver ou editar um projecto-->
<form method="post" name="myFormEditar" th:action="@{/sakidila_ic/entidade/salvar}" enctype="multipart/form-data"  onsubmit="return validateFormEditar()" required>
    <div class="modal fade" id="modalVer" tabindex="-1" aria-labelledby="ModalLabelVer" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="ModalLabelVer">Detalhes da Entidade</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!--<input type="hidden" id="idProjectoAux" name="id">-->
                    <div class="row">
                        <input type="hidden" name="id" id="idEntidade">
                        <input type="hidden" name="endereco" id="enderecoAlterar">
                        <input type="hidden" name="localidade.id" id="idLocalidadeAlterar">
                        <input type="hidden" name="tipo.id" id="idTipoEntidadeAlterar">
                        <div class="ver">
                            <h6>Logotipo</h6>
                            <img id="imagemAux" width="100" height="100">
                        </div>
                        <div class="p-3 editar  d-none">
                            <label class="form-label fw-bolder">Adicione o logotipo da entidade</label>
                            <input class="form-control" type="file" id="imagem" name="imagem">
                        </div>
                    </div>
                    <div class="row">
                        <div class="ver">
                            <h6 class="mb-1">NIF</h6>
                            <p class="" id="nifAux"></p>
                        </div>
                        <div class="p-3 editar d-none">
                            <label class="form-label fw-bolder">NIF</label>
                            <input type="text" class="form-control" onfocusout="validNIFAlterar()" id="nifAltera" name="nif">
                            <div class="invalid-feedback">
                                Preencher o campo com o NIF da entidade.
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="ver">
                            <h6 class="mb-1">Sigla</h6>
                            <p class="" id="siglaAux"></p>
                        </div>
                        <div class="p-3 editar d-none">
                            <label class="form-label fw-bolder">Sigla</label>
                            <input type="text" class="form-control" onfocusout="validSiglaAlterar()" id="siglaAltera" name="sigla">
                            <div class="invalid-feedback">
                                Preencher o campo com a sigla da entidade.
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="ver">
                            <h6 class="mb-1">Designação</h6>
                            <p class="" id="designacaoAux"></p>
                        </div>
                        <div class="p-3 editar d-none">
                            <label class="form-label fw-bolder">Designação</label>
                            <input type="text" class="form-control" onfocusout="validDesignacaoAlterar()" id="designacaoAltera" name="designacao">
                            <div class="invalid-feedback">
                                Preencher o campo com a designação da entidade.
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="ver">
                            <h6 class="mb-1">Tipo de Entidade</h6>
                            <p class="" id="tipoEntidadeAux"></p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="ver">
                            <h6 class="mb-1">Endereco</h6>
                            <p class="" id="enderecoAux"></p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                    <button type="button" class="btn btn-warning" id="modificarButton" value="0">Modificar</button>
                    <button type="submit" class="btn btn-primary d-none" id="salvarButton">Guardar</button>
                </div>
            </div>
        </div>
    </div>
</form>
<script>

    var idEntidade;
    var nif;
    var designacaoGlobal;
    var imagemGlobal;
    var endereco;
    var siglaGlobal;
    var tipoEntidade;
    var idTipoEntidade;
    var idLocalidade;

    window.onload = (event) => {
        getMunicipios();

        getFaculdades();
        getBairros();

    };
    $(document).ready(function () {
        $("#provincia").change(function () {
            $("#municipio").empty();
            getMunicipios();
        })

        $("#municipio").change(function () {
            $("#bairro").empty();
            getBairros();
        })

        $("#instituto").change(function () {
            $("#faculdade").empty();
            getFaculdades();
        })

        $("#registrar").click(function (){
            validateForm()
        })

        $("#modificarButton").click(function (){
            modificarView();
        })
    })

    function getFaculdades() {
        institutoId = $("#instituto").val();
        url = "http://localhost:8080/entidadeFaculdadeAjax/" + institutoId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                faculdades = $("#faculdade")
                $.each(responseJson, function (index, entidadeFaculdade) {
                    $("<option>")
                        .val(entidadeFaculdade.id.faculdade.id)
                        .text(entidadeFaculdade.id.faculdade.designacao)
                        .appendTo(faculdades);

                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }

    function getMunicipios() {
        provinciaId = $("#provincia").val();
        url = "http://localhost:8080/localidadesAjax/" + provinciaId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                municipios = $("#municipio")
                $.each(responseJson, function (index, localidade) {
                    $("<option>")
                        .val(localidade.id)
                        .text(localidade.designacao)
                        .appendTo(municipios);

                })
                getBairros()
            })
            .fail(function () {
            })
            .always(function () {
            });
    }


    function getBairros() {
        municipioId = $("#municipio").val();
        url = "http://localhost:8080/localidadesAjax/" + municipioId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                bairros = $("#bairro")
                $.each(responseJson, function (index, localidade) {
                    $("<option>")
                        .val(localidade.id)
                        .text(localidade.designacao)
                        .appendTo(bairros);

                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }

    function modificarView(){
        if($("#modificarButton").val() == "0") {
            $(".editar").removeClass("d-none")
            $(".ver").addClass("d-none")
            $("#modificarButton").removeClass("btn-warning")
            $("#modificarButton").addClass("btn-outline-warning")
            $("#modificarButton").text("Apenas ver")
            $("#modificarButton").val("1")
            $("#salvarButton").removeClass("d-none")
        }
        else if ($("#modificarButton").val() == "1"){
            $(".ver").removeClass("d-none")
            $(".editar").addClass("d-none")
            $("#modificarButton").removeClass("btn-outline-warning")
            $("#modificarButton").addClass("btn-warning")
            $("#modificarButton").text("Modificar")
            $("#modificarButton").val("0")
            $("#salvarButton").addClass("d-none")
        }
    }

    $(document).on("click", "#entidades button.detalhe", function sms() {

        let tr = $(this).closest('tr');
        idEntidade = tr.find($("[name=idEntidade]")).val();
        $("#idEntidade").text(idEntidade);
        $("#idEntidade").val(idEntidade);
        nif = tr.find($("[name=nif]")).val();
        $("#nifAux").text(nif);
        $("#nifAltera").val(nif);
        imagemGlobal = tr.find($("[name=imagem]")).val()
        $("#imagemAux").attr("src", imagemGlobal);
        designacaoGlobal = tr.find($("[name=designacao]")).val()
        $("#designacaoAux").text(designacaoGlobal);
        $("#designacaoAltera").val(designacaoGlobal);
        endereco = tr.find($("[name=provincia]")).val() +', '+ tr.find($("[name=municipio]")).val() +', '+ tr.find($("[name=bairro]")).val() +', '+ tr.find($("[name=endereco]")).val()
        $("#enderecoAux").text(endereco);
        $("#enderecoAlterar").val(endereco);
        $("#enderecoAlterar").text(endereco);
        siglaGlobal = tr.find($("[name=sigla]")).val();
        $("#siglaAux").text(siglaGlobal);
        $("#siglaAltera").val(siglaGlobal);
        tipoEntidade = tr.find($("[name=tipoEntidade]")).val();
        idTipoEntidade = tr.find($("[name=idTipoEntidade]")).val();
        $("#tipoEntidadeAux").text(tipoEntidade);
        $("#idTipoEntidadeAlterar").val(idTipoEntidade);
        $("#idTipoEntidadeAlterar").text(idTipoEntidade);
        alert(idTipoEntidade)
        idLocalidade = tr.find($("[name=idLocalidade]")).val();
        $("#idLocalidadeAlterar").val(idLocalidade);
        $("#enderecoAlterar").val(tr.find($("[name=endereco]")).val());
        $("#enderecoAlterar").text(tr.find($("[name=endereco]")).val());

    })

    function validNome(){
        info = document.forms["myForm"]["designacao"].value;
        component = $("#nomeValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSigla(){
        info = document.forms["myForm"]["sigla"].value;
        component = $("#siglaValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validNif(){
        info = document.forms["myForm"]["nif"].value;
        component = $("#nifValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validEndereco(){
        info = document.forms["myForm"]["endereco"].value;
        component = $("#enderecoValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSiglaAlterar(){
        info = document.forms["myFormEditar"]["sigla"].value;
        component = $("#siglaAltera")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDesignacaoAlterar(){
        info = document.forms["myFormEditar"]["designacao"].value;
        component = $("#designacaoAltera")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validNIFAlterar(){
        info = document.forms["myFormEditar"]["nif"].value;
        component = $("#nifAltera")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validateFormEditar() {

        const toastLiveExample = document.getElementById('liveToast')
        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        nome = document.forms["myFormEditar"]["designacao"].value;
        sigla = document.forms["myFormEditar"]["sigla"].value;
        nif = document.forms["myFormEditar"]["nif"].value;
        if (nome == ""
            || sigla == ""
        || nif == "") {
            validDesignacaoAlterar()
            validSiglaAlterar()
            validNIFAlterar()
            $("#liveToast").addClass("text-bg-danger")
            $("#liveToast").removeClass("text-bg-warning")
            $("#titlo-alerta").empty()
            $("#titlo-alerta").text("Erro ao criar conta estudante")
            $("#mensagem").empty()
            $("#mensagem").text("Verifique os campos se estão vazios ou devidamente preenchidos.")
            toastBootstrap.show()
            return false;
        }
        else {
            $("#liveToast").removeClass("text-bg-danger")
            $("#liveToast").removeClass("text-bg-warning")
            $("#titlo-alerta").empty()
            $("#titlo-alerta").text("MusalaLinkUp")
            $("#mensagem").empty()
            $("#mensagem").text("Instituição cadastrada com sucesso!")
            toastBootstrap.show()
            setTimeout(() => {
                document.myFormEditar.submit()
            }, 5000)
        }
        return false;
    }

    function validateForm() {

        const toastLiveExample = document.getElementById('liveToast')
        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        nome = document.forms["myForm"]["designacao"].value;
        sigla = document.forms["myForm"]["sigla"].value;
        nif = document.forms["myForm"]["nif"].value;
        endereco = document.forms["myForm"]["endereco"].value;
        if (nome == ""
        || sigla == ""
        || nif == ""
        || endereco =="") {
            validNome()
            validSigla()
            validNif()
            validEndereco()
            $("#liveToast").addClass("text-bg-danger")
            $("#liveToast").removeClass("text-bg-warning")
            $("#titlo-alerta").empty()
            $("#titlo-alerta").text("Erro ao criar conta estudante")
            $("#mensagem").empty()
            $("#mensagem").text("Verifique os campos se estão vazios ou devidamente preenchidos.")
            toastBootstrap.show()
            return false;
        }
        else {
            $("#liveToast").removeClass("text-bg-danger")
            $("#liveToast").removeClass("text-bg-warning")
            $("#titlo-alerta").empty()
            $("#titlo-alerta").text("MusalaLinkUp")
            $("#mensagem").empty()
            $("#mensagem").text("Instituição cadastrada com sucesso!")
            toastBootstrap.show()
            setTimeout(() => {
                document.myForm.submit()
            }, 5000)
        }
        return false;
    }


</script>
</body>
</html>