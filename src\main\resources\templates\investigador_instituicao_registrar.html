<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">

    <!-- Importação do Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
            crossorigin="anonymous"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style th:inline="text">
        body{
            font-family: Poppins;
        }
    </style>
    <title>Registra-se na Sakidila!</title>
</head>
<body class="row" style="background-color: ghostwhite;overflow-x: hidden">

<!--Menu da App-->
<div class="col-xl-2 col-lg-3 col-md-4">
<div class="d-flex flex-column flex-shrink-0 p-3 bg-white shadow-sm position-fixed" style="width: 280px; height: 100vh">
    <a href="/sakidila_ic/portal/" class="d-flex align-items-center mb-3 mb-md-0 me-md-auto link-body-emphasis text-decoration-none">
        <img src="/img/logotipo.jpg" alt="Bootstrap" width="100"> <span class="fw-bold text-dark text-center">Gestão Acadêmica</span></a>
    </a>
    <hr>
    <ul class="nav nav-pills flex-column mb-auto">
        <li class="nav-item">
            <a href="/sakidila_ic/portal/" class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Inicio
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/portal/modulo_gestao" class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Dashboard
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/entidade/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == adminPerfil}" class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Entidade
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/entidade_faculdade/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Faculdade
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/usuario/gestor_instituicao_registrar"
               th:if="${dadoConta.tipoConta.designacao == adminPerfil}"
               class="nav-link link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#table"></use></svg>
                Gestor de Instituição
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/centro/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link  active">
                <svg class="bi pe-none me-2" width="16" height="16"><use h:href="/sakidila_ic/centro/registrar"></use></svg>
                Centro de Investigação
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/centro/investigador/associar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil}"
               class="nav-link  link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use h:href="/sakidila_ic/centro/registrar"></use></svg>
                Centro de Investigação
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/portal/projecto/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil
               or dadoConta.tipoConta.designacao == gestorProjectoPerfil
               or dadoConta.tipoConta.designacao == investigadorPerfil}"
               class="nav-link">
                <svg class="bi pe-none me-2" width="16" height="16"><use href="/sakidila_ic/centro/registrar"></use></svg>
                Projectos
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/portal/projecto/pendentes/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link">
                <svg class="bi pe-none me-2" width="16" height="16"><use href="/sakidila_ic/centro/registrar"></use></svg>
                Projectos pendentes
            </a>
        </li>
        <li>
            <a href="#" class="nav-link link-body-emphasis"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}">
                <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#table"></use></svg>
                Estudante
            </a>
        </li>
        <li>
            <a href="#" class="nav-link link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#people-circle"></use></svg>
                Relatórios
            </a>
        </li>
    </ul>
    <hr>
    <div class="dropdown">
        <a href="#" class="d-flex align-items-center link-body-emphasis text-decoration-none dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
            <img th:if="${conta == userDefault}" th:src="@{/img/user.png}" alt="mdo" width="32" height="32" class="rounded-circle">
            <img th:if="${conta != null}" th:src="${dadoConta.foto}" alt="mdo" width="32" height="32" class="rounded-circle">
            <strong>[[${dadoConta.username}]]</strong>
        </a>
        <ul class="dropdown-menu text-small shadow">
            <li><a th:if="${conta == userDefault}" class="dropdown-item" href="/sakidila_ic/usuario/login">Entrar</a></li>
            <li><a th:if="${conta != null}" class="dropdown-item" href="">Meu Perfil</a></li>
            <li><a th:if="${conta != null}" class="dropdown-item" href="/logout">Sair</a></li>
        </ul>
    </div>
</div>
</div>

<div class="col-xl-10 col-lg-9 col-md-8">
<div class="d-flex flex-column container ">
    <div class="d-flex flex-row mt-4 justify-content-end">
        <a class=" me-3" style="text-decoration: none; color: #7c6aa2" href="/sakidila_ic/centro/registrar/1">Voltar</a>
        <a class="" style="text-decoration: none; color: #7c6aa2" href="/sakidila_ic/centro/investigador/associar/1">Associar investigador</a>
    </div>
    <h5 class="display-6 mt-5 mb-1 fw-bold mt-5" style="color: #AB81A3">Criar Conta <i class="fw-bold text-dark">Investigador!</i></h5>
    <hr>
    <form method="post" name="myForm" th:action="@{/sakidila_ic/usuario/investigador/registrar/salvar}" enctype="multipart/form-data" onsubmit="return validateForm()" required>
        <fieldset class="bg-white px-5 py-4 shadow-sm rounded-4 mb-3">
            <div class="row mb-3">
                <div class="col">
                    <div class="">
                        <h4 style="color: #AB81A3">Instituto</h4>
                        <h4 th:text="${instituicao.designacao}" style=""></h4>
                        <input type="hidden" style="display: none" name="entidade.id" th:value="${instituicao.id}">
                    </div>
                </div>
            </div>
            <legend>Dados Pessoais</legend>
            <div class="row mb-3">
                <div class="col">
                    <div class="">
                        <label class="form-label" id="nome">Nome</label>
                        <input type="text"  class="form-control" placeholder="" id="nomeValidate" onfocusout="validNome()" name="pessoa.nome" aria-label="Nome" aria-describedby="Nome">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher o campo com o seu nome.
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" id="apelido">Apelido</label>
                        <input type="text" class="form-control" id="apelidoValidate" onfocusout="validApelido()" name="pessoa.apelido" placeholder="" aria-label="Apelido" aria-describedby="apelido">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher o campo com o seu ultimo nome.
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label">Adicione sua foto de perfil</label>
                        <input class="form-control" type="file" id="foto" name="imagem">
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col">
                    <div class="">
                        <label class="form-label" id="dataNascimento">Data de Nascimento</label>
                        <input type="date" class="form-control" id="Nascimentovalidate" onfocusout="validDataNascimento()" name="pessoa.dataNascimento" placeholder="" aria-label="dataNascimento" aria-describedby="dataNascimento">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Especifique a sua data de nascimento
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label">Sexo</label>
                        <select class="form-select"  aria-label="Sexo" id="sexo" name="pessoa.sexo.id" aria-describedby="sexo">
                            <option th:each="sexo : ${sexos}" th:value="${sexo.id}"
                                    th:text="${sexo.designacao}"></option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" id="estadoCivil.id">Estado Civil</label>
                        <select class="form-select" placeholder="" name="pessoa.estadoCivil.id" aria-label="estadoCivil" id="estado" aria-describedby="estadoCivil">
                            <option th:each="estado : ${estados}" th:value="${estado.id}"
                                    th:text="${estado.designacao}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col">
                    <div class="">
                        <label class="form-label" id="identidade">Nº Identidade</label>
                        <input type="text" class="form-control" placeholder="" id="biValidate" onfocusout="validBi()" name="pessoa.bi" aria-label="identidade" aria-describedby="identidade">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher o campo com o número de identidade.
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" id="dataEmissao">Data de Emissão</label>
                        <input type="date" class="form-control"  aria-label="dataEmissao" id="emissaoValidate" onfocusout="validDataEmissao()" name="pessoa.dataEmissao" aria-describedby="dataEmissao">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Especifique a data de emissão.
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" id="dataValidade">Data de Validade</label>
                        <input type="date" class="form-control" placeholder="" id="validadeValidate" onfocusout="validDataValidade()" name="pessoa.dataValidade" aria-label="dataValidade" aria-describedby="dataValidade">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Especifique a data de validade.
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col">
                    <div class="">
                        <label class="form-label">Provincia</label>
                        <select class="form-select" id="provincia" placeholder="" aria-label="provincia" aria-describedby="provincia">
                            <option th:each="localidade : ${localidades}" th:value="${localidade.id}"
                                    th:text="${localidade.designacao}"></option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label">Municipio</label>
                        <select class="form-select"  aria-label="municipio"  id="municipio" aria-describedby="municipio"></select>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" >Bairro</label>
                        <select class="form-select" id="bairro" name="pessoa.localidade.id" placeholder="" aria-label="bairro" aria-describedby="bairro"></select>
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col">
                    <div class="">
                        <label class="form-label" id="tel">Contacto telefónico</label>
                        <input type="text" class="form-control" placeholder="" id="contactoValidate" onfocusout="validContacto()" name="pessoa.contacto" aria-label="tel" aria-describedby="tel">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher campo com o contacto telefônico.
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" id="email">Correio Electrônico</label>
                        <input type="email" class="form-control" id="emailValidate" onfocusout="validEmail()" name="pessoa.email"  aria-label="email" aria-describedby="email">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher campo com o email.
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="">
                        <label class="form-label" id="rua">Endereço</label>
                        <input type="text" class="form-control" placeholder="" id="enderecoValidate" onfocusout="validEndereco()" name="pessoa.endereco" aria-label="rua" aria-describedby="rua">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Especifique a rua, nº da casa.
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="bg-white px-5 py-4 shadow-sm rounded-4 mb-3">
            <legend>Dados Acadêmicos</legend>
            <div class="row mb-3">
                <div class="col-4">
                    <div class="">
                        <label class="form-label">Faculdade</label>
                        <select class="form-select" placeholder="" aria-label="faculdade" id="faculdade" name="faculdade.id" aria-describedby="faculdade">
                            <option th:each="item : ${faculdades}" th:value="${item.id}" th:text="${item.designacao}"></option>
                        </select>
                    </div>
                </div>
                <div class="col-4">
                    <div class="">
                        <label class="form-label" id="nivelAcademico">Nível Acadêmico</label>
                        <select class="form-select" placeholder="" aria-label="nivelAcademico" name="nivelAcademico.id" aria-describedby="nivelAcademico">
                            <option th:each="nivel : ${niveis}" th:value="${nivel.id}"
                                    th:text="${nivel.designacao}"></option>
                        </select>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset class="bg-white px-5 py-4 shadow-sm rounded-4 mb-3">
            <legend>Dados da Conta</legend>
            <div class="row mb-3">
                <div class="col-4">
                    <div class="">
                        <label class="form-label" id="usuario">Username</label>
                        <input type="text" class="form-control" placeholder="" name="username" onfocusout="validUsuario()" id="usuarioValidate" aria-label="usuario" aria-describedby="usuario">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher campo com o nome de usuario.
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="">
                        <label class="form-label" id="emailConta">E-mail</label>
                        <input type="email" class="form-control" placeholder="" id="emailLoginValidate" onfocusout="validEmailLogin()" name="email" aria-label="emailConta" aria-describedby="emailConta">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher campo com o email de login.
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="">
                        <label class="form-label" id="senha">Senha</label>
                        <input type="password" class="form-control" placeholder="" id="senhaValidate" onfocusout="validSenha()" name="senha" aria-label="senha" aria-describedby="senha">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher campo com a nova senha da conta.
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <!--<div class="col-4">
                    <div class="input-group ">
                        <span class="input-group-text" id="tipoConta">Tipo de Conta</span>
                        <select class="form-select" placeholder="" aria-label="tipoConta" aria-describedby="tipoConta"></select>
                    </div>
                </div>-->

                <div class="col-4">
                    <div>
                        <label class="form-label">Confirmar Senha</label>
                        <input type="password" class="form-control" placeholder="" id="senhaconfirmaValidate" onfocusout="validSenhaConfirma()" name="senha1" aria-label="senha1" aria-describedby="senha1">
                        <div class="valid-feedback">

                        </div>
                        <div class="invalid-feedback">
                            Preencher campo com mesma senha para confirmar.
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <div class="col-12 d-flex flex-row justify-content-end">
            <button class="btn btn-warning mb-3 me-2 px-3 shadow-sm rounded-4" type="submit">Limpar</button>
            <button class="btn mb-3 shadow-sm rounded-4 text-white" type="submit" style="background-color: #AB81A3">Registrar</button>
        </div>
    </form>
</div>
</div>
<div class="toast-container position-fixed bottom-0 end-0 p-3 "  data-bs-autohide="false">
    <div id="liveToast" class="toast  border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto" id="titlo-alerta">MusalaLinkUp</strong>
            <small class="text-body-secondary">Agora</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="mensagem">
            Registre os investigadores da instituição e associe posteriormente a um centro especifico...
        </div>
    </div>
</div>
<script>
    window.onload = (event) => {
        getMunicipios();

        getFaculdades();
        getBairros();

        const toastLiveExample = document.getElementById('liveToast')

        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        setTimeout(() => {
            toastBootstrap.show()
        },1000)
    };
    $(document).ready(function () {
        $("#provincia").change(function () {
            $("#municipio").empty();
            getMunicipios();
        })

        $("#municipio").change(function () {
            $("#bairro").empty();
            getBairros();
        })

        $("#instituto").change(function () {
            $("#faculdade").empty();
            getFaculdades();
        })
    })

    function getFaculdades() {
        institutoId = $("#instituto").val();
        url = "http://localhost:8080/entidadeFaculdadeAjax/" + institutoId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                faculdades = $("#faculdade")
                $.each(responseJson, function (index, entidadeFaculdade) {
                    $("<option>")
                        .val(entidadeFaculdade.id.faculdade.id)
                        .text(entidadeFaculdade.id.faculdade.designacao)
                        .appendTo(faculdades);

                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }

    function getMunicipios() {
        provinciaId = $("#provincia").val();
        url = "http://localhost:8080/localidadesAjax/" + provinciaId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                municipios = $("#municipio")
                $.each(responseJson, function (index, localidade) {
                    $("<option>")
                        .val(localidade.id)
                        .text(localidade.designacao)
                        .appendTo(municipios);

                })
                getBairros()
            })
            .fail(function () {
            })
            .always(function () {
            });
    }


    function getBairros() {
        municipioId = $("#municipio").val();
        url = "http://localhost:8080/localidadesAjax/" + municipioId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                bairros = $("#bairro")
                $.each(responseJson, function (index, localidade) {
                    $("<option>")
                        .val(localidade.id)
                        .text(localidade.designacao)
                        .appendTo(bairros);

                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }

    function validNome(){
        info = document.forms["myForm"]["pessoa.nome"].value;
        component = $("#nomeValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validApelido(){
        info = document.forms["myForm"]["pessoa.apelido"].value;
        component = $("#apelidoValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataNascimento(){
        info = document.forms["myForm"]["pessoa.dataNascimento"].value;
        component = $("#dataNascimento")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validBi(){
        info = document.forms["myForm"]["pessoa.bi"].value;
        component = $("#identidade")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataEmissao(){
        info = document.forms["myForm"]["pessoa.dataEmissao"].value;
        component = $("#dataEmissao")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataValidade(){
        info = document.forms["myForm"]["pessoa.dataValidade"].value;
        component = $("#dataValidade")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validContacto(){
        info = document.forms["myForm"]["pessoa.contacto"].value;
        component = $("#contactoValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validEmail(){
        info = document.forms["myForm"]["pessoa.email"].value;
        component = $("#emailValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validEndereco(){
        info = document.forms["myForm"]["pessoa.endereco"].value;
        component = $("#enderecoValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validUsuario(){
        info = document.forms["myForm"]["username"].value;
        component = $("#usuarioValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validEmailLogin(){
        info = document.forms["myForm"]["email"].value;
        component = $("#emailLoginValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenha(){
        info = document.forms["myForm"]["senha"].value;
        component = $("#senhaValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenhaConfirma(){
        info = document.forms["myForm"]["senha1"].value;
        component = $("#senhaconfirmaValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    var dadosGlobais;

    function fazerRequisicao() {
        email = document.forms["myForm"]["username"].value;

        url = "http://localhost:8080/usuarioAjax/"+email;
        // Retorna uma Promise que resolve quando a requisição é bem-sucedida
        return new Promise((resolve, reject) => {
            // Faz a requisição usando a função fetch
            fetch(url)
                .then(response => {

                    // Verifica se a resposta da requisição é bem-sucedida (status 2xx)
                    if (!response.ok) {
                        throw new Error('Erro na requisição');
                    }
                    // Parseia a resposta como JSON
                    return response.json();
                })
                .then(data => {
                    // Armazena os dados na variável global
                    dadosGlobais = data;
                    // Resolve a Promise
                    submitValidation = true
                    resolve(submitValidation);
                })
                .catch(error => {
                    // Rejeita a Promise em caso de erro
                    reject(error);
                });
        });
    }

    function validateForm() {
        nome = document.forms["myForm"]["pessoa.nome"].value;
        apelido = document.forms["myForm"]["pessoa.apelido"].value;
        dtNascimento = document.forms["myForm"]["pessoa.dataNascimento"].value;
        bi = document.forms["myForm"]["pessoa.bi"].value;
        dtEmissao = document.forms["myForm"]["pessoa.dataEmissao"].value;
        dtValidade = document.forms["myForm"]["pessoa.dataValidade"].value;
        contacto = document.forms["myForm"]["pessoa.contacto"].value;
        email = document.forms["myForm"]["pessoa.email"].value;
        endereco = document.forms["myForm"]["pessoa.endereco"].value;
        username = document.forms["myForm"]["username"].value;
        email1 = document.forms["myForm"]["email"].value;
        senha = document.forms["myForm"]["senha"].value;
        senhaConfirmar = document.forms["myForm"]["senha1"].value;
        const toastLiveExample = document.getElementById('liveToast')
        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        let submit = false;
        fazerRequisicao().then((submitValidation) => {
            if (nome == ""
                || apelido == ""
                || dtNascimento == ""
                || bi == ""
                || dtEmissao == ""
                || dtValidade == ""
                || contacto == ""
                || email == ""
                || endereco == ""
                || username == ""
                || email1 == ""
                || senha == ""
                || senhaConfirmar == ""
            || senha != senhaConfirmar) {
                validNome()
                validApelido()
                validBi()
                validContacto()
                validDataEmissao()
                validDataNascimento()
                validDataValidade()
                validEmail()
                validEmailLogin()
                validEndereco()
                validSenha()
                validSenhaConfirma()
                validUsuario()
                if(senha != senhaConfirmar){

                    $("#liveToast").addClass("text-bg-danger")
                    $("#titlo-alerta").empty()
                    $("#titlo-alerta").text("Erro ao criar conta estudante")
                    $("#mensagem").empty()
                    $("#mensagem").text("A senha deve ser a mesma no campo confirmar senha")
                    toastBootstrap.show()
                    submit = false;
                }
                setTimeout(() => {
                    const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
                    $("#liveToast").addClass("text-bg-danger")
                    $("#liveToast").removeClass("text-bg-warning")
                    $("#titlo-alerta").empty()
                    $("#titlo-alerta").text("Erro ao criar conta investigador")
                    $("#mensagem").empty()
                    $("#mensagem").text("Verifique os campos se estão vazios ou devidamente preenchidos.")
                    toastBootstrap.show()
                    submit = false;
                }, 4000)
                return false;
            }
            else if(dadosGlobais)
            {
                submit = false;
                const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
                $("#liveToast").addClass("text-bg-warning")
                $("#liveToast").removeClass("text-bg-danger")
                $("#titlo-alerta").empty()
                $("#titlo-alerta").text("Erro ao criar conta investigador")
                $("#mensagem").empty()
                $("#mensagem").text("Já existe uma conta com este username")
                toastBootstrap.show()
            }
            else {
                submit = submitValidation;
                $("#liveToast").removeClass("text-bg-danger")
                $("#liveToast").removeClass("text-bg-warning")
                $("#titlo-alerta").empty()
                $("#titlo-alerta").text("MusalaLinkUp")
                $("#mensagem").empty()
                $("#mensagem").text("Conta investigador registrada com sucesso!")
                toastBootstrap.show()
                setTimeout(() => {
                    document.myForm.submit()
                }, 5000)
            }
        })
        return submit;
    }
</script>
</body>
</html>