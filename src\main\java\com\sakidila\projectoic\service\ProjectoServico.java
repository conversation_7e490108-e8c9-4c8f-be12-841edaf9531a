package com.sakidila.projectoic.service;

import com.sakidila.projectoic.entity.*;
import com.sakidila.projectoic.repository.ProjectoRespository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class ProjectoServico {

    @Autowired
    private ProjectoRespository respository;

    @Autowired
    private CentroServico centroServico;

    @Autowired
    private EntidadeFaculdadeService entidadeFaculdadeService;

    @Autowired
    private FaculdadeService faculdadeService;

    public Projecto salvarProjecto(Projecto projecto){
        return respository.save(projecto);
    }

    public List<Projecto> encontrarTodosProjectosPeloIdInstituicao(Long idInstituto, Conta conta){
        List<EntidadeFaculdade> lista = entidadeFaculdadeService.ListaEntidadeFaculdadePelaInstituto(idInstituto);
        List<Faculdade> faculdades = faculdadeService.encontrarTodasFaculdadePeloInstituto(lista);
        List<Centro> listaCentros = centroServico.encontrarTodosCentrosPelaIdInstituicao(faculdades,conta);
        List<Projecto> projectos = new ArrayList<>();
        for (Centro centro: listaCentros) {
            projectos.addAll(centro.getProjectos());
        }
        return projectos;
    }

    public List<Projecto> encontrarProjectosEstudantesPelaInstituicao(Long idInstituicao){
        return respository.findAllByEhAprovadoIsFalseAndCentroIsNullAndGestorEntidadeId(idInstituicao);
    }


    public List<Projecto> encontrarProjectosPeloGestor(Long idGestor){
        return respository.findAllByGestorId(idGestor);
    }

    public List<Projecto> encontrarProjectosPeloIdCentro(Long idCentro){
        return respository.findAllByCentroId(idCentro);
    }

    public List<Projecto> EncontrarProjectoPeloIdParticipante(Long id,List<ProjectoConta> projectoContas){
        List<Projecto> lista = new ArrayList<>();

        for (ProjectoConta projectoConta:projectoContas) {
            for (Tarefa tarefa: projectoConta.getChave().getProjecto().getTarefas()) {
                if(tarefa.getInvestigador().getId() == id) {
                    lista.add(projectoConta.getChave().getProjecto());
                    break;
                }
            }
        }
        return lista;
    }

    public List<Projecto> encontrarTodosProjectosPeloIdGestor(List<Projecto> projectos, Long idGestor){
        List<Projecto> lista = new ArrayList<>();
        if(projectos != null){
            for (Projecto projecto: projectos) {
                if(projecto.getGestor().getId() == idGestor){
                    lista.add(projecto);
                }
            }
            return lista;
        }
        return null;
    }

    public Page<Projecto> encontrarPaginaPeloIdInstituto(Long id,Long ordem,Long investimento,int pagina, int tamanho) {
        Pageable pageable = PageRequest.of(pagina - 1, tamanho);
        if(id != 0 && ordem == 0 && investimento == 0)
            return  respository.findAllByGestorEntidadeIdAndDisabledFalseAndEhAprovadoIsTrueOrderByDesignacaoAsc(id,pageable);
        else if(id != 0 && ordem != 0 && investimento == 0) {
            if(ordem == 2)
                return respository.findAllByGestorEntidadeIdAndDisabledFalseAndEhAprovadoIsTrueOrderByDesignacaoDesc(id, pageable);
            if(ordem == 1)
                return respository.findAllByGestorEntidadeIdAndDisabledFalseAndEhAprovadoIsTrueOrderByDataCriacaoDesc(id, pageable);
        }
        else if(id != 0 && ordem != 0 && investimento != 0) {
            if(ordem == 2 && investimento == 1)
                return respository.findAllByGestorEntidadeIdAndDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsTrueOrderByDesignacaoDesc(id, pageable);
            else if(ordem == 2 && investimento == 2)
                return respository.findAllByGestorEntidadeIdAndDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsFalseOrderByDesignacaoDesc(id,pageable);
            else if(ordem == 1 && investimento == 1)
                return respository.findAllByGestorEntidadeIdAndDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsTrueOrderByDataCriacaoDesc(id, pageable);
            else if(ordem == 1 && investimento == 2)
                return respository.findAllByGestorEntidadeIdAndDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsFalseOrderByDataCriacaoDesc(id, pageable);
        }
        else if(id == 0 && ordem != 0 && investimento != 0) {
            if(ordem == 2 && investimento == 1)
                return respository.findAllByDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsTrueOrderByDesignacaoDesc(pageable);
            else if(ordem == 2 && investimento == 2)
                return respository.findAllByDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsFalseOrderByDesignacaoDesc(pageable);
            else if(ordem == 1 && investimento == 1)
                return respository.findAllByDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsTrueOrderByDataCriacaoDesc(pageable);
            else if(ordem == 1 && investimento == 2)
                return respository.findAllByDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsFalseOrderByDataCriacaoDesc(pageable);
        }
        else if(id != 0 && ordem == 0 && investimento != 0) {
            if(investimento == 1)
                return respository.findAllByDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsTrueOrderByDesignacaoAsc(pageable);
            if(investimento == 2)
                return respository.findAllByDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsFalseOrderByDesignacaoAsc(pageable);
        }
        else if(id == 0 && ordem == 0 && investimento != 0) {
            if(investimento == 1)
                return respository.findAllByDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsTrueOrderByDesignacaoAsc(pageable);
            if(investimento == 2)
                return respository.findAllByDisabledFalseAndEhAprovadoIsTrueAndEhDeInvestimentoIsTrueOrderByDesignacaoDesc(pageable);
        }
        else if(id == 0 && ordem != 0 && investimento == 0) {
            if(ordem == 1)
                return respository.findAllByDisabledFalseAndEhAprovadoIsTrueOrderByDataCriacaoDesc(pageable);
            if(ordem == 2)
                return respository.findAllByDisabledFalseAndEhAprovadoIsTrueOrderByDesignacaoDesc(pageable);
        }
            return respository.findAllByDisabledIsFalseAndEhAprovadoIsTrueOrderByDesignacaoAsc(pageable);
    }

    public List<Projecto> listaProjectosRecentes(){
        return respository.findTop4ByOrderByCreatedAtDesc();
    }

    public List<Projecto> listaProjectostop(){
        return respository.findTop4ByEhDeInvestimentoIsTrueAndEhInvestidoIsFalseOrderByOrcamentoDesc();
    }

    public Projecto findById(Long id){
        return respository.findById(id).get();
    }

    public void tornarInvestido(Long idProjecto){
        respository.fecharInvestimento(idProjecto);
    }

    public Page<?> convertListToPage(int page, int size,List<?> lista) {

        Pageable pageRequest = PageRequest.of(page-1, size);

        int start = (int) pageRequest.getOffset();
        int end = Math.min((start + pageRequest.getPageSize()), lista.size());

        return new PageImpl<>(lista.subList(start, end), pageRequest, lista.size());
    }

    public Projecto desativar(Long idProjecto) {
        Projecto projecto = respository.findById(idProjecto).get();
        if(projecto!=null)
            respository.disabledByIdProjecto(idProjecto);
        return projecto;
    }
}
