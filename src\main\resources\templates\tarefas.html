<!DOCTYPE html>
<html lang="en">
<head>

    <!-- Importação do Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
            crossorigin="anonymous"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
    <style th:inline="text">
        body{

            font-family: Poppins;
        }


    </style>
    <title>Registra-se na Sakidila!</title>
</head>
<body class="bg-light row" style="overflow-x: hidden">

<!--Menu da App-->
<div class="col-xl-2 col-lg-3 col-md-4">
<div class="d-flex flex-column flex-shrink-0 p-3 bg-white shadow-sm position-fixed" style="width: 280px; height: 100vh">
    <a href="/sakidila_ic/portal/" class="d-flex align-items-center mb-3 mb-md-0 me-md-auto link-body-emphasis text-decoration-none">
        <img src="/img/logotipo.jpg" alt="Bootstrap" width="100"> <span class="fw-bold text-dark text-center">Gestão Acadêmica</span></a>
    </a>
    <hr>
    <ul class="nav nav-pills flex-column mb-auto">
        <li class="nav-item">
            <a href="/sakidila_ic/portal/" class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Inicio
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/portal/modulo_gestao" class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Dashboard
            </a>
        </li>
        <li class="nav-item" th:if="${dadoConta.tipoConta != null}">
            <a href="/sakidila_ic/entidade/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == adminPerfil}" class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Entidade
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/entidade_faculdade/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Faculdade
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/usuario/gestor_instituicao_registrar"
               th:if="${dadoConta.tipoConta.designacao == adminPerfil}"
               class="nav-link link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#table"></use></svg>
                Gestor de Instituição
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/centro/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link  link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use h:href="/sakidila_ic/centro/registrar"></use></svg>
                Centro de Investigação
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/centro/investigador/associar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil}"
               class="nav-link  link-body-emphasis ">
                <svg class="bi pe-none me-2" width="16" height="16"><use h:href="/sakidila_ic/centro/registrar"></use></svg>
                Centro de Investigação
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/portal/projecto/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil
               or dadoConta.tipoConta.designacao == gestorProjectoPerfil
               or dadoConta.tipoConta.designacao == investigadorPerfil
               or dadoConta.tipoConta.designacao == estudantePerfil}"
               class="nav-link active">
                <svg class="bi pe-none me-2" width="16" height="16"><use href="/sakidila_ic/centro/registrar"></use></svg>
                Projectos
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/portal/projecto/pendentes/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link">
                <svg class="bi pe-none me-2" width="16" height="16"><use href="/sakidila_ic/centro/registrar"></use></svg>
                Projectos pendentes
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/portal/projecto/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == empresaPerfil}"
               class="nav-link active" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Meus investimentos
            </a>
        </li>
        <li>
            <a href="#" class="nav-link link-body-emphasis"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}">
                <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#table"></use></svg>
                Estudante
            </a>
        </li>
        <li>
            <a href="#" class="nav-link link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#people-circle"></use></svg>
                Relatórios
            </a>
        </li>
    </ul>
    <hr>
    <div class="dropdown">
        <a href="#" class="d-flex align-items-center link-body-emphasis text-decoration-none dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
            <img th:if="${conta == userDefault}" th:src="@{/img/user.png}" alt="mdo" width="32" height="32" class="rounded-circle">
            <img th:if="${conta != null}" th:src="${dadoConta.foto}" alt="mdo" width="32" height="32" class="rounded-circle">
            <strong>[[${dadoConta.username}]]</strong>
        </a>
        <ul class="dropdown-menu text-small shadow">
            <li><a th:if="${conta == userDefault}" class="dropdown-item" href="/sakidila_ic/usuario/login">Entrar</a></li>
            <li><a th:if="${conta != null}" class="dropdown-item" href="">Meu Perfil</a></li>
            <li><a th:if="${conta != null}" class="dropdown-item" href="/logout">Sair</a></li>
        </ul>
    </div>
</div>
</div>

<div class="col-xl-10 col-lg-9 col-md-8">
<div class="container">
    <div class="d-flex flex-row mt-4 justify-content-end">
        <a class=" me-3"  style="text-decoration: none; color: #7c6aa2" onclick="history.go(-1)">Voltar</a>
        <button th:if="${dadoConta.tipoConta.designacao == gestorProjectoPerfil or dadoConta.tipoConta.designacao == investigadorIndependentePerfil}" th:onclick="carregar()" class="p-0"  style="border:none;background-color:transparent;text-decoration: none; color: #7c6aa2" data-bs-toggle="modal" data-bs-target="#exampleModal">Nova tarefa</button>

    </div>
    <h5 class="display-6 mt-1 mb-1 fw-bold" style="color: rgba(158,158,151)">Monitorização de Tarefas <i class="fw-bold text-dark"></i></h5>
    <hr>
    <fieldset class="bg-white px-5 py-4 shadow-sm rounded mb-3" style="height: 52%">
        <table class="table table-hover">
            <tr>
                <th scope="col">Descrição</th>
                <th scope="col">fase</th>
                <th scope="col">Estado</th>
                <th scope="col" th:if="${dadoConta.tipoConta.designacao != empresaPerfil or dadoConta.tipoConta.designacao != investigadorIndependentePerfil}">Investigador</th>
                <th scope="col" th:if="${dadoConta.tipoConta.designacao != empresaPerfil}">Data Inicial</th>
                <th scope="col" th:if="${dadoConta.tipoConta.designacao != empresaPerfil}">Data Final</th>
                <th scope="col" th:if="${dadoConta.tipoConta.designacao != empresaPerfil}">Editar</th>
                <th scope="col" th:if="${dadoConta.tipoConta.designacao != empresaPerfil}">Remover</th>
                <th scope="col" th:if="${dadoConta.tipoConta.designacao != empresaPerfil}">Validar</th>
            </tr>
            <tr th:each="item : ${tarefas}" id="tarefas">
                <td th:text="${item.designacao}"></td>
                <td th:text="${item.fase.designacao}"></td>
                <td th:text="${item.estado.designacao}"></td>
                <td th:if="${dadoConta.tipoConta.designacao != empresaPerfil or dadoConta.tipoConta.designacao != investigadorIndependentePerfil}" th:text="${item.investigador.pessoa.nome+' '+item.investigador.pessoa.apelido}"></td>
                <td th:if="${dadoConta.tipoConta.designacao != empresaPerfil}" th:text="${item.dataInicio}"></td>
                <td th:if="${dadoConta.tipoConta.designacao != empresaPerfil}" th:text="${item.dataFim}"></td>
                <td th:if="${dadoConta.tipoConta.designacao != empresaPerfil}">

                    <input type="hidden" name="idTarefa" th:value="${item.idTarefa}">
                    <input type="hidden" name="descricao" th:value="${item.designacao}">
                    <input type="hidden" name="idProjecto" th:value="${item.projecto.id}">
                    <input type="hidden" name="idMetodologia" th:value="${item.fase.fasePai.id}">
                    <input type="hidden" name="idInvestidor" th:value="${item.investigador.id}">
                    <input type="hidden" name="dataInicio" th:value="${item.dataInicio}">
                    <input type="hidden" name="dataFim" th:value="${item.dataFim}">

                    <button type="button" class="btn btn-primary btn-sm detalhe" data-bs-target="#modalVer" data-bs-toggle="modal">Ver</button>
                </td>
                <td th:if="${dadoConta.tipoConta.designacao != empresaPerfil}"><a type="button" class="btn btn-danger btn-sm shadow-sm" th:href="@{/sakidila_ic/portal/projecto/tarefa/delete/{id}(id=${item.idTarefa})}">Remover</a></td>
                <td th:if="${dadoConta.id == item.projecto.gestor.id and item.estado.designacao == 'Em progresso'}"><a type="button" class="btn btn-success btn-sm shadow-sm" th:href="@{'/sakidila_ic/portal/projecto/tarefa/concluido/' + ${item.idTarefa} + '/'+1}">Concluído?</a></td>
                <td th:if="${dadoConta.id == item.projecto.gestor.id and item.estado.designacao == 'Pendente'}"><a type="button" class="btn btn-warning btn-sm shadow-sm" th:href="@{'/sakidila_ic/portal/projecto/tarefa/concluido/' + ${item.idTarefa} + '/'+2}">Iniciar?</a></td>
            </tr>
        </table>
        <div class="d-flex justify-content-center">
            <nav class="Page navigation example" th:if="${totalPaginas > 1}">
                <ul class="pagination">
                    <li class="page-item"><a class="page-link">Total de projectos: [[${totalItems}]]</a></li>
                    <div th:each="i : ${#numbers.sequence(1,totalPaginas)}">
                        <li class="page-item">
                            <a class="page-link" th:if="${paginaActual != i}"
                               th:href="@{'/sakidila_ic/portal/projecto/tarefa/'+ ${idProjecto+'/'+i}}">[[${i}]]</a>
                            <a class="page-link" th:unless="${paginaActual != i}">[[${i}]]</a> &nbsp; &nbsp;
                        </li>
                    </div>
                    <li class="page-item">
                        <a class="page-link" th:if="${paginaActual < totalPaginas}"
                           th:href="@{'/sakidila_ic/portal/projecto/tarefa/'+${idProjecto+'/'+paginaActual + 1}}">Próximo</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" th:if="${paginaActual < totalPaginas}"
                           th:href="@{'/sakidila_ic/portal/projecto/tarefa/'idProjecto+'/'+${paginaActual + 1}}">Ultimo</a>
                        <a class="page-link" th:unless="${paginaActual < totalPaginas}">Ultimo</a>
                    </li>
                </ul>
            </nav>
        </div>
    </fieldset>
    <form method="post" th:action="@{/sakidila_ic/portal/projecto/tarefa/criar}" >
    <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">

            <div class="modal-dialog modal-dialog-scrollable modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="exampleModalLabel">Nova tarefa</h1>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">

                        <div class="mb-3" th:if="${dadoConta.tipoConta.designacao == investigadorIndependentePerfil}">
                            <h5 for="exampleFormControlInput1" class="form-label">Atribuir tarefa ao <i class="fw-bolder" style="color: #AB81A3">[[${dadoConta.pessoa.nome +' '+dadoConta.pessoa.apelido}]]</i></h5>
                            <input type="hidden" name="investigador.id" th:value="${dadoConta.id}">
                        </div>

                        <div class="mb-3">
                            <label for="exampleFormControlInput1" class="form-label fw-bolder">Descrição</label>
                            <input type="text" class="form-control" id="exampleFormControlInput1" name="designacao" placeholder="Nova tarefa">
                        </div>

                        <div class="mb-3">
                            <label for="exampleFormControlInput1" class="form-label fw-bolder">Especifique o projecto</label>
                            <select class="form-select" id="projecto" name="projecto.id">
                                <option th:each="item : ${projecto}" th:text="${item.designacao}" th:value="${item.id}"></option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="exampleFormControlInput1" id="metodologia" class="form-label fw-bolder">Metodologia •</label>
                            <select class="form-select" id="fases" name="fase.id">
                                <option>Selecione a fase</option>
                            </select>
                        </div>
                        <div class="mb-3" th:if="${dadoConta.tipoConta.designacao != investigadorIndependentePerfil}">
                            <label for="exampleFormControlInput1" class="form-label fw-bolder">Atribuir tarefa</label>
                            <select class="form-select" id="investigadores" name="investigador.id">
                                <option>Selecione o investigador</option>
                            </select>
                        </div>


                        <div class="mb-3 row">
                            <div class="col">
                                <label for="exampleFormControlInput1" class="form-label fw-bolder">Começa em</label>
                                <input type="date" class="form-control" id="dataInicio" name="dataInicio">
                            </div>
                            <div class="col">
                                <label for="exampleFormControlInput1" class="form-label fw-bolder">Termina em</label>
                                <input type="date" class="form-control" id="datafim" name="dataFim">
                            </div>
                        </div>

                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                        <input type="submit"  class="btn btn-primary" value="Criar">
                    </div>
                </div>
            </div>

    </div>
    </form>
</div>
</div>


<!--Modal de ver ou editar um projecto-->
<form method="post" name="myFormEditar" th:action="@{/sakidila_ic/portal/projecto/tarefa/criar}">
    <div class="modal fade" id="modalVer" tabindex="-1" aria-labelledby="ModalLabelVer" aria-hidden="true">
        <div class="modal-dialog modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="ModalLabelVer">Detalhes da Entidade</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="idTarefaAltera" name="idTarefa">
                    <div class="mb-3" th:if="${dadoConta.tipoConta.designacao == investigadorIndependentePerfil}">
                        <h5 for="exampleFormControlInput1" class="form-label">Atribuir tarefa ao <i class="fw-bolder" style="color: #AB81A3">[[${dadoConta.pessoa.nome +' '+dadoConta.pessoa.apelido}]]</i></h5>
                        <input type="hidden" name="investigador.id" id="idInvestidorAltera" th:value="${dadoConta.id}">
                    </div>

                    <div class="mb-3">
                        <label for="exampleFormControlInput1" class="form-label fw-bolder">Descrição</label>
                        <input type="text" class="form-control" id="designacaoAltera" name="designacao" placeholder="Nova tarefa">
                    </div>

                    <div class="mb-3">
                        <label for="exampleFormControlInput1" class="form-label fw-bolder">Especifique o projecto</label>
                        <select class="form-select" id="projectoAux" name="projecto.id">
                            <option th:each="item : ${projecto}" th:text="${item.designacao}" th:value="${item.id}"></option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="exampleFormControlInput1" id="metodologiaAltera" class="form-label fw-bolder">Metodologia •</label>
                        <select class="form-select" id="fasesAltera" name="fase.id">
                            <option>Selecione a fase</option>
                        </select>
                    </div>
                    <div class="mb-3" th:if="${dadoConta.tipoConta.designacao != investigadorIndependentePerfil}">
                        <label for="exampleFormControlInput1" class="form-label fw-bolder">Atribuir tarefa</label>
                        <select class="form-select" id="investigadoresAltera" name="investigador.id">
                            <option>Selecione o investigador</option>
                        </select>
                    </div>


                    <div class="mb-3 row">
                        <div class="col">
                            <label for="exampleFormControlInput1" class="form-label fw-bolder">Começa em</label>
                            <input type="date" class="form-control" id="dataInicioAltera" name="dataInicio">
                        </div>
                        <div class="col">
                            <label for="exampleFormControlInput1" class="form-label fw-bolder">Termina em</label>
                            <input type="date" class="form-control" id="datafimAltera" name="dataFim">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                    <button type="submit" class="btn btn-primary" id="salvarButton">Guardar</button>
                </div>
            </div>
        </div>
    </div>
</form>
<script th:inline="javascript">
    window.onload = (event) => {
            $("#metodologia").empty();
            $("#fases").empty();
            $("#fasesAltera").empty();
            $("#investigadores").empty();
            $("#investigadoresAltera").empty();
            getMetodologia();
            getFases();
            getInvestigadores()
    };
    $(document).ready(function () {
        $("#projecto").change(function () {
            $("#metodologia").empty();
            $("#fases").empty();
            $("#fasesAltera").empty();
            $("#investigadores").empty();
            $("#investigadoresAltera").empty();
            getMetodologia();
            getFases();
            getInvestigadores()
        })


        /* $("#enviar").click(function (e){
             e.preventDefault();
             alert("OK")
             saveTarefa()
         })*/
    })

    function getFases() {
        projectoId = $("#projecto").val();
        url = "http://localhost:8080/projectoAjax/" + projectoId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                fases = $("#fases")
                fasesAltera = $("#fasesAltera")
                $.each(responseJson, function (index, metodologia) {
                    $("<option>")
                        .val(metodologia.id)
                        .text(metodologia.designacao)
                        .appendTo(fases)

                    $("<option>")
                        .val(metodologia.id)
                        .text(metodologia.designacao)
                        .appendTo(fasesAltera)
                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }

    function getMetodologia() {
        projectoId = $("#projecto").val();
        url = "http://localhost:8080/projectoAjax/metodologia/" + projectoId;
        $.ajax({method: "GET", url,
            success:  function (data){
                $("#metodologia").text(data.designacao)
                    .val(data.id)

                $("#metodologiaAltera").text(data.designacao)
                    .val(data.id)
            }});

    }

    /*function saveTarefa(){
        url = "http://localhost:8080/sakidila_ic/portal/projecto/tarefa/criar";
        formData = $("#formulario").serialize();
        $.ajax({
            type: 'post',
            url: url,
            data: formData,
            success: function (data) {
                console.log('Submission was successful.');
                console.log(data);
            },
            error: function (data) {
                console.log('An error occurred.');
                console.log(data);
            },
        });
    }*/

    function getInvestigadores() {
        projectoId = $("#projecto").val();
        url = "http://localhost:8080/projectoAjax/contas/" + projectoId;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                investigadoresSelect = $("#investigadores")
                investigadoresAltera = $("#investigadoresAltera")
                $.each(responseJson, function (index, investigadores) {
                    $("<option>")
                        .text(investigadores.pessoa.nome+" "+investigadores.pessoa.apelido)
                        .val(investigadores.id)
                        .appendTo(investigadoresSelect)

                    $("<option>")
                        .text(investigadores.pessoa.nome+" "+investigadores.pessoa.apelido)
                        .val(investigadores.id)
                        .appendTo(investigadoresAltera)
                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }

    $(document).on("click", "#tarefas button.detalhe", function sms() {

        let tr = $(this).closest('tr');
        descricao = tr.find($("[name=descricao]")).val();
        $("#designacaoAltera").text(descricao);
        $("#designacaoAltera").val(descricao);

        dataInicio = tr.find($("[name=dataInicio]")).val();
        $("#dataInicioAltera").text(dataInicio);
        $("#dataInicioAltera").val(dataInicio);

        dataFim = tr.find($("[name=dataFim]")).val();
        $("#dataInicioAltera").text(dataFim);
        $("#datafimAltera").val(dataFim);

        idTarefa = tr.find($("[name=idTarefa]")).val();
        $("#idTarefaAltera").val(idTarefa);

    })
</script>
</body>
</html>