<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">

    <!-- Importação do Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
            crossorigin="anonymous"></script>
    <style th:inline="text">
        body{

            font-family: Poppins;
        }


    </style>
    <title>Registra-se na Sakidila!</title>
</head>
<body class="bg-light d-flex flex-row">

<!--Menu da App-->
<div class="d-flex flex-column flex-shrink-0 p-3 bg-white shadow-sm" style="width: 280px; height: 950px">
    <a href="/sakidila_ic/portal/" class="d-flex align-items-center mb-3 mb-md-0 me-md-auto link-body-emphasis text-decoration-none">
        <img src="/img/logotipo.jpg" alt="Bootstrap" width="100"> <span class="fw-bold text-dark text-center">Gestão Acadêmica</span></a>
    </a>
    <hr>
    <ul class="nav nav-pills flex-column mb-auto">
        <li class="nav-item">
            <a href="/sakidila_ic/portal/" class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Inicio
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/entidade/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == adminPerfil}" class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Entidade
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/entidade_faculdade/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Faculdade
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/usuario/gestor_instituicao_registrar"
               th:if="${dadoConta.tipoConta.designacao == adminPerfil}"
               class="nav-link link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#table"></use></svg>
                Gestor de Instituição
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/centro/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link  link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use h:href="/sakidila_ic/centro/registrar/1"></use></svg>
                Centro de Investigação
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/centro/investigador/associar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil}"
               class="nav-link  link-body-emphasis ">
                <svg class="bi pe-none me-2" width="16" height="16"><use h:href="/sakidila_ic/centro/registrar"></use></svg>
                Centro de Investigação
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/portal/projecto/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil
               or dadoConta.tipoConta.designacao == gestorProjectoPerfil
               or dadoConta.tipoConta.designacao == investigadorPerfil
               or dadoConta.tipoConta.designacao == estudantePerfil}"
               class="nav-link active">
                <svg class="bi pe-none me-2" width="16" height="16"><use href="/sakidila_ic/centro/registrar"></use></svg>
                Projectos
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/portal/projecto/pendentes/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link active">
                <svg class="bi pe-none me-2" width="16" height="16"><use href="/sakidila_ic/centro/registrar"></use></svg>
                Projectos pendentes
            </a>
        </li>
        <li>
            <a href="#" class="nav-link link-body-emphasis"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}">
                <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#table"></use></svg>
                Estudante
            </a>
        </li>
    </ul>
    <hr>
    <div class="dropdown">
        <a href="#" class="d-flex align-items-center link-body-emphasis text-decoration-none dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
            <img th:if="${conta == userDefault}" th:src="@{/img/user.png}" alt="mdo" width="32" height="32" class="rounded-circle">
            <img th:if="${conta != null}" th:src="${dadoConta.foto}" alt="mdo" width="32" height="32" class="rounded-circle">
            <strong>[[${dadoConta.username}]]</strong>
        </a>
        <ul class="dropdown-menu text-small shadow">
            <li><a th:if="${conta == userDefault}" class="dropdown-item" href="/sakidila_ic/usuario/login">Entrar</a></li>
            <li><a th:if="${conta != null}" class="dropdown-item" href="">Meu Perfil</a></li>
            <li><a th:if="${conta != null}" class="dropdown-item" href="/logout">Sair</a></li>
        </ul>
    </div>
</div>

<div class="container">
    <h5 class="display-6 mt-5 mb-1 fw-bold mt-5" style="color: #AB81A3">Projectos Pendentes de <i class="fw-bold text-dark">Estudantes!</i></h5>
    <hr>
    <fieldset class="bg-white px-5 py-4 shadow-sm rounded mb-3" style="height: 52%">
        <table class="table table-hover">
            <tr>
                <th scope="col">Projecto</th>
                <th scope="col">Tipo</th>
                <th scope="col">Estudante</th>
                <th scope="col">Estado</th>
            </tr>
            <tr th:each="item : ${projectos}">
                <td th:text="${item.designacao}"></td>
                <td th:text="${item.tipoProjecto.designacao}"></td>
                <td th:text="${item.gestor.pessoa.nome+' '+item.gestor.pessoa.apelido}"></td>
                <td th:if="${item.EhAprovado}">Aprovado</td>
                <td th:if="${item.EhAprovado != true}">Pendente</td>
                <td><a type="button" th:if="${item.EhAprovado}" class="btn btn-warning btn-sm">Remover Aprovação</a></td>
                <td><a type="button" th:if="${item.EhAprovado != true}"  th:href="@{/sakidila_ic/portal/projecto/aprovar/{id}(id=${item.id})}" class="btn btn-danger btn-sm">Aprovar divulgação</a></td>
            </tr>
        </table>
        <div class="d-flex justify-content-center">
            <nav class="Page navigation example" th:if="${totalPaginas > 1}">
                <ul class="pagination">
                    <li class="page-item"><a class="page-link">Total de projectos: [[${totalItems}]]</a></li>
                    <div th:each="i : ${#numbers.sequence(1,totalPaginas)}">
                        <li class="page-item">
                            <a class="page-link" th:if="${paginaActual != i}"
                               th:href="@{'/sakidila_ic/portal/listagem/'+ ${i}}">[[${i}]]</a>
                            <a class="page-link" th:unless="${paginaActual != i}">[[${i}]]</a> &nbsp; &nbsp;
                        </li>
                    </div>
                    <li class="page-item">
                        <a class="page-link" th:if="${paginaActual < totalPaginas}"
                           th:href="@{'/sakidila_ic/portal/listagem/'+${paginaActual + 1}}">Próximo</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" th:if="${paginaActual < totalPaginas}"
                           th:href="@{'/sakidila_ic/portal/listagem/'+${paginaActual + 1}}">Ultimo</a>
                        <a class="page-link" th:unless="${paginaActual < totalPaginas}">Ultimo</a>
                    </li>
                </ul>
            </nav>
        </div>
    </fieldset>
</div>
</body>
</html>