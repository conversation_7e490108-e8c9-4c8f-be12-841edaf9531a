<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">

    <!-- Importação do Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
            crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style th:inline="text">
        .tela-principal{
            background-image: url([[@{/img/telaprincipal.jpg}]]);
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            background-attachment: fixed;
        }

        .parceria{
            background-image: url([[@{/img/parceria.jpg}]]);
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            background-attachment: fixed;
        }

        body{
            font-family: Poppins;
        }

        .opcao{
            background-color: white;
            transition: background-color 0.5s;
            transform: scale(1);
            transition: background-color 0.5s, transform 0.5s;
        }

        .opcao:hover{
            transform: scale(1.05);
            background-color: aliceblue;
        }

        .opcao a{
            background-color: lavender;
            transition: background-color 0.5s;
            color: black;
        }

        .opcao:hover a{
            background-color: #7c6aa2;
            color: white;
        }

        .item-on{
            display: block;
        }

        .opcao:hover .item-on{
            display: none;
        }

        .item-off{
            display: none;
        }

        .opcao:hover .item-off{
            display: block;
        }

        .descricao{
            background-color: transparent;
        }

        .opcao:hover .descricao{
            background-color: rgba(255,255,255,0.89)
        }
    </style>
    <title>Title</title>
</head>
<body class="" style="margin: 0%;background-color: ghostwhite">
<nav class="navbar navbar-expand-lg p-0 sticky-top" style="margin: 0%;">
    <button class="navbar-toggler ms-2" type="button" data-bs-toggle="collapse" data-bs-target="#navbarTogglerDemo01" aria-controls="navbarTogglerDemo01" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarTogglerDemo01">
        <div class=" d-flex flex-column bg-white shadow-sm" style="width: 100%">
            <div class="container-fluid">
                <div class="container d-flex flex-row align-items-center">
                    <a class="navbar-brand" href="/sakidila_ic/portal/">
                        <img th:src="@{/img/logotipo.jpg}" alt="Bootstrap" width="120">
                    </a>
                    <div class="container d-flex flex-row justify-content-end align-items-center">
                        <a th:if="${conta == userDefault}" href="/sakidila_ic/usuario/principal" class="nav-link">Registrar</a>

                        <i style="font-style: normal;font-weight: bold" th:if="${conta != null}">Olá,</i>
                            <div th:if="${dadoConta != null}" class="me-1 ms-1"><i style="color: #AB81A3"> [[${dadoConta.tipoConta.nomenclatura}]]</i>
                        <i style="font-style: normal;font-weight: normal" id="username" th:text="${dadoConta.username}"></i>
                            </div>
                        <div class="dropdown btn">
                            <a href="#" class="d-block link-body-emphasis text-decoration-none dropdown dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <img th:if="${conta == userDefault}" th:src="@{/img/user.png}" alt="mdo" width="32" height="32" class="rounded-circle">
                                <img th:if="${conta != null}" th:src="${dadoConta.foto}" alt="mdo" width="32" height="32" class="rounded-circle">
                            </a>
                            <ul class="dropdown-menu text-small">
                                <li><a th:if="${conta == userDefault}" class="dropdown-item" href="/sakidila_ic/usuario/login">Entrar</a></li>
                                <li><button th:if="${conta != null}" class="dropdown-item" data-bs-target="#perfil" href="#" data-bs-toggle="modal">Meu Perfil</button></li>
                                <li><a th:if="${conta != null}" class="dropdown-item" href="/logout">Sair</a></li>
                                <!--<li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#">Sair</a></li>-->
                            </ul>
                        </div>
                        <i class="fs-5 bi bi-facebook me-2" style="color: #7c6aa2"></i>
                        <i class="fs-5 bi bi-instagram me-2" style="color: #7c6aa2"></i>
                        <i class="fs-5 bi bi-whatsapp" style="color: #7c6aa2"></i>
                    </div>
                </div>

            </div>
            <div class="container-fluid py-1" style="background-color: #AB81A3">
                <ul class="container navbar-nav d-flex justify-content-end ">
                    <li class="nav-item">
                        <a class="nav-link text-white active px-3" aria-current="page" href="/sakidila_ic/portal/">Portal</a>
                    </li>
                    <li class="nav-item" th:if="${dadoConta != null}">
                        <a class="nav-link text-white active px-3"
                           th:if="${dadoConta.tipoConta.designacao == adminPerfil}"  href="/sakidila_ic/entidade/registrar/1">Gestão Administrativa</a>
                    </li>
                    <li class="nav-item" th:if="${dadoConta != null}">
                        <a class="nav-link text-white active px-3"
                           th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"  href="/sakidila_ic/centro/registrar/1">Gestão Administrativa</a>
                    </li>
                    <li class="nav-item" th:if="${dadoConta != null}">
                        <a class="nav-link text-white active px-3"
                           th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil}"  href="/sakidila_ic/centro/investigador/associar/1">Gestão Administrativa</a>
                    </li>
                    <li class="nav-item" th:if="${dadoConta != null}">
                        <a class="nav-link text-white active px-3"
                           th:if="${dadoConta.tipoConta.designacao == estudantePerfil
                           or dadoConta.tipoConta.designacao == investigadorPerfil
                           or dadoConta.tipoConta.designacao == investigadorIndependentePerfil}"  href="/sakidila_ic/portal/projecto/registrar/1">Gestão Administrativa</a>
                    </li>
                    <li class="nav-item" th:if="${dadoConta != null}">
                        <a class="nav-link text-white active px-3"
                           th:if="${dadoConta.tipoConta.designacao == gestorProjectoPerfil}"  href="/sakidila_ic/portal/modulo_gestao">Gestão Administrativa</a>
                    </li>
                    <li class="nav-item" th:if="${dadoConta != null}">
                        <a class="nav-link text-white active px-3"
                           th:if="${dadoConta.tipoConta.designacao == empresa}"  href="/sakidila_ic/portal/projecto/registrar/1">Meus investimentos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white px-3" href="/sakidila_ic/portal/listagem/1">Projectos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white px-3" href="#">Sobre</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white ps-3 me-3" href="#">Contacte-nos</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</nav>

<div class="tela-principal">
    <div class="px-4 text-center " style="padding-top: 12%;padding-bottom: 10%">
        <h1 class="display-5 fw-bold " style="text-shadow: 0 0 2px black;color: #7c6aa2">Portal de Divulgação de Projectos Cientificos</h1>
        <div class="col-lg-6 mx-auto">
            <p class="lead mb-4 text-white">Seja bem-vindo ao nosso portal de projectos cientificos desenvolvidos pelas instituições colaboradoras. Temos para si muitos projectos inovadores e que possam resolver problemas do nosso dia-a-dia. Faça parte da nossa comunidade e permita-nos ajudar a promover o seu projecto. </p>
        </div>
    </div>
    <div class="container mb-5">
        <div class="container px-4 pb-5 mt-5 row" id="hanging-icons">
                <h2 class=" border-bottom text-center" style="color: #7c6aa2">Visão Aplicacional</h2>
            <div class=" row justify-content-between g-4 pb-2">
                <div class="col d-flex align-items-start rounded-4 p-4 shadow mx-2 opcao">
                    <div class="icon-square text-body-emphasis d-inline-flex align-items-center justify-content-center fs-4 flex-shrink-0 px-2 me-3 rounded-circle" style="background-color: #7c6aa2">
                        <i class=" p-1 bi bi-graph-up-arrow text-white"></i>
                    </div>
                    <div>
                        <h3 class="fs-2 " style="color: #AB81A3">Promover</h3>
                        <p style="text-align: justify">Divulgar o seu trabalho te tornará mais visivel ao mundo! Partilhe suas soluções, cresça com a nossa comunidade.</p>
                        <a href="/sakidila_ic/portal/listagem" class="btn shadow-sm border-0 rounded-4 mt-5">
                            Ir em Projectos
                        </a>
                    </div>
                </div>
                <div class="col d-flex align-items-start rounded-4 p-4 shadow mx-2 opcao">
                    <div class="icon-square text-body-emphasis  d-inline-flex align-items-center justify-content-center fs-4 flex-shrink-0 px-2 me-3 rounded-circle" style="background-color: #7c6aa2">
                        <i class="p-1 bi bi-wrench-adjustable text-white"></i>
                    </div>
                    <div>
                        <h3 class="fs-2 " style="color: #AB81A3">Solicitar</h3>
                        <p style="text-align: justify">Se tens alguma ideia e não sabes como torna-lá real, procure uma equipe ou profissional capacitado para concretiza-lá. Estamos disponivel para te conectar aos melhores profissionais qualifacados.</p>
                        <a href="#" class="btn shadow-sm border-0 rounded-4">
                            Ver serviços
                        </a>
                    </div>
                </div>
                <div class="col d-flex align-items-start rounded-4 p-4 shadow mx-2 opcao">
                    <div class="icon-square d-inline-flex align-items-center justify-content-center fs-4 flex-shrink-0 px-2 me-3 rounded-circle" style="background-color: #7c6aa2">
                        <i class="p-1 bi bi-currency-dollar text-white"></i>
                    </div>
                    <div>
                        <h3 class="fs-2" style="color: #AB81A3">Investir</h3>
                        <p style="text-align: justify">Encontre os melhores projectos para investir em nossa app. Acompanhe o percurso de desenvolvimento até a sua fase final. Já sabes em quais projectos investir?</p>
                        <a href="#" class="btn shadow-sm border-0 rounded-4 mt-4" >
                            Investir em projectos
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class=" mx-5 mt-5 mb-5">
    <!--<hr>-->
    <div class="row">
        <div class="col-3">
            <h4 class="fw-bold ms-4" style="color: #7c6aa2">Recentemente Publicados</h4>
            <p class="ms-4 me-5 mt-2" style="text-align: justify">Saiba mais sobre projectos desenvolvidos recentemente pela nossa comunidade. Soluções novas encontras aqui!</p>
            <button class="btn btn-outline-primary ms-4 mt-2 mb-5" type="button">Ver mais</button>
        </div>
        <div class="col" style="overflow-x: auto">
            <div class="d-flex flex-row flex-nowrap mb-3" >
                <div class="card shadow border-0 mx-2 rounded-4 d-flex flex-column opcao" th:each="item : ${recentes}" style="width: 360px; height: 290px">
                    <div class="container-fluid" style="position: absolute">
                        <h6 class="card-title text-center mt-3 fw-bold bg-white py-2 shadow-sm rounded-4 item-on" style="color: #AB81A3" th:text="${item.designacao}"></h6>
                    </div>
                    <img th:src="${item.logotipo}" width="100%" height="100%" class="rounded-4 item-on">
                    <div class="container-fluid bg-dark d-flex justify-content-end item-on">
                        <!--<a type="button" class="btn btn-primary btn-sm me-1 mb-2 shadow-sm border-0 rounded-4 item-on" style="position: absolute;bottom: 5%;">Ver mais</a>-->
                    </div>
                    <div class="card-body item-off">
                        <p class="fw-bold text-center item-off" th:text="${item.designacao}"></p>
                        <div style="height: 128px; overflow-y: hidden">
                            <p class="card-text item-off" th:text="${item.descricao}"></p>
                        </div>
                        <div class="d-flex justify-content-between align-items-center item-off">
                            <div class="btn-group">

                            </div>
                            <!--<a class="btn btn-primary btn-sm shadow-sm border-0 rounded-4 mt-3 item-off" style="">Ver mais</a>-->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
<h5 class="display-4 ms-5 fw-bold" style="color: #7c6aa2">Investe na MosalaLinkUp!</h5>
    <div class="container-fluid d-flex flex-row  ps-5" style="overflow-x:auto">
        <div class=" col-3 card border-0 shadow my-3 mx-2 rounded-4 opcao" th:each="item : ${top}" style="width: 450px;height: 450px">
            <div class="container-fluid" style="position: absolute">
                <h6 class="card-title text-center mt-3 fw-bold py-2 rounded-4 shadow" style="background-color: rgb(255,255,255);color: #AB81A3">Sistema de Gestão de projecto</h6>
            </div>
            <img th:src="${item.logotipo}" height="100%" class="rounded-4">
            <div class="container-fluid d-flex justify-content-center item-off">
                <div class=" rounded-4 py-3 d-flex flex-column justify-content-center shadow-sm item-off descricao" style="position: absolute;bottom: 5%;width: 90%;height: 200px;">
                    <div class="row m-3 item-off" style="height: 129px; overflow: hidden">
                        <h6 class="text-center p-0 item-off" style="color: #AB81A3">Descrição</h6>
                        <p class="card-text mb-5 item-off" th:text="${item.descricao}" style="text-align: justify"></p>
                    </div>
                    <div class="d-flex justify-content-center item-off">
                        <!--<button type="button" class="btn shadow-sm border-0 rounded-4 item-off" style="" data-bs-toggle="modal" data-bs-target="#staticBackdrop" >Saber mais</button>-->
                    </div>
                    <div class="d-flex justify-content-center item-on">
                        <!--<a type="button" class="btn me-1 mb-2 shadow-sm border-0 rounded-4" style="position: absolute;bottom: 5%;">Ver mais</a>-->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class=" mt-5 " style="background-color: aliceblue">
    <h5 class="display-6 text-center fw-bold pt-5" style="color: #7c6aa2">Nossos Parceiros</h5>
    <div class="container mx-auto pt-3">
        <p class="" style="text-align: justify">Na nossa jornada para o sucesso, a parceria é o nosso segredo. Com os nossos parceiros empresariais, construímos pontes para o êxito, fortalecendo cada passo do caminho. Juntos, alcançamos novos patamares de inovação, eficiência e prosperidade. Seja parte da nossa rede de parceiros e descubra um mundo de oportunidades ilimitadas. A sua visão, o nosso compromisso, o nosso futuro em conjunto.</p>
    </div>
    <div class="d-flex mx-5 mt-5 flex-wrap justify-content-center pb-5">
        <!--<div class="col-2 rounded-circle mx-3 mb-3 ">
            <div class="" style="position: relative;">
                <img src="/img/imagem.jpg" class="rounded-circle shadow-sm" width="100%" height="100%">
                    <div class="fixed-bottom rounded-bottom-circle rounded-circle d-flex justify-content-center align-items-center" style="height: 100%;position: absolute;background-color: rgba(159,129,171,0.82)">
                        <a href="#" class="nav-link text-center text-white fs-6 fw-bolder">Abrir</a>
                    </div>
            </div>
            <div class="row">
                <h6 class="text-center mt-2">UCAN</h6>
            </div>
        </div>-->
        <div class="col-2 rounded-circle mx-3 mb-3" th:each="item : ${instituicoes}">
            <div class="row" >
                    <img th:src="${item.logotipo}" class="rounded-circle" width="100" >

            </div>
            <div class="row">
                <h4 class="text-center mt-2 fw-bolder" th:text="${item.sigla}"></h4>
            </div>
        </div>
    </div>
</div>

<!--Modal de click no projecto-->
<div class="modal fade border-0" id="staticBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable border-0">
        <div class="modal-content">
            <div class="modal-header " style="background-color: #AB81A3">
                <h1 class="modal-title fs-5 text-white" id="staticBackdropLabel"></h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="background-color: ghostwhite">
                <div class="row d-flex flex-column align-items-center">

                    <div class="container bg-white shadow-sm rounded-4 d-flex justify-content-center">
                        <img src="/img/imagem.jpg" class="m-3" width="350" height="350">
                    </div>

                    <div class="rounded-4 bg-white shadow-sm p-3 text-center mt-2">
                        <h6 class="mb-0" style="color: #7c6aa2">Instituição Acadêmica</h6>
                        <p>Universidade Católica de Angola</p>
                        <h6 class="mb-0" style="color: #7c6aa2">Faculdade</h6>
                        <p>Eng. Informática</p>
                        <h6 class="mb-0" style="color: #7c6aa2">Area de Actuação</h6>
                        <p>Eng. Setor Educacional</p>
                        <small class="mb-0" style="color: #AB81A3">Data de Lançamento</small>
                        <small class="mb-0 text-muted">10-Jun-2023</small>
                    </div>
                    <div class="container-fluid accordion accordion-flush" id="accordionFlushExample">
                        <div class="accordion-item border-0 my-1 rounded-4">
                            <h2 class="accordion-header rounded-4">
                                <button class="accordion-button collapsed rounded-4 shadow-sm bg-white" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapse1" aria-expanded="false" aria-controls="flush-collapse1">
                                    <h6 class="mb-0">Imagens</h6>
                                </button>
                            </h2>
                            <div id="flush-collapse1" class="accordion-collapse collapse shadow-sm rounded-4 mt-1 bg-white" style="background-color: #AB81A3;" data-bs-parent="#accordionFlushExample">
                                <div class="accordion-body">
                                    <div class="row row-cols-4 d-flex align-items-between">
                                        <div class="col-3 my-3">
                                            <img src="/img/imagem.jpg" class="shadow-sm bg-white rounded-4" width="80" height="70">
                                        </div>
                                        <div class="col-3 my-3">
                                            <img src="/img/imagem.jpg" class="shadow-sm bg-white rounded-4" width="80" height="70">
                                        </div>
                                        <div class="col-3 my-3">
                                            <img src="/img/imagem.jpg" class="shadow-sm bg-white rounded-4" width="80" height="70">
                                        </div>
                                        <div class="col-3 my-3">
                                            <img src="/img/imagem.jpg" class="shadow-sm bg-white rounded-4" width="80" height="70">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item border-0 my-1 rounded-4">
                            <h2 class="accordion-header rounded-4">
                                <button class="accordion-button collapsed rounded-4 shadow-sm bg-white" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                                    <h6 class="mb-0">Resumo</h6>
                                </button>
                            </h2>
                            <div id="flush-collapseOne" class="accordion-collapse collapse shadow-sm rounded-4 mt-1 bg-white" style="background-color: #AB81A3;" data-bs-parent="#accordionFlushExample">
                                <div class="accordion-body">Placeholder content for this accordion, which is intended to demonstrate the <code>.accordion-flush</code> class. This is the first item's accordion body.</div>
                            </div>
                        </div>
                        <div class="accordion-item border-0 my-1 rounded-4">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed rounded-4 shadow-sm bg-white" style="background-color: #AB81A3;" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                                    <h6 class="mb-0">Colaboradores</h6>
                                </button>
                            </h2>
                            <div id="flush-collapseTwo" class="accordion-collapse collapse shadow-sm rounded-4 mt-1 bg-white" style="background-color: #AB81A3;" data-bs-parent="#accordionFlushExample">
                                <div class="accordion-body">Placeholder content for this accordion, which is intended to demonstrate the <code>.accordion-flush</code> class. This is the second item's accordion body. Let's imagine this being filled with some actual content.</div>
                            </div>
                        </div>
                        <div class="accordion-item border-0 my-1 rounded-4">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed rounded-4 shadow-sm bg-white" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseThree" aria-expanded="false" aria-controls="flush-collapseThree">
                                    <h6 class="mb-0 ">Informações Adicionais</h6>
                                </button>
                            </h2>
                            <div id="flush-collapseThree" class="accordion-collapse collapse shadow-sm rounded-4 mt-1 bg-white" data-bs-parent="#accordionFlushExample">
                                <div class="accordion-body">Placeholder content for this accordion, which is intended to demonstrate the <code>.accordion-flush</code> class. This is the third item's accordion body. Nothing more exciting happening here in terms of content, but just filling up the space to make it look, at least at first glance, a bit more representative of how this would look in a real-world application.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary shadow-sm border-0 rounded-4" style="background-color: #7c6aa2" data-bs-dismiss="modal" >Fechar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Perfil-->
<div class="modal fade" id="perfil" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" th:if="${dadoConta != null}">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="" id="staticBackdropLabel"><h5 class="modal-title fs-4 fw-bold" style="color: rgba(158,158,151)">Meu <i class=" fw-bold text-dark">Perfil</i></h5></h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row row-cols-4 d-flex justify-content-center">
                    <div class="col d-flex flex-column">
                        <figure class="text-center" th:if="${dadoConta.tipoConta != null and dadoConta.tipoConta.designacao != investigadorIndependentePerfil}">
                            <img th:if="${dadoConta.tipoConta.designacao != empresaPerfil}" th:src="${dadoConta.entidade.logotipo}" class="rounded-circle mx-auto d-block">
                            <blockquote class="blockquote my-3">
                                <p class="ver" th:if="${dadoConta.tipoConta != null and dadoConta.tipoConta.designacao != investigadorIndependentePerfil or dadoConta.tipoConta.designacao != empresaPerfil}">[[${dadoConta.entidade.designacao}]] <i class="fw-bolder">[[${dadoConta.entidade.sigla}]]</i></p>
                            </blockquote>

                            <figcaption class="blockquote-footer" th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}">
                                total de centros de investigações <cite title="Source Title"><i class="fw-bolder">[[${centros.size}]]</i></cite>
                            </figcaption>
                        </figure>
                        <form method="post" name="formEmpresa" th:action="@{/sakidila_ic/usuario/empresa/registrar/salvar}" enctype="multipart/form-data" onsubmit="return validateFormEmp()" required>
                            <img th:if="${dadoConta.tipoConta.designacao == empresaPerfil}" th:src="${dadoConta.foto}" class="rounded-circle mx-auto d-block" width="150" height="150">
                            <div th:if="${dadoConta.tipoConta.designacao == empresaPerfil}" class="p-3 editar  d-none">
                                <label class="form-label fw-bolder">Adicione o logotipo da Empresa</label>
                                <input class="form-control" type="file" id="imagem" name="imagem">
                            </div>
                            <input type="hidden" name="id" th:value="${dadoConta.id}">
                            <input type="hidden" name="entidade.id" th:if="${dadoConta.entidade != null}" th:value="${dadoConta.entidade.id}">
                            <input type="hidden" id="password" th:value="${dadoConta.senha}">
                            <dl class="row" th:if="${dadoConta.tipoConta.designacao == empresaPerfil}">

                                <dt class="col-sm-5 ver">Denominação</dt>
                                <dd class=" mb-3">
                                    <p class="p-0 m-0 ver">[[${dadoConta.entidade.designacao}]]</p>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Denominação</label>
                                        <input class="form-control" type="text" id="designacaoEmp" onfocusout="validDenominacao()" name="entidade.designacao" th:value="${dadoConta.entidade.designacao}">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com o nome da empresa.
                                        </div>
                                    </div>
                                </dd>

                                <dt class="col-sm-5 ver">Sigla</dt>
                                <dd class=" mb-3">
                                    <p class="p-0 m-0 ver">[[${dadoConta.entidade.sigla}]]</p>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Sigla</label>
                                        <input class="form-control" type="text" id="siglaEmp" onfocusout="validSigla()" name="entidade.sigla" th:value="${dadoConta.entidade.sigla}">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com a sigla da empresa.
                                        </div>
                                    </div>
                                </dd>
                                <dt class="col-sm-5 ver">NIF</dt>
                                <dd class=" mb-3">
                                    <p class="p-0 m-0 ver">[[${dadoConta.entidade.nif}]]</p>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">NIF</label>
                                        <input class="form-control" type="text" id="nifEmp" onfocusout="validNif()" name="entidade.nif" th:value="${dadoConta.entidade.nif}">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com o nif da empresa.
                                        </div>
                                    </div>
                                </dd>
                                <dt class="col-sm-5 ver">Residencia</dt>
                                <dd class=" mb-3">
                                    <p class="p-0 m-0 ver">[[${dadoConta.entidade.localidade.localidadePai.localidadePai.designacao}]], [[${dadoConta.entidade.localidade.localidadePai.designacao}]], [[${dadoConta.entidade.localidade.designacao}]]</p>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Provincia</label>
                                        <select class="form-select" id="provincia" name="provincia">
                                            <option th:each="localidade : ${localidades}" th:value="${localidade.id}"
                                                    th:text="${localidade.designacao}" th:selected="${localidade.id == dadoConta.entidade.localidade.localidadePai.localidadePai.id}"></option>
                                        </select>
                                    </div>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Municipio</label>
                                        <select class="form-select" id="municipio" name="municipio"></select>
                                    </div>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Bairro</label>
                                        <select class="form-select" id="bairro" name="entidade.localidade.id"></select>
                                    </div>
                                    <p class="p-0 m-0 ver">Endereço: [[${dadoConta.entidade.endereco}]]</p>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Endereço</label>
                                        <input class="form-control" type="text" id="enderecoEmp" onfocusout="validEnderecoEmp()" name="entidade.endereco" th:value="${dadoConta.entidade.endereco}">
                                    </div>
                                </dd>
                                <dt class="col-sm-5 ver">Contactos</dt>
                                <dd class=" mb-3">
                                    <p class="p-0 m-0 ver">[[${dadoConta.contacto}]]</p>
                                    <p class="p-0 m-0 ver">[[${dadoConta.email}]]</p>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Telefone</label>
                                        <input class="form-control" type="text" id="telEmp" onfocusout="validTelEmp()" name="contacto" th:value="${dadoConta.contacto}">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com o contacto telefônico da empresa.
                                        </div>
                                    </div>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Email</label>
                                        <input class="form-control" type="text" id="emailEmp" onfocusout="validEmailEmp()" name="email" th:value="${dadoConta.email}">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com o email da empresa.
                                        </div>
                                    </div>
                                </dd>
                                <dt class="col-sm-5 ver">Conta Empresa</dt>
                                <dd class=" mb-3">
                                    <p class="p-0 m-0 ver">Username: [[${dadoConta.username}]]</p>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Username</label>
                                        <input class="form-control" type="text" id="usernameEmp" onfocusout="validUsernameEmp()" name="username" th:value="${dadoConta.username}">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com o novo username da empresa.
                                        </div>
                                    </div>
                                    <div class="form-check form-switch ms-3 editar d-none">
                                        <input class="form-check-input" type="checkbox" role="switch" id="alterarSenha" value="0">
                                        <label class="form-check-label fw-bolder" for="alterarSenha">Modificar a senha?</label>
                                    </div>
                                    <div class="p-3 editarSenha  d-none">
                                        <label class="form-label fw-bolder">Senha antiga</label>
                                        <input class="form-control" type="password" onfocusout="validSenhaEmp()" name="senha-antiga" id="senha-antiga">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com a senha antiga.
                                        </div>
                                    </div>
                                    <div class="p-3 editarSenha  d-none">
                                        <label class="form-label fw-bolder">Senha nova</label>
                                        <input class="form-control" type="password" id="senha-nova" onfocusout="validSenhaNovaEmp()" name="senha" th:value="${dadoConta.senha}">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com a nova senha.
                                        </div>
                                    </div>
                                    <div class="p-3 editarSenha  d-none">
                                        <label class="form-label fw-bolder">Confirmar senha nova</label>
                                        <input class="form-control" type="password" onfocusout="validSenhaConfirmarEmp()"  name="senha-confirmar" id="senha-confirmar">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            confirme com a sua senha nova.
                                        </div>
                                    </div>
                                </dd>
                                <dt class="col-sm-5 ver">Plano de subcrição</dt>
                                <dd class="col-sm-7 mb-3 ver">
                                    <p th:if="${dadoConta.ehSubscrito}" class="text-success">Activo</p>
                                    <p th:if="${!dadoConta.ehSubscrito}" class="text-danger">Sem pacote</p>
                                    <input type="hidden" name="ehSubscrito" th:value="${dadoConta.ehSubscrito}">
                                </dd>
                            </dl>
                            <button type="submit" class="btn btn-success editar d-none w-100" th:if="${dadoConta.tipoConta.designacao == empresaPerfil}">Guardar</button>
                        </form>
                        <form method="post" name="formUser" th:action="@{/sakidila_ic/usuario/update}" enctype="multipart/form-data" onsubmit="return validateFormP()" required>
                            <dl class="row" th:if="${dadoConta.tipoConta.designacao != empresaPerfil}">
                                <input type="hidden" name="id" th:value="${dadoConta.id}">
                                <input type="hidden" name="pessoa.id" th:value="${dadoConta.pessoa.id}">
                                <input type="hidden" id="password" th:value="${dadoConta.senha}">
                                <input type="hidden" name="tipoConta.id" th:value="${dadoConta.tipoConta.id}">
                                <input type="hidden" name="centro.id" th:if="${dadoConta.centro != null}" th:value="${dadoConta.centro.id}">
                                <div th:if="${dadoConta.tipoConta.designacao != investigadorIndependentePerfil}">
                                    <input type="hidden" name="entidade.id" th:value="${dadoConta.entidade.id}">
                                </div>

                                <div th:if="${dadoConta.tipoConta.designacao != gestorInstituicaoPerfil}">
                                    <input type="hidden" name="nivelAcademico.id" th:value="${dadoConta.nivelAcademico.id}">
                                </div>

                                <div th:if="${dadoConta.faculdade != null}">
                                    <input type="hidden" name="faculdade.id" th:value="${dadoConta.faculdade.id}">
                                </div>

                                <dt class="col-sm-5 ver">Nome completo</dt>
                                <dd class=" mb-3">
                                    <div class="ver">[[${dadoConta.pessoa.nome+' '+dadoConta.pessoa.apelido}]]</div>
                                    <img th:src="${dadoConta.foto}" class=" rounded-circle ver" width="150" height="150">
                                    <div th:if="${dadoConta.tipoConta.designacao != empresaPerfil}" class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Actualize a sua foto de perfil</label>
                                        <input class="form-control" type="file" id="imagemP" name="imagem">
                                    </div>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Nome</label>
                                        <input class="form-control" type="text" id="nomeP" onfocusout="validNomeP()" name="pessoa.nome" th:value="${dadoConta.pessoa.nome}">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com o novo nome.
                                        </div>
                                    </div>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Apelido</label>
                                        <input class="form-control" type="text" id="apelidoP" onfocusout="validApelidoP()" name="pessoa.apelido" th:value="${dadoConta.pessoa.apelido}">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com o novo apelido.
                                        </div>
                                    </div>
                                </dd>

                                <dt class="col-sm-5 ver">Nº Identidade</dt>
                                <dd class=" mb-3">
                                    <p class="p-0 m-0 ver">[[${dadoConta.pessoa.bi}]]</p>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Nº Identidade</label>
                                        <input class="form-control" type="text" id="biP" onfocusout="validBiP()" name="pessoa.bi" th:value="${dadoConta.pessoa.bi}">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com o novo número do BI.
                                        </div>
                                    </div>
                                    <p class="p-0 m-0 ver">[[${dadoConta.pessoa.dataNascimento}]]</p>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Data de nascimento</label>
                                        <input class="form-control" type="date" id="dataNascimentoP" onfocusout="validDataNascimentoP()" name="pessoa.dataNascimento" th:value="${dadoConta.pessoa.dataNascimento}">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com o novo número do BI.
                                        </div>
                                    </div>
                                    <p class="p-0 m-0 ver">Emitido aos [[${dadoConta.pessoa.dataEmissao}]]</p>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Data de emissão</label>
                                        <input class="form-control" type="date" id="dataEmissaoP" onfocusout="validDataEmissaoP()" name="pessoa.dataEmissao" th:value="${dadoConta.pessoa.dataEmissao}">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com a nova data de emissão do BI.
                                        </div>
                                    </div>
                                    <p class="p-0 m-0 ver">Válido até [[${dadoConta.pessoa.dataValidade}]]</p>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Data de validade</label>
                                        <input class="form-control" type="date" id="dataValidadeP" onfocusout="validDataValidadeP()" name="pessoa.dataValidade" th:value="${dadoConta.pessoa.dataValidade}">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com a nova data de validade do BI.
                                        </div>
                                    </div>
                                </dd>

                                <dt class="col-sm-5 ver">Estado civil</dt>
                                <dd class=" mb-3">
                                    <div class="ver">[[${dadoConta.pessoa.estadoCivil.designacao}]]</div>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Estado civil</label>
                                        <select class="form-select" id="estadoCivilP" onfocusout="validEstadoCivilP()" name="pessoa.estadoCivil.id">
                                            <option th:each="item : ${estados}" th:text="${item.designacao}" th:value="${item.id}" th:selected="${item.id == dadoConta.pessoa.estadoCivil.id}"></option>
                                        </select>
                                    </div>
                                </dd>

                                <dt class="col-sm-5 text-truncate ver">Genêro</dt>
                                <dd class=" mb-3">
                                    <div class="ver">[[${dadoConta.pessoa.sexo.designacao}]]</div>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Genêro</label>
                                        <select class="form-select" id="sexoP" onfocusout="validSexoP()" name="pessoa.sexo.id">
                                            <option th:each="item : ${sexos}" th:text="${item.designacao}" th:value="${item.id}" th:selected="${item.id == dadoConta.pessoa.sexo.id}"></option>
                                        </select>
                                    </div>
                                </dd>

                                <dt class="col-sm-5 ver">Residencia</dt>
                                <dd class=" mb-3">
                                    <p class="p-0 m-0 ver">[[${dadoConta.pessoa.localidade.localidadePai.localidadePai.designacao}]], [[${dadoConta.pessoa.localidade.localidadePai.designacao}]], [[${dadoConta.pessoa.localidade.designacao}]]</p>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Provincia</label>
                                        <select class="form-select" id="provincia" name="provincia">
                                            <option th:each="localidade : ${localidades}" th:value="${localidade.id}"
                                                    th:text="${localidade.designacao}" th:selected="${localidade.id == dadoConta.pessoa.localidade.localidadePai.localidadePai.id}"></option>
                                        </select>
                                    </div>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Municipio</label>
                                        <select class="form-select" id="municipio" name="municipio"></select>
                                    </div>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Bairro</label>
                                        <select class="form-select" id="bairro" name="pessoa.localidade.id"></select>
                                    </div>
                                    <p class="p-0 m-0 ver">Endereço: [[${dadoConta.pessoa.endereco}]]</p>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Endereço</label>
                                        <input class="form-control" type="text" id="enderecoP" onfocusout="validEnderecoP()" name="pessoa.endereco" th:value="${dadoConta.pessoa.endereco}">
                                    </div>
                                </dd>
                                <dt class="col-sm-5 ver">Contactos</dt>
                                <dd class=" mb-3">
                                    <p class="p-0 m-0 ver">[[${dadoConta.email}]]</p>
                                    <p class="p-0 m-0 ver">[[${dadoConta.contacto}]]</p>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Email</label>
                                        <input class="form-control" type="text" id="emailP" onfocusout="validEmailP()" name="email" th:value="${dadoConta.email}">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com o email.
                                        </div>
                                    </div>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Contacto</label>
                                        <input class="form-control" type="text" id="contactoP" onfocusout="validContactoP()" name="contacto" th:value="${dadoConta.contacto}">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com o contacto telefônico.
                                        </div>
                                    </div>
                                </dd>
                                <dt class="col-sm-5 ver"
                                    th:if="${dadoConta.faculdade != null}">Faculdade</dt>
                                <dd class=" mb-3"
                                    th:if="${dadoConta.faculdade != null}">
                                    <p class="p-0 m-0" th:if="${dadoConta.faculdade != null}">[[${dadoConta.faculdade.designacao}]]</p>
                                    <div class="p-3 editar  d-none" th:if="${dadoConta.faculdade != null}">
                                        <label class="form-label fw-bolder">Faculdade</label>
                                        <select class="form-select" id="faculdadeP" name="faculdade.id">
                                            <option th:each="item : ${faculdades}" th:text="${item.designacao}" th:value="${item.id}" th:selected="${item.id == dadoConta.faculdade.id}"></option>
                                        </select>
                                    </div>
                                </dd>
                                <dt class="col-sm-5 ver"
                                    th:if="${dadoConta.nivelAcademico != null}">Nível Acadêmico</dt>
                                <dd class=" mb-3"
                                    th:if="${dadoConta.nivelAcademico != null}">
                                    <p class="p-0 m-0 ver">[[${dadoConta.nivelAcademico.designacao}]]</p>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Nível acadêmico</label>
                                        <select class="form-select" id="nivelAcademicoP" name="nivelAcademico.id">
                                            <option th:each="item : ${niveis}" th:text="${item.designacao}" th:value="${item.id}" th:selected="${item.id == dadoConta.nivelAcademico.id}"></option>
                                        </select>
                                    </div>
                                </dd>
                                <dt class="col-sm-5 ver">Conta</dt>
                                <dd class="mb-3">
                                    <p class="p-0 m-0 ver">Username: [[${dadoConta.username}]]</p>
                                    <div class="p-3 editar  d-none">
                                        <label class="form-label fw-bolder">Username</label>
                                        <input class="form-control" type="text" id="usernameP" onfocusout="validUsernameP()" name="username" th:value="${dadoConta.username}">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com o novo nome de usuario.
                                        </div>
                                    </div>
                                    <div class="form-check form-switch ms-3 editar d-none">
                                        <input class="form-check-input" type="checkbox" role="switch" id="alterarSenha" value="0">
                                        <label class="form-check-label fw-bolder" for="alterarSenha">Modificar a senha?</label>
                                    </div>
                                    <div class="p-3 editarSenha  d-none">
                                        <label class="form-label fw-bolder">Senha antiga</label>
                                        <input class="form-control" type="password" onfocusout="validSenhaP()" name="senha-antiga" id="senha-antigaP">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com a senha antiga.
                                        </div>
                                    </div>
                                    <div class="p-3 editarSenha  d-none">
                                        <label class="form-label fw-bolder">Senha nova</label>
                                        <input class="form-control" type="password" id="senha-novaP" onfocusout="validSenhaNovaP()" name="senha" th:value="${dadoConta.senha}">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            Preencher o campo com a nova senha.
                                        </div>
                                    </div>
                                    <div class="p-3 editarSenha  d-none">
                                        <label class="form-label fw-bolder">Confirmar senha nova</label>
                                        <input class="form-control" type="password" onfocusout="validSenhaConfirmarP()"  name="senha-confirmar" id="senha-confirmarP">
                                        <div class="valid-feedback">

                                        </div>
                                        <div class="invalid-feedback">
                                            confirme com a sua senha nova.
                                        </div>
                                    </div>
                                </dd>
                                <dt class="col-sm-5 ver">Plano de subcrição</dt>
                                <dd class="col-sm-7 mb-3 ver">
                                    <p th:if="${dadoConta.ehSubscrito}" class="text-success p-0 m-0">Activo</p>
                                    <p th:if="${!dadoConta.ehSubscrito}" class="text-danger p-0 m-0">Sem pacote</p>
                                </dd>
                            </dl>
                            <button type="submit" class="btn btn-success editar d-none w-100" th:if="${dadoConta.tipoConta.designacao != empresaPerfil}">Guardar</button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Voltar</button>
                <button type="button" class="btn btn-warning" id="modificarButton" value="0">Editar perfil</button>

            </div>
        </div>
    </div>
</div>

<div class=" container-fluid bg-white text-center shadow" >
    <div class="container d-flex flex-row justify-content-between pt-3">
        <div class="menu-rodape">
            <ul class="nav">
                <li class="nav-item m-0"><a class="nav-link"  style="color: #7c6aa2" href="">Portal</a></li>
                <li class="nav-item m-0"><a class="nav-link"  style="color: #7c6aa2" href="">Iniciar Sessão</a></li>
                <li class="nav-item m-0"><a class="nav-link"  style="color: #7c6aa2" href="">Registrar</a></li>
            </ul>
        </div>
        <div class="d-flex flex-row align-items-center">
            <i class="fs-7 me-2 text-muted" style="font-style: normal">Media Social</i>
            <i class="fs-7 bi bi-facebook me-2" style="color: #7c6aa2"></i>
            <i class="fs-7 bi bi-instagram me-2" style="color: #7c6aa2"></i>
            <i class="fs-7 bi bi-whatsapp" style="color: #7c6aa2"></i>
        </div>
    </div>
    <small class="text-muted" style="font-size: 12px">Software desenvolvido pela Sakidila©, todos direitos reservados.</small>
</div>
<div class="toast-container position-fixed bottom-0 end-0 p-3"  data-bs-autohide="false">
    <div id="liveToast" class="toast" style="background-color: #AB81A3; color: white" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto">MusalaLinkUp</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">
            Seja bem vindo a nossa plataforma, faça login ou registra-te para ver projectos divulgados e muito mais...
        </div>
    </div>
</div>
<script>

    window.onload = (event) => {
        const toastLiveExample = document.getElementById('liveToast')

            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        setTimeout(() => {
            toastBootstrap.show()
        },2000)

        //Senha predifinida carregada

        pass = $("#password").val()
        $("#senha-nova").val(pass);
        $("#senha-confirmar").val(pass);
        $("#senha-antiga").val(pass);
        $("#senha-novaP").val(pass);
        $("#senha-confirmarP").val(pass);
        $("#senha-antigaP").val(pass);

    };

    $(document).ready(function () {

        $("#senha-antiga").change(function (){

            decodificar().then(() => {
                component = $("#senha-antiga")
                if(validar) {
                    component.addClass("is-valid")
                    component.removeClass("is-invalid")
                }
                else {
                    component.addClass("is-invalid")
                    component.removeClass("is-valid")
                }
            })
        })

        $("#senha-antigaP").change(function (){
            decodificarP().then(() => {
                component = $("#senha-antigaP")
                if(validar) {
                    component.addClass("is-valid")
                    component.removeClass("is-invalid")
                }
                else {
                    component.addClass("is-invalid")
                    component.removeClass("is-valid")
                }
            })
        })

        $("#modificarButton").click(function (){
            modificarView();
        })

        $("#alterarSenha").click(function (){
            showInputSenhas();
        })


    })

    function showInputSenhas(){

        var password = $("#password").val();

        if($("#alterarSenha").val() == "0"){
            $(".editarSenha").removeClass("d-none")
            $(".verSenha").addClass("d-none")
            $("#alterarSenha").val("1")
            $("#senha-nova").val("");
            $("#senha-antiga").val("");
            $("#senha-confirmar").val("");
            $("#senha-antigaP").val("");
            $("#senha-novaP").val("");
            $("#senha-confirmarP").val("");
        }
        else{
            $(".verSenha").removeClass("d-none")
            $(".editarSenha").addClass("d-none")
            $("#alterarSenha").val("0")
            $("#senha-nova").val(password);
            $("#senha-confirmar").val(password);
            $("#senha-antiga").val(password);
            $("#senha-novaP").val(password);
            $("#senha-confirmarP").val(password);
            $("#senha-antigaP").val(password);
        }
    }


    function modificarView(){
        if($("#modificarButton").val() == "0") {
            $(".editar").removeClass("d-none")
            $(".ver").addClass("d-none")
            $("#modificarButton").removeClass("btn-warning")
            $("#modificarButton").addClass("btn-outline-warning")
            $("#modificarButton").text("Apenas ver")
            $("#modificarButton").val("1")
            $("#salvarButton").removeClass("d-none")
        }
        else if ($("#modificarButton").val() == "1"){
            $(".ver").removeClass("d-none")
            $(".editar").addClass("d-none")
            $("#modificarButton").removeClass("btn-outline-warning")
            $("#modificarButton").addClass("btn-warning")
            $("#modificarButton").text("Modificar")
            $("#modificarButton").val("0")
            $("#salvarButton").addClass("d-none")
        }
    }

    function validDenominacao(){
        info = document.forms["formEmpresa"]["entidade.designacao"].value;
        component = $("#designacaoEmp")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSigla(){
        info = document.forms["formEmpresa"]["entidade.sigla"].value;
        component = $("#siglaEmp")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validNif(){
        info = document.forms["formEmpresa"]["entidade.nif"].value;
        component = $("#nifEmp")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validTelEmp(){
        info = document.forms["formEmpresa"]["contacto"].value;
        component = $("#telEmp")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validEnderecoEmp(){
        info = document.forms["formEmpresa"]["entidade.endereco"].value;
        component = $("#enderecoEmp")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validUsernameEmp(){
        info = document.forms["formEmpresa"]["username"].value;
        component = $("#usernameEmp")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validEmailEmp(){
        info = document.forms["formEmpresa"]["email"].value;
        component = $("#emailEmp")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenhaEmp(){
        info = document.forms["formEmpresa"]["senha-antiga"].value;
        component = $("#senha-antiga")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenhaNovaEmp(){
        info = document.forms["formEmpresa"]["senha"].value;
        component = $("#senha-nova")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenhaConfirmarEmp(){
        info = document.forms["formEmpresa"]["senha-confirmar"].value;
        component = $("#senha-confirmar")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    //Validação pessoa

    function validNomeP(){
        info = document.forms["formUser"]["pessoa.nome"].value;
        component = $("#nomeP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validApelidoP(){
        info = document.forms["formUser"]["pessoa.apelido"].value;
        component = $("#apelidoP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validBiP(){
        info = document.forms["formUser"]["pessoa.bi"].value;
        component = $("#biP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataNascimentoP(){
        info = document.forms["formUser"]["pessoa.dataNascimento"].value;
        component = $("#dataNascimentoP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataEmissaoP (){
        info = document.forms["formUser"]["pessoa.dataEmissao"].value;
        component = $("#dataEmissaoP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataValidadeP (){
        info = document.forms["formUser"]["pessoa.dataValidade"].value;
        component = $("#dataValidadeP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validEnderecoP (){
        info = document.forms["formUser"]["pessoa.endereco"].value;
        component = $("#enderecoP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validUsernameP (){
        info = document.forms["formUser"]["username"].value;
        component = $("#usernameP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenhaP (){
        info = document.forms["formUser"]["senha-antiga"].value;
        component = $("#senha-antigaP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenhaNovaP(){
        info = document.forms["formUser"]["senha"].value;
        component = $("#senha-novaP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenhaConfirmarP(){
        info = document.forms["formUser"]["senha-confirmar"].value;
        component = $("#senha-confirmarP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validDataNascimentoP(){
        info = document.forms["formUser"]["pessoa.dataNascimento"].value;
        component = $("#dataNascimentoP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validEmailP(){
        info = document.forms["formUser"]["email"].value;
        component = $("#emailP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validContactoP(){
        info = document.forms["formUser"]["contacto"].value;
        component = $("#contactoP")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    var dadosGlobais;

    // Empresa
    function fazerRequisicao() {
        email = document.forms["formEmpresa"]["username"].value;

        url = "http://localhost:8080/usuarioAjax/" + email;
        // Retorna uma Promise que resolve quando a requisição é bem-sucedida
        return new Promise((resolve, reject) => {
            // Faz a requisição usando a função fetch
            fetch(url)
                .then(response => {

                    // Verifica se a resposta da requisição é bem-sucedida (status 2xx)
                    if (!response.ok) {
                        throw new Error('Erro na requisição');
                    }
                    // Parseia a resposta como JSON
                    return response.json();
                })
                .then(data => {
                    // Armazena os dados na variável global
                    dadosGlobais = data;
                    // Resolve a Promise
                    submitValidation = true
                    resolve(submitValidation);
                })
                .catch(error => {
                    // Rejeita a Promise em caso de erro
                    reject(error);
                });
        });
    }


    // Empresa
    function decodificar() {
        id = document.forms["formEmpresa"]["id"].value;
        senha = document.forms["formEmpresa"]["senha-antiga"].value;

        url = "http://localhost:8080/usuarioAjax/conta/"+id+"/"+senha;
        // Retorna uma Promise que resolve quando a requisição é bem-sucedida
        return new Promise((resolve, reject) => {
            // Faz a requisição usando a função fetch
            fetch(url)
                .then(response => {

                    // Verifica se a resposta da requisição é bem-sucedida (status 2xx)
                    if (!response.ok) {
                        throw new Error('Erro na requisição');
                    }
                    // Parseia a resposta como JSON
                    return response.json();
                })
                .then(data => {
                    // Armazena os dados na variável global
                    validar = data;
                    // Resolve a Promise
                    submitValidation = true
                    resolve(submitValidation);
                })
                .catch(error => {
                    // Rejeita a Promise em caso de erro
                    reject(error);
                });
        });
    }



    //Empresa
    function validateFormEmp() {

        const toastLiveExample = document.getElementById('liveToast')
        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        let submit = false;
        fazerRequisicao().then((submitValidation) => {
            nome = document.forms["formEmpresa"]["entidade.designacao"].value;
            sigla = document.forms["formEmpresa"]["entidade.sigla"].value;
            nif = document.forms["formEmpresa"]["entidade.nif"].value;
            endereco = document.forms["formEmpresa"]["entidade.endereco"].value;
            username = document.forms["formEmpresa"]["username"].value;
            email = document.forms["formEmpresa"]["email"].value;
            senha = document.forms["formEmpresa"]["senha-nova"].value;
            senhaConfirmar = document.forms["formEmpresa"]["senha-confirmar"].value;
            contacto = document.forms["formEmpresa"]["contacto"].value;
            if (nome == ""
                || sigla == ""
                || nif == ""
                || endereco == ""
                || username == ""
                || email == ""
                || senha == ""
                || senhaConfirmar == ""
                || contacto == ""
                || senha != senhaConfirmar) {

                validDenominacao()
                validSigla()
                validNif()
                validEnderecoEmp()
                validUsernameEmp()
                validTelEmp()
                validEmailEmp()
                validSenhaEmp()
                validSenhaConfirma()

                //confirmar senha

                if (senha != senhaConfirmar) {

                    $("#liveToast").addClass("text-bg-danger")
                    $("#titlo-alerta").empty()
                    $("#titlo-alerta").text("Erro ao criar conta estudante")
                    $("#mensagem").empty()
                    $("#mensagem").text("A senha nova deve ser a mesma no campo confirmar senha")
                    toastBootstrap.show()
                    submit = false;
                }
                setTimeout(() => {
                    const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
                    $("#liveToast").addClass("text-bg-danger")
                    $("#liveToast").removeClass("text-bg-warning")
                    $("#titlo-alerta").empty()
                    $("#titlo-alerta").text("Erro ao criar conta estudante")
                    $("#mensagem").empty()
                    $("#mensagem").text("Verifique os campos se estão vazios ou devidamente preenchidos.")
                    toastBootstrap.show()
                    submit = false;
                }, 4000)
            } else if (dadosGlobais) {
                submit = false
                const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
                $("#liveToast").addClass("text-bg-warning")
                $("#liveToast").removeClass("text-bg-danger")
                $("#titlo-alerta").empty()
                $("#titlo-alerta").text("Erro ao criar conta estudante")
                $("#mensagem").empty()
                $("#mensagem").text("Já existe uma conta com este username")
                toastBootstrap.show()
            } else {
                submit = submitValidation;
                $("#liveToast").removeClass("text-bg-danger")
                $("#liveToast").removeClass("text-bg-warning")
                $("#titlo-alerta").empty()
                $("#titlo-alerta").text("MusalaLinkUp")
                $("#mensagem").empty()
                $("#mensagem").text("Os dados da tua conta foram alterados com sucesso. Agora podes iniciar sua sessão.")
                toastBootstrap.show()
                setTimeout(() => {
                    document.formEmpresa.submit()
                }, 5000)
            }
        })
        return submit;
    }


    // Pessoas
    function fazerRequisicaoP() {
        email = document.forms["formUser"]["username"].value;

        url = "http://localhost:8080/usuarioAjax/" + email;
        // Retorna uma Promise que resolve quando a requisição é bem-sucedida
        return new Promise((resolve, reject) => {
            // Faz a requisição usando a função fetch
            fetch(url)
                .then(response => {

                    // Verifica se a resposta da requisição é bem-sucedida (status 2xx)
                    if (!response.ok) {
                        throw new Error('Erro na requisição');
                    }
                    // Parseia a resposta como JSON
                    return response.json();
                })
                .then(data => {
                    // Armazena os dados na variável global
                    dadosGlobais = data;
                    // Resolve a Promise
                    submitValidation = true
                    resolve(submitValidation);
                })
                .catch(error => {
                    // Rejeita a Promise em caso de erro
                    reject(error);
                });
        });
    }


    // Pessoas
    function decodificarP() {
        id = document.forms["formUser"]["id"].value;
        senha = document.forms["formUser"]["senha-antiga"].value;
        url = "http://localhost:8080/usuarioAjax/conta/"+id+"/"+senha;
        // Retorna uma Promise que resolve quando a requisição é bem-sucedida
        return new Promise((resolve, reject) => {
            // Faz a requisição usando a função fetch
            fetch(url)
                .then(response => {

                    // Verifica se a resposta da requisição é bem-sucedida (status 2xx)
                    if (!response.ok) {
                        throw new Error('Erro na requisição');
                    }
                    // Parseia a resposta como JSON
                    return response.json();
                })
                .then(data => {
                    // Armazena os dados na variável global
                    validar = data;
                    // Resolve a Promise
                    submitValidation = true
                    resolve(submitValidation);
                })
                .catch(error => {
                    // Rejeita a Promise em caso de erro
                    reject(error);
                });
        });
    }

    //Empresa
    function validateFormP() {

        const toastLiveExample = document.getElementById('liveToast')
        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        let submit = false;
        fazerRequisicaoP().then((submitValidation) => {

            nome = document.forms["formUser"]["pessoa.nome"].value;
            apelido = document.forms["formUser"]["pessoa.apelido"].value;
            email = document.forms["formUser"]["email"].value;
            endereco = document.forms["formUser"]["pessoa.endereco"].value;
            dataEmissao = document.forms["formUser"]["pessoa.dataEmissao"].value;
            dataValidade = document.forms["formUser"]["pessoa.dataValidade"].value;
            dataNascimento = document.forms["formUser"]["pessoa.dataNascimento"].value;

            bi = document.forms["formUser"]["pessoa.bi"].value;
            contacto = document.forms["formUser"]["contacto"].value;
            username = document.forms["formUser"]["username"].value;

            senha = document.forms["formUser"]["senha"].value;
            senhaConfirmar = document.forms["formUser"]["senha-confirmar"].value;

            // por mexer aqui!!!!!!!!
            if (nome == ""
                || apelido == ""
                || email == ""
                || endereco == ""
                || dataEmissao == ""
                || dataValidade == ""
                || dataNascimento == ""
                || bi == ""
                || contacto == ""
                || username == ""
                || senha == ""
                || senhaConfirmar == ""
                || senha != senhaConfirmar) {

                validNomeP()
                validApelidoP()
                validEmailP()
                validEnderecoP()
                validDataEmissaoP()
                validDataValidadeP()
                validDataNascimentoP()
                validBiP()
                validContactoP()
                validUsernameP()
                validSenhaP()
                validSenhaNovaP()
                validSenhaConfirmarP()

                //confirmar senha

                if (senha != senhaConfirmar) {

                    $("#liveToast").addClass("text-bg-danger")
                    $("#titlo-alerta").empty()
                    $("#titlo-alerta").text("Erro ao criar conta estudante")
                    $("#mensagem").empty()
                    $("#mensagem").text("A senha nova deve ser a mesma no campo confirmar senha")
                    toastBootstrap.show()
                    submit = false;
                }
                setTimeout(() => {
                    const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
                    $("#liveToast").addClass("text-bg-danger")
                    $("#liveToast").removeClass("text-bg-warning")
                    $("#titlo-alerta").empty()
                    $("#titlo-alerta").text("Erro ao criar conta estudante")
                    $("#mensagem").empty()
                    $("#mensagem").text("Verifique os campos se estão vazios ou devidamente preenchidos.")
                    toastBootstrap.show()
                    submit = false;
                }, 4000)
            } else if (dadosGlobais) {
                submit = false
                const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
                $("#liveToast").addClass("text-bg-warning")
                $("#liveToast").removeClass("text-bg-danger")
                $("#titlo-alerta").empty()
                $("#titlo-alerta").text("Erro ao criar conta estudante")
                $("#mensagem").empty()
                $("#mensagem").text("Já existe uma conta com este username")
                toastBootstrap.show()
            } else {
                submit = submitValidation;
                $("#liveToast").removeClass("text-bg-danger")
                $("#liveToast").removeClass("text-bg-warning")
                $("#titlo-alerta").empty()
                $("#titlo-alerta").text("MusalaLinkUp")
                $("#mensagem").empty()
                $("#mensagem").text("Os dados da tua conta foram alterados com sucesso. Agora podes iniciar sua sessão.")
                toastBootstrap.show()
                setTimeout(() => {
                    document.formUser.submit()
                }, 5000)
            }
        })
        return submit;
    }

</script>
</body>
</html>