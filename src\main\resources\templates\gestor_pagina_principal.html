<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">

    <!-- Importação do Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
            crossorigin="anonymous"></script>

    <style th:inline="text">
        body{
            font-family: Poppins;
        }

        ul {list-style-type: none;}
        /* Month header */
        .month {
            padding: 20px 25px;
            width: 100%;
            background: white;
            text-align: center;
        }

        /* Month list */
        .month ul {
            margin: 0;
            padding: 0;
        }

        .month ul li {
            color: black;
            font-size: 20px;
            text-transform: uppercase;
            letter-spacing: 3px;
        }

        /* Previous button inside month header */
        .month .prev {
            float: left;
            padding-top: 10px;
        }

        /* Next button */
        .month .next {
            float: right;
            padding-top: 10px;
        }

        /* Weekdays (Mon-Sun) */
        .weekdays {
            margin: 0;
            padding: 10px 0;
            background-color:#3c8aff;
            box-shadow: 0 0 2px #3c8aff;
            z-index: 5;
        }

        .weekdays li {
            display: inline-block;
            width: 12%;
            color: white;
            text-align: center;
        }

        /* Days (1-31) */
        .days {
            padding: 10px 0;
            background: white;
            margin: 0;
            z-index: 4;
        }

        .days li {
            list-style-type: none;
            display: inline-block;
            width: 12%;
            text-align: center;
            margin-bottom: 5px;
            font-size:12px;
            color: black;
        }

        /* Highlight the "current" day */
        .days li .active {
            padding: 5px;
            background: #3c8aff;
            color: white !important;
            border-radius: 100%;
            box-shadow: 0 0 3px silver;
        }
    </style>

    <title>Title</title>
</head>
<body class="row" style="margin: 0%;background-color: snow">
<!--Menu da App-->
<!--Menu da App-->
<div class="col-xl-2 col-lg-3 col-md-4">
<div class="d-flex flex-column flex-shrink-0 p-3 bg-white shadow-sm position-fixed" style="width: 280px; height: 100vh">
    <a href="/sakidila_ic/portal/" class="d-flex align-items-center mb-3 mb-md-0 me-md-auto link-body-emphasis text-decoration-none">
        <img src="/img/logotipo.jpg" alt="Bootstrap" width="100"> <span class="fw-bold text-dark text-center">Gestão Acadêmica</span></a>
    </a>
    <hr>
    <ul class="nav nav-pills flex-column mb-auto">
        <li class="nav-item">
            <a href="/sakidila_ic/portal/" class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Inicio
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/portal/modulo_gestao" th:if="${dadoConta.tipoConta.designacao == gestorProjectoPerfil}" class="nav-link active" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Dashboard
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/entidade/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == adminPerfil}" class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Entidade
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/entidade_faculdade/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Faculdade
            </a>
        </li>
        <li class="nav-item">
            <a href="/sakidila_ic/portal/projecto/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == empresaPerfil}"
               class="nav-link link-body-emphasis" aria-current="page">
                <svg class="bi pe-none me-2" width="16" height="16"></svg>
                Meus investimentos
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/usuario/gestor_instituicao_registrar"
               th:if="${dadoConta.tipoConta.designacao == adminPerfil}"
               class="nav-link link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#table"></use></svg>
                Gestor de Instituição
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/centro/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link  link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use h:href="/sakidila_ic/centro/registrar"></use></svg>
                Centro de Investigação
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/centro/investigador/associar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil}"
               class="nav-link  link-body-emphasis">
                <svg class="bi pe-none me-2" width="16" height="16"><use h:href="/sakidila_ic/centro/registrar"></use></svg>
                Centro de Investigação
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/portal/projecto/registrar/1"
               th:if="${dadoConta.tipoConta.designacao == gestorCentroPerfil
               or dadoConta.tipoConta.designacao == gestorProjectoPerfil
               or dadoConta.tipoConta.designacao == investigadorPerfil
               or dadoConta.tipoConta.designacao == estudantePerfil}"
               class="nav-link">
                <svg class="bi pe-none me-2" width="16" height="16"><use href="/sakidila_ic/centro/registrar"></use></svg>
                Projectos
            </a>
        </li>
        <li>
            <a href="/sakidila_ic/portal/projecto/pendentes/1"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}"
               class="nav-link">
                <svg class="bi pe-none me-2" width="16" height="16"><use href="/sakidila_ic/centro/registrar"></use></svg>
                Projectos pendentes
            </a>
        </li>
        <li>
            <a href="#" class="nav-link link-body-emphasis"
               th:if="${dadoConta.tipoConta.designacao == gestorInstituicaoPerfil}">
                <svg class="bi pe-none me-2" width="16" height="16"><use xlink:href="#table"></use></svg>
                Estudante
            </a>
        </li>
    </ul>
    <hr>
    <div class="dropdown">
        <a href="#" class="d-flex align-items-center link-body-emphasis text-decoration-none dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
            <img th:if="${conta == userDefault}" th:src="@{/img/user.png}" alt="mdo" width="32" height="32" class="rounded-circle">
            <img th:if="${conta != null}" th:src="${dadoConta.foto}" alt="mdo" width="32" height="32" class="rounded-circle">
            <strong>[[${dadoConta.username}]]</strong>
        </a>
        <ul class="dropdown-menu text-small shadow">
            <li><a th:if="${conta == userDefault}" class="dropdown-item" href="/sakidila_ic/usuario/login">Entrar</a></li>
            <li><a th:if="${conta != null}" class="dropdown-item" href="">Meu Perfil</a></li>
            <li><a th:if="${conta != null}" class="dropdown-item" href="/logout">Sair</a></li>
        </ul>
    </div>
</div>
</div>
<div class="col-xl-10 col-lg-9 col-md-8">

    <div class="container-fluid d-flex flex-row">
        <div class="col-4 d-flex flex-column justify-content-start">
            <div class=" bg-white shadow container-fluid me-4 mt-4 mb-4 rounded-4" style="height: 100%;overflow-y: auto">
                <h6 class="fs-4 fw-bold mt-3 sticky-top">Meus projectos</h6>
                <table class="table" id="projectos">
                    <tr>
                        <th scope="col">Designacao</th>
                        <th scope="col">Gerir</th>
                    </tr>
                    <tr th:if="${dadoConta.tipoConta.designacao == gestorProjectoPerfil}" th:each="item : ${projectos}">
                        <td><div th:text="${item.designacao}"></div></td>
                        <td class="">
                            <input type="hidden" name="projectoId" th:value="${item.id}">
                            <button type="button" class="btn btn-primary btn-sm rounded-pill selecionado">Gerir</button>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="col-8 container">

            <div class="row ms-2  my-4 " style="">
                <div class="col bg-white rounded-4 shadow">
                    <div class="  ">
                        <div class="sticky-top py-2" style="">
                            <h6 class=" fs-4 fw-bold mt-3 sticky-top" id="metodologia">Fases do projecto</h6>
                        </div>
                        <div class="d-flex flex-row" style="overflow-x: auto" id="fases">

                        </div>
                    </div>
                </div>

                <div class=" bg-white shadow container mt-4 rounded-4" style="height: 500px;overflow-y: auto">
                    <div class="">
                        <div class="d-flex flex-row justify-content-between sticky-top">
                            <h6 class=" fs-4 fw-bold mt-3">Tarefas</h6>
                            <div class="d-flex flex-row mt-2">
                                <div class="me-2 "><select class="form-select rounded-4">
                                    <option>Selecione o estado</option>
                                    <option th:each="item : ${estados}" th:text="${item.designacao}" th:value="${item.idEstado}">Em progresso</option>
                                </select></div>
                                <div><input type="text" class="form-control rounded-4" placeholder="Pesquisar..."></div>
                                <div class="ms-2"><button type="button" class="btn btn-success rounded-4">Buscar</button></div>
                            </div>
                        </div>
                    </div>
                    <table class="table" id="tarefas">
                        <tr>
                            <th scope="col" class="col-5">Descrição</th>
                            <th scope="col"> Data inicial - Data Final</th>
                            <th scope="col"> Validação</th>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

    </div>
        <div class="row mx-2">
            <div class="col bg-white shadow rounded-4" style=" height: 245px; overflow-y: auto">
                <div class="sticky-top py-2 ps-2 bg-white">
                    <h6 class=" fs-4 fw-bold mt-3 sticky-top">Membros do projecto</h6>
                </div>

                <div class="row row-cols-2 mx-auto container " id="investigadores">

                </div>
            </div>
        </div>
</div>
<script>
    $(document).on("click", "#projectos button.selecionado", function sms() {
        let tr = $(this).closest('tr');

        idProjecto = tr.find($("[name=projectoId]")).val();
        alert(idProjecto)
        $("#fases").empty()
        $("#metodologia").text("")
        $("#tarefas").text("")
        $("#investigadores").empty()
        getFases(idProjecto)
        getMetodologia(idProjecto)
        getTarefas(idProjecto)
        getInvestigadores(idProjecto)
        })

    function getFases(idProjecto) {
        url = "http://localhost:8080/projectoAjax/" + idProjecto;
        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                fases = $("#fases")
                $.each(responseJson, function (index, metodologia) {
                    div1 = $("<div>")
                    div1.attr("class","d-flex flex-row me-2")
                    div2 = $("<div>")
                    div2
                        .attr("class","d-flex justify-content-end align-items-center my-2 px-2 rounded-pill")
                    .attr("style","background-color: #3c8aff")
                    $("<button>")
                        .attr("class","btn fw-bold text-white")
                        .text(metodologia.designacao)
                        .appendTo(div2);
                    div2.appendTo(div1);
                    div1.appendTo(fases);
                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }

    function getMetodologia(idProjecto) {
        projectoId = $("#projecto").val();
        url = "http://localhost:8080/projectoAjax/metodologia/" + idProjecto;
        $.ajax({method: "GET", url,
            success:  function (data){
                $("#metodologia").text(data.designacao)
                    .val(data.id)
            }});

    }

    function getTarefas(idProjecto) {
        url = "http://localhost:8080/tarefaAjax/" + idProjecto;

        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                tarefas = $("#tarefas")

                $.each(responseJson, function (index, data) {
                    linha = $("<tr>")
                    campo1 = $("<td>")
                    campo2 = $("<td>")
                    campo3 = $("<td>")
                    $("<div>")
                    .text(data.estado.designacao)
                    .appendTo(campo3)
                    $("<div>")
                    .text(data.dataInicio+' até '+data.dataFim)
                    .appendTo(campo2)
                    $("<div>")
                        .text(data.designacao)
                        .appendTo(campo1)
                    campo1.appendTo(linha)
                    campo2.appendTo(linha)
                    campo3.appendTo(linha)
                    linha.appendTo(tarefas)
                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }


    function getInvestigadores(idProjecto) {
        url = "http://localhost:8080/Ajax/projecto/" + idProjecto;

        $.ajax({method: "GET", url})
            .done(function (responseJson) {
                investigadores = $("#investigadores")

                $.each(responseJson, function (index, data) {
                    coluna = $("<div>")
                    coluna.attr("class","col-2 m-2");
                    info = $("<div>")
                    info.attr("class","d-flex flex-column align-items-center m-3 bg-light rounded-4");
                    info.attr("style","width: 150px")
                    imagem = $("<img>")
                    imagem.attr("class","rounded-circle my-2")
                    imagem.attr("src",data.chave.conta.foto).attr("width","70").attr("height","70");
                    imagem.appendTo(info)
                    titlo = $("<h6>")
                    titlo.attr("class","h6 text-center");
                    titlo.text(data.chave.conta.pessoa.nome+' '+data.chave.conta.pessoa.apelido)
                    titlo.appendTo(info)
                    info.appendTo(coluna)
                    coluna.appendTo(investigadores)

                })
            })
            .fail(function () {
            })
            .always(function () {
            });
    }


</script>
</body>
</html>