package com.sakidila.projectoic.controller;

import com.sakidila.projectoic.entity.*;
import com.sakidila.projectoic.service.LocalidadeServico;
import com.sakidila.projectoic.service.ProjectoContaService;
import com.sakidila.projectoic.service.ProjectoServico;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/projectoAjax")
public class ProjectoAjaxController {

    @Autowired
    private ProjectoServico projectoServico;

    @Autowired
    private ProjectoContaService projectoContaService;

    @GetMapping("/{id}")
    public List<Metodologia> encontrarSubfasesPeloIdProjecto(@PathVariable Long id){
        List<Metodologia> lista = new ArrayList<>();
        Projecto projecto = projectoServico.findById(id);
        if(projecto != null) {
            lista = projecto.getMetodologia().getSubFases();
            return lista;
        }
        return null;
    }

    @GetMapping("/metodologia/{id}")
    public ResponseEntity<?> encontrarMetodologia(@PathVariable Long id){
        Projecto projecto = projectoServico.findById(id);
        if(projecto != null) {
            System.out.println(projecto.getMetodologia().getDesignacao());
            return new ResponseEntity<>(projecto.getMetodologia(),HttpStatus.OK);
        }
        return null;
    }

    @GetMapping("/contas/{id}")
    public ResponseEntity<?> encontrarInvestigadoresPeloIdProjecto(@PathVariable Long id){
        List<Conta> investigadores = new ArrayList<>();
        for (ProjectoConta projectoConta: projectoContaService.findAllInvestigadoresByIdProjecto(id)) {
            investigadores.add(projectoConta.getChave().getConta());
        }
        System.out.println(investigadores.get(0).getPessoa().getNome());
        return new ResponseEntity<>(investigadores, HttpStatus.OK);
    }

}
