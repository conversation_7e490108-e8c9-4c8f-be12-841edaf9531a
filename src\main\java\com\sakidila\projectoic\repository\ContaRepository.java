package com.sakidila.projectoic.repository;

import com.sakidila.projectoic.entity.Conta;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
@Repository
@Transactional
public interface ContaRepository extends JpaRepository<Conta,Long> {
    public Optional<Conta> findByEmail(String email);

    @Query("select c from Conta c where c.username = ?1")
    public Optional<Conta> findByNomeUsuario(String nome);
    public Boolean existsByEmail(String email);
    public Conta findByUsername(String username);
    public List<Conta> findAllByEntidadeIdAndTipoContaId(Long idEntidade, Long idTipoConta);

    public Optional<Conta> findContaByCentroIdAndId(Long idCentro, Long idConta);

    @Modifying(clearAutomatically = true)
    @Query("UPDATE Conta c SET c.ehSubscrito=true WHERE c.id =:idConta")
    public void subscribe(@Param("idConta") Long idConta);

    @Modifying
    @Query("update Conta c set c.disabled = true where c.id =:idConta")
    void disabledByIdConta(Long idConta);

    List<Conta> findAllByEntidadeId(Long idEntidade);
}
