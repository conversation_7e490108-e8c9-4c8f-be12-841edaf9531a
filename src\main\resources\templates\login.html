<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">

    <!-- Importação do Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-w76AqPfDkMBDXo30jS1Sgez6pr3x5MlQ1ZAGC+nuZB+EYdgRZgiwxhTBTkF7CXvN"
            crossorigin="anonymous"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style th:inline="text">
        body{
            background-image: url([[@{/img/login.jpg}]]);
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
            background-attachment: fixed;
            font-family: Poppins;
        }


    </style>
    <title>Entre com a tua conta!</title>
</head>
<body>
<nav class="navbar navbar-expand-lg p-0" style="margin: 0%;">
    <button class="navbar-toggler ms-2" type="button" data-bs-toggle="collapse" data-bs-target="#navbarTogglerDemo01" aria-controls="navbarTogglerDemo01" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarTogglerDemo01">
        <div class=" d-flex flex-column bg-white shadow-sm" style="width: 100%">
            <div class="container-fluid">
                <div class="container d-flex flex-row align-items-center">
                    <a class="navbar-brand" href="/sakidila_ic/portal/">
                        <img src="/img/logotipo.jpg" alt="Bootstrap" width="120">
                    </a>

                    <div class="container d-flex flex-row justify-content-end align-items-center">
                        <a href="/sakidila_ic/usuario/principal" class="nav-link">Registrar</a>
                        <div class="dropdown btn">
                            <a href="#" class="d-block link-body-emphasis text-decoration-none dropdown dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <img src="/img/user.png" alt="mdo" width="32" height="32" class="rounded-circle">
                            </a>
                            <ul class="dropdown-menu text-small">
                                <li><a class="dropdown-item" href="/sakidila_ic/usuario/login">Entrar</a></li>
                                <!--<li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#">Sair</a></li>-->
                            </ul>
                        </div>
                        <i class="fs-5 bi bi-facebook me-2" style="color: #7c6aa2"></i>
                        <i class="fs-5 bi bi-instagram me-2" style="color: #7c6aa2"></i>
                        <i class="fs-5 bi bi-whatsapp" style="color: #7c6aa2"></i>
                    </div>
                </div>

            </div>
            <div class="container-fluid py-1" style="background-color: #AB81A3">
                <ul class="container navbar-nav d-flex justify-content-end ">
                    <li class="nav-item">
                        <a class="nav-link text-white active px-3" aria-current="page" href="/sakidila_ic/portal/">Portal</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link text-white  px-3" data-bs-toggle="dropdown" aria-expanded="false" href="#">Instituições</a>
                        <ul class="dropdown-menu rounded-top-0 rounded-bottom-4 border-top-0 text-white" style="background-color: #AB81A3">
                            <li><a class="dropdown-item text-white" href="#"><i class="fst-normal fw-bold">UCAN </i>Universidade Católica de Angola</a></li>
                            <li><a class="dropdown-item text-white" href="#"><i class="fst-normal fw-bold">UAN </i>Universidade Agostinho Neto</a></li>
                            <li><a class="dropdown-item text-white" href="#"><i class="fst-normal fw-bold">UMA </i>Universidade Metódista de Angola</a></li>
                            <li><a class="dropdown-item text-white" href="#"><i class="fst-normal fw-bold">UTANGA </i>Universidade Técnica de Angola</a></li>
                            <li><a class="dropdown-item text-white" href="#"><i class="fst-normal fw-bold">ISPITEC </i>Instituto Politecnico de Inovação Tecnologica</a></li>
                            <li><a class="dropdown-item text-white" href="#">Ver mais...</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white px-3" href="/sakidila_ic/portal/listagem/1">Projectos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white px-3" href="#">Sobre</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white ps-3 me-3" href="#">Contacte-nos</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</nav>
<div class="container mx-auto ">
    <div class="row mx-auto" style="width: 90%; margin-top: 5%">
        <div class="col-7  rounded-start-5 shadow me-1" style="background-color: rgba(171,129,163,0.8);font-family: 'Poppins Light'">
            <h5 class="display-6 mt-5 mb-1 ms-3 fw-bold" style="color: white">Conta <i class="fw-bold text-dark">Sakidila!</i></h5>
            <p style="text-align: justify; text-indent: 45px" class="px-3 text-white">Bem-vindo à nossa emocionante plataforma de divulgação de projetos! Aqui, você não apenas descobre ideias inovadoras, mas também tem a oportunidade de compartilhar e promover os seus próprios projetos. O processo de registro e login é a porta de entrada para um mundo repleto de possibilidades e colaborações incríveis.</p>
            <p style="text-align: justify; text-indent: 45px" class="px-3 text-white">Ao clicar no botão de registro, você está prestes a entrar em uma comunidade vibrante de criatividade e inovação. O registro é fácil, rápido e abre as portas para um universo de oportunidades. Imagine-se conectando com mentes brilhantes, compartilhando suas paixões e ganhando visibilidade para o seu projeto único. </p>
            <p style="text-align: justify; text-indent: 45px" class="px-3 text-white">Após o registro, o login é a chave para desbloquear todas as funcionalidades da nossa plataforma. Ao fazer login, você entra em um ambiente digital exclusivo, onde suas interações, descobertas e conquistas são personalizadas para atender às suas necessidades.</p>
        </div>
        <div class="col-4 shadow rounded-end-5" style="background-color: rgba(255,255,255,0.8)">
            <div class="d-flex flex-column align-items-center">
                <img src="/img/user.png" class="mt-5" width="100" height="100">
            </div>
            <h5 class="display-6 fs-2 text-center">Iniciar a sessão</h5>
            <form method="post" th:action="@{/sakidila_ic/usuario/login}" name="myForm" onsubmit="return validateForm()" required class="px-4">
                <div class="form-floating mt-4 mb-2">
                    <input class="form-control border-top-0 border-start-0 border-end-0 border-2 rounded-0" name="username" style="border-color: #AB81A3" id="usernameValidate" onfocusout="validUsuario()" type="text" placeholder="<EMAIL>">
                    <label class="label">Usuario/E-mail</label>
                    <div class="valid-feedback">

                    </div>
                    <div class="invalid-feedback">
                        Preencher campo com a senha.
                    </div>
                </div>
                <div class="form-floating mt-3 mb-2">
                    <input class="form-control border-top-0 border-start-0 border-end-0 border-2 rounded-0" name="senha" style="border-color: #AB81A3" id="senhaValidate" onfocusout="validSenha()" type="password" placeholder="<EMAIL>">
                    <label class="label" for="senha">Senha</label>
                    <div class="valid-feedback">

                    </div>
                    <div class="invalid-feedback">
                        Preencher campo com a senha.
                    </div>
                </div>
                <div class="d-flex flex-row align-items-center">
                    <input  type="checkbox" style="width: 15px; height: 15px; margin-right: 5px" id="lembrar">
                    <label for="lembrar" >Lembrar a sua senha</label>
                </div>
                <div>
                    <a href="#" style="font-size: smaller;text-decoration: none">Esqueceu sua senha?</a>
                </div>
                <div style="width: 100%" class="d-flex flex-column align-items-center">
                    <button type="submit" class="btn mt-3 px-4 rounded-4 border-0 text-white shadow-sm" style="background-color: #AB81A3">Entrar</button>
                    <a href="/sakidila_ic/usuario/principal" class="mb-5" style="font-size: smaller;text-decoration: none">Registra-te agora!</a>
                </div>

            </form>

        </div>
    </div>
</div>

<div class=" container-fluid bg-white text-center shadow mt-5 fixed-bottom" >
    <div class="container d-flex flex-row justify-content-between pt-3">
        <div class="menu-rodape">
            <ul class="nav">
                <li class="nav-item m-0"><a class="nav-link"  style="color: #7c6aa2" href="/sakidila_ic/portal/">Portal</a></li>
                <li class="nav-item m-0"><a class="nav-link active"  style="color: #AB81A3" href="">Iniciar Sessão</a></li>
                <li class="nav-item m-0"><a class="nav-link"  style="color: #7c6aa2" href="/sakidila_ic/usuario/principal">Registrar</a></li>
            </ul>
        </div>
        <div class="d-flex flex-row align-items-center">
            <i class="fs-7 me-2 text-muted" style="font-style: normal">Media Social</i>
            <i class="fs-7 bi bi-facebook me-2" style="color: #7c6aa2"></i>
            <i class="fs-7 bi bi-instagram me-2" style="color: #7c6aa2"></i>
            <i class="fs-7 bi bi-whatsapp" style="color: #7c6aa2"></i>
        </div>
    </div>
    <small class="text-muted" style="font-size: 12px">Software desenvolvido pela Sakidila©, todos direitos reservados.</small>
</div>
<div class="toast-container position-fixed bottom-0 end-0 p-3 "  data-bs-autohide="false">
    <div id="liveToast" class="toast  border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto" id="titlo-alerta">MusalaLinkUp</strong>
            <small class="text-body-secondary">Agora</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="mensagem">
            Se já tens uma conta inicie a sua sessão, caso contrario cria uma conta agora!
        </div>
    </div>
</div>
<script>
    window.onload = (event) => {
        const toastLiveExample = document.getElementById('liveToast')
        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        setTimeout(() => {
            toastBootstrap.show()
        },1000)
    }

    function validUsuario(){
        info = document.forms["myForm"]["username"].value;
        component = $("#usernameValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    function validSenha(){
        info = document.forms["myForm"]["senha"].value;
        component = $("#senhaValidate")
        if(info != "" ) {
            component.addClass("is-valid")
            component.removeClass("is-invalid")
        }
        else {
            component.addClass("is-invalid")
            component.removeClass("is-valid")
        }
    }

    var dadosGlobais;

    function fazerRequisicao() {
        username = document.forms["myForm"]["username"].value;

        url = "http://localhost:8080/usuarioAjax/conta/"+username;
        // Retorna uma Promise que resolve quando a requisição é bem-sucedida
        return new Promise((resolve, reject) => {
            // Faz a requisição usando a função fetch
            fetch(url)
                .then(response => {

                    // Verifica se a resposta da requisição é bem-sucedida (status 2xx)
                    if (!response.ok) {
                        throw new Error('Erro na requisição');
                    }
                    // Parseia a resposta como JSON
                    return response.json();
                })
                .then(data => {
                    // Armazena os dados na variável global
                    dadosGlobais = data;
                    // Resolve a Promise
                    submitValidation = true
                    resolve(submitValidation);
                })
                .catch(error => {
                    // Rejeita a Promise em caso de erro
                    reject(error);
                });
        });
    }

    function validateForm() {
        const toastLiveExample = document.getElementById('liveToast')
        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
        let submit = false;
        fazerRequisicao().then((submitValidation) => {
            username = document.forms["myForm"]["username"].value;
            senha = document.forms["myForm"]["senha"].value;
            if (username == "" || senha == "") {

                validSenha()
                validUsuario()

                //confirmar senha

               /* if (senha != dadosGlobais.senha) {

                    $("#liveToast").addClass("text-bg-danger")
                    $("#titlo-alerta").empty()
                    $("#titlo-alerta").text("Erro ao iniciar sessão")
                    $("#mensagem").empty()
                    $("#mensagem").text("A senha inserida está errada, verique atentamente!")
                    toastBootstrap.show()
                    submit = false;
                }*/
                        const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
                        $("#liveToast").addClass("text-bg-danger")
                        $("#liveToast").removeClass("text-bg-warning")
                        $("#titlo-alerta").empty()
                        $("#titlo-alerta").text("Erro ao iniciar a sessão")
                        $("#mensagem").empty()
                        $("#mensagem").text("Verique se os campos da conta estão vazios!")
                        toastBootstrap.show()
                        submit = false;
            } else if(dadosGlobais.senha != null){
                submit = submitValidation;
                $("#liveToast").removeClass("text-bg-danger")
                $("#liveToast").removeClass("text-bg-warning")
                $("#titlo-alerta").empty()
                $("#titlo-alerta").text("MusalaLinkUp")
                $("#mensagem").empty()
                $("#mensagem").text("Sessão iniciada com sucesso! Aguarde...")
                toastBootstrap.show()
                setTimeout(() => {
                    document.myForm.submit()
                }, 5000)
            }
        })
        .catch(reason => {
            username = document.forms["myForm"]["username"].value;
            senha = document.forms["myForm"]["senha"].value;
            const toastLiveExample = document.getElementById('liveToast')
            const toastBootstrap = bootstrap.Toast.getOrCreateInstance(toastLiveExample)
            if (username == ""
                || senha == "") {

                validSenha()
                validUsuario()
                        $("#liveToast").addClass("text-bg-danger")
                        $("#liveToast").removeClass("text-bg-warning")
                        $("#titlo-alerta").empty()
                        $("#titlo-alerta").text("Erro ao iniciar a sessão")
                        $("#mensagem").empty()
                        $("#mensagem").text("Verique se os campos da conta estão vazios!")
                        toastBootstrap.show()
                        submit = false;

            }
            else{
                $("#liveToast").addClass("text-bg-danger")
                $("#liveToast").removeClass("text-bg-warning")
                $("#titlo-alerta").empty()
                $("#titlo-alerta").text("Erro ao iniciar a sessão")
                $("#mensagem").empty()
                $("#mensagem").text("Senha ou palavra passe incorreta")
                toastBootstrap.show()
                submit = false;

            }
        })
        return submit;
    }
</script>
</body>
</html>