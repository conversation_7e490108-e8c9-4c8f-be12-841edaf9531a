package com.sakidila.projectoic.controller;

import com.sakidila.projectoic.entity.Conta;
import com.sakidila.projectoic.entity.Entidade;
import com.sakidila.projectoic.entity.Projecto;
import com.sakidila.projectoic.entity.Tarefa;
import com.sakidila.projectoic.repository.EstadoRepository;
import com.sakidila.projectoic.service.*;
import com.sakidila.projectoic.utility.Nomenclatura;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Controller
@RequestMapping("/sakidila_ic/portal/projecto/tarefa")
public class TarefaController {

    @Autowired
    private TarefaService tarefaService;

    @Autowired
    private ContaService contaService;

    @Autowired
    private MetodologiaService metodologiaService;

    @Autowired
    private ProjectoServico projectoServico;

    @Autowired
    private EstadoRepository estadoRepository;

    @Autowired
            private InvestidorProjectoService investidorProjectoService;

    Nomenclatura nomenclatura = new Nomenclatura();

    @PostMapping("/criar")
            public String salvar(@ModelAttribute Tarefa tarefa,
                         HttpSession session){

        tarefa.setInvestigador(contaService.findById(tarefa.getInvestigador().getId()));
        tarefa.setFase(metodologiaService.findById(tarefa.getFase().getId()));
        tarefa.setProjecto(projectoServico.findById(tarefa.getProjecto().getId()));
        if(tarefa.getDataInicio()
                .compareTo(LocalDate.now()) >= 0
                && tarefa.getDataFim()
                .compareTo(LocalDate.now()) >= 1)
            tarefa.setEstado(estadoRepository.findById(Long.parseLong("3")).get());
        if(tarefa.getDataInicio().compareTo(LocalDate.now()) <= 0
                && tarefa.getDataFim().compareTo(LocalDate.now()) >= 0)
            tarefa.setEstado(estadoRepository.findById(Long.parseLong("2")).get());
        tarefaService.salvar(tarefa);
        if (tarefa != null)
            session.setAttribute("msg", "Usuario " + tarefa.getDesignacao() + " criado com sucesso!");
        else
            session.setAttribute("msg", "O E-mail " + tarefa.getDesignacao() + " já existe no sistema");
        return "redirect:/sakidila_ic/portal/projecto/tarefa/"+tarefa.getProjecto().getId()+"/1";
    }

    @GetMapping("/{id}/{pagina}")
    public String findTarefasByProjecto(@PathVariable Long id, Model model,@PathVariable int pagina){
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication.isAuthenticated() && !authentication.getName().equals("anonymousUser")) {
            int tamanho = 10;
            System.out.println("ID Projecto "+id);
            Projecto projecto = projectoServico.findById(id);
            Conta conta = contaService.mostrarDadoConta(authentication.getName());
            List<Tarefa> tarefas = tarefaService.encontrarTarefasPeloIdProjecto(id);
            tarefas = conta.getId() == projecto.getGestor().getId() ? tarefas : tarefaService.encontrarTarefasPeloIdInvestigador(conta.getId(),tarefas);
            if(conta.getTipoConta().getId() == nomenclatura.getEMPRESA())
                tarefas = tarefaService.encontrarTarefasPeloIdProjecto(id);
            Page<Tarefa> paginaNova = null;
            paginaNova = (Page<Tarefa>) tarefaService.convertListToPage(pagina,tamanho,tarefas);
            model.addAttribute("paginaActual",pagina);
            model.addAttribute("totalPaginas",paginaNova.getTotalPages());
            model.addAttribute("totalItems",paginaNova.getTotalElements());
            model.addAttribute("conta", authentication.getName());
            model.addAttribute("userDefault", "anonymousUser");
            model.addAttribute("dadoConta", conta);
            model.addAttribute("tarefas",paginaNova.getContent());
            model.addAttribute("projecto",projecto);
            model.addAttribute("idProjecto",id);
            model.addAttribute("empresaPerfil", "ROLE_EMPRESA");
            model.addAttribute("gestorInstituicaoPerfil", "ROLE_GESTOR_INSTITUICAO");
            model.addAttribute("gestorCentroPerfil", "ROLE_RESPONSAVEL_CENTRO");
            model.addAttribute("investigadorIndependentePerfil","ROLE_INVESTIGADOR_INDEPENDENTE");
            model.addAttribute("gestorProjectoPerfil", "ROLE_GESTOR_PROJECTO");
            model.addAttribute("investigadorPerfil", "ROLE_INVESTIGATOR");
            model.addAttribute("adminPerfil", "ROLE_ADMIN");
            return "tarefas";
        }else{
            SecurityContextHolder.clearContext();
            model.addAttribute("conta",null);
            model.addAttribute("dadoConta",null);
            return "login";
        }
    }

    @GetMapping("/delete/{id}")
    public String deletar(@PathVariable Long id){
        Tarefa tarefa = tarefaService.deletar(id);
        return "redirect:/sakidila_ic/portal/projecto/tarefa/"+tarefa.getProjecto().getId()+"/1";

    }

    @GetMapping("/concluido/{id}/{idEstado}")
    public String validarConcluido(@PathVariable Long id,@PathVariable Long idEstado){
        Tarefa tarefa = tarefaService.validar(id,idEstado);
        return "redirect:/sakidila_ic/portal/projecto/tarefa/"+tarefa.getProjecto().getId()+"/1";

    }
}