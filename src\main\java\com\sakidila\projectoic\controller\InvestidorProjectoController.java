package com.sakidila.projectoic.controller;

import com.sakidila.projectoic.entity.InvestidorProjecto;
import com.sakidila.projectoic.service.InvestidorProjectoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Controller
@RequestMapping("/sakidila_ic/investidor/")
public class InvestidorProjectoController {

    @Autowired
    private InvestidorProjectoService service;

    @PostMapping("/salvar")
    public String salvar(@ModelAttribute InvestidorProjecto investidorProjecto){
         InvestidorProjecto resultado = service.criarInvestidorProjecto(investidorProjecto);
         return "redirect:/sakidila_ic/portal/projecto/registrar/1";
    }

    @GetMapping("/delete/{idProjecto}/{idInvestidor}")
    public String deletar(@PathVariable Long idProjecto, @PathVariable Long idInvestidor){
        service.deletar(idProjecto, idInvestidor);
        return "redirect:/sakidila_ic/portal/projecto/registrar/1";

    }

    @GetMapping("/aprovar/{idProjecto}/{idInvestidor}")
    public String aprovar(@PathVariable Long idProjecto, @PathVariable Long idInvestidor){
        service.aprovar(idProjecto, idInvestidor);
        return "redirect:/sakidila_ic/portal/projecto/registrar/1";

    }

}
