package com.sakidila.projectoic.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
public class Curso {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String designacao;

    @JsonIgnore
    @ManyToOne
    @JoinColumn(name = "fk_faculdade")
    @Nullable
    private Faculdade faculdade;

}
