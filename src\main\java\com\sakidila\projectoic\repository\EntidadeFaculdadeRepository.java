package com.sakidila.projectoic.repository;

import com.sakidila.projectoic.entity.EntidadeFaculdade;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
@Transactional
public interface EntidadeFaculdadeRepository extends JpaRepository<EntidadeFaculdade,Long> {
  @Query("select ep from EntidadeFaculdade ep where ep.id.instituto.id = ?1")
  List<EntidadeFaculdade> encontrarFaculdadesPeloIdInstituto(Long idInstituto);

  @Query("select ef FROM EntidadeFaculdade ef where ef.id.faculdade.id =:idFaculdade and ef.id.instituto.id =:idInstituto")
  Optional<EntidadeFaculdade> findByIdFaculdade(Long idFaculdade,Long idInstituto);

  @Modifying
  @Query("update EntidadeFaculdade ef set ef.disabled = true where ef.id.faculdade.id =:idFaculdade and ef.id.instituto.id =:idInstituto")
  void disabledByIdFaculdade(Long idFaculdade,Long idInstituto);

  Page<EntidadeFaculdade> findAllByIdInstitutoId(Long idInstituto, Pageable pageable);
}
